# 期货交易会员解析
import sys
sys.path.append("/app/宁波银行POC")
from util_ai import ChatBot
from utils import fn_to_markdown, process_file_to_base64, markdown_json_to_dict, cal_fn_md5, fn_to_markdown_v2
import os
import json
import tqdm
import datetime
import inspect
import re
from ocr_api import get_ocr_table_info
from util_img_process import base64_to_img

# 本代码不采用pdf转扫描式pdf的前置步骤
fn = "/app/宁波银行POC/大模型样例/POC脱敏材料/赎回确认（脱敏）/华润信托·润钱宝尊享2号集合资金信托计划_2024-10-21_润天利集合_2019-2141_20241018_计划赎回明细表.pdf_4014474_e95b13a7-c842-4654-82ec-d5bfc35687f3.png"
markdown_content, imgs_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B', add_ocr_info=False)

base64_to_img(imgs_list[1])

chatbot = ChatBot(
    system_prompt="""你是一名期货开户文件解析专家。请严格区分“会员号”（固定 4 位数字）和“交易编码”（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填“/”）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为“投机”）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填“/”）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填“/”）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所、上海交易所
- 大商所＝大连商品交易所、大连交易所
- 郑商所＝郑州商品交易所、郑州交易所
- 中金所＝中国金融期货交易所、金融交易所
- 上能所＝上海能源交易所、能源中心、能源所
- 广期所＝广州期货交易所、广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认“投机”。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
    """
    )

response = chatbot.chat(markdown_content)
print(response)


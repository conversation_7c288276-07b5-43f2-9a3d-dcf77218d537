{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# 非标交易确认单文档解析\n", "\n", "## 功能概述\n", "通过大模型对客户发送的非标交易确认单进行解析，支持多种文档格式：\n", "- PDF文件\n", "- Excel文件(.xlsx, .xls)\n", "- 图片文件(.jpg, .png等)\n", "- Word文档(.docx)\n", "- 邮件文件(.eml)\n", "\n", "## 解析内容\n", "1. **投资者名称**：通常指代客户姓名，一般是资管计划的名称\n", "2. **投资者账号**：通常指代客户的资金账号\n", "3. **业务日期**：对应某一笔交易的日期\n", "4. **业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. **投资标的名称**：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. **投资标的代码**：投资标的的代码，多为数字和字母的组合，也可能为空\n", "7. **投资标的金额**：实际交易的确认金额\n", "8. **投资标的数量**：文档中可能用份额来描述\n", "9. **交易费用**：一般申购、赎回、买入、卖出交易中，会标明交易费用，没有则可以为空\n", "\n", "## 支持的文件格式详情\n", "- **PDF文件**: 使用OCR和VLM技术解析\n", "- **Excel文件**: \n", "  - .xlsx (Excel 2007及以上版本)\n", "  - .xls (Excel 97-2003版本)\n", "- **图片文件**: .jpg, .jpeg, .png, .bmp, .gif, .tiff, .webp\n", "- **Word文档**: .docx格式\n", "- **邮件文件**: .eml格式，提取邮件头信息和正文内容"]}, {"cell_type": "code", "execution_count": 43, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ tabulate 已安装\n", "✓ openpyxl 已安装\n", "✓ xlrd 已安装\n", "✓ python-docx 已安装\n", "✓ PyPDF2 已安装\n", "✓ Pillow 已安装\n", "\n", "所有依赖库都已安装完成!\n", "\n", "当前工作目录: d:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\n", "目录内容: ['output', '券商账户计息变更.ipynb', '券商账户计息变更V1.1.ipynb', '券商账户计息变更V1.3.ipynb', '券商账户计息变更V1.4.ipynb']...\n"]}], "source": ["import sys\n", "sys.path.append(\"..\")  # 添加上级目录到路径\n", "import os\n", "import json\n", "import pandas as pd\n", "from datetime import datetime\n", "from utils import fn_to_markdown_v2\n", "from util_ai import ChatBot\n", "import re\n", "import subprocess\n", "\n", "# 检查和安装必要的依赖库\n", "def check_and_install_dependencies():\n", "    \"\"\"检查并安装必要的依赖库\"\"\"\n", "    required_packages = {\n", "        'tabulate': 'tabulate',\n", "        'openpyxl': 'openpyxl',\n", "        'xlrd': 'xlrd',\n", "        'python-docx': 'docx',\n", "        'PyPDF2': 'PyPDF2',\n", "        'Pillow': 'PIL'\n", "    }\n", "    \n", "    missing_packages = []\n", "    \n", "    for package_name, import_name in required_packages.items():\n", "        try:\n", "            if import_name == 'docx':\n", "                import docx\n", "            elif import_name == 'PIL':\n", "                from PIL import Image\n", "            else:\n", "                __import__(import_name)\n", "            print(f\"✓ {package_name} 已安装\")\n", "        except ImportError:\n", "            print(f\"✗ {package_name} 未安装\")\n", "            missing_packages.append(package_name)\n", "    \n", "    if missing_packages:\n", "        print(f\"\\n需要安装以下包: {', '.join(missing_packages)}\")\n", "        print(\"正在自动安装...\")\n", "        \n", "        for package in missing_packages:\n", "            try:\n", "                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])\n", "                print(f\"✓ {package} 安装成功\")\n", "            except subprocess.CalledProcessError as e:\n", "                print(f\"✗ {package} 安装失败: {e}\")\n", "                print(f\"请手动运行: pip install {package}\")\n", "    else:\n", "        print(\"\\n所有依赖库都已安装完成!\")\n", "\n", "# 运行依赖检查\n", "check_and_install_dependencies()\n", "\n", "# 导入自定义模块\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from ocr_api import trans_pdf_to_markdown\n", "\n", "# 导入文档处理相关库\n", "import docx\n", "from openpyxl import load_workbook\n", "import xlrd\n", "import PyPDF2\n", "from PIL import Image\n", "import email\n", "import re\n", "\n", "print(f\"\\n当前工作目录: {os.getcwd()}\")\n", "print(f\"目录内容: {os.listdir('.')[:5]}...\")  # 只显示前5个项目\n"]}, {"cell_type": "code", "execution_count": 44, "id": "chatbot_init", "metadata": {}, "outputs": [], "source": ["# 初始化聊天机器人（已优化数字精度处理）\n", "chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 投资者名称：通常指代客户姓名，一般是资管计划的名称\n", "2. 投资者账号：通常指代客户的资金账号\n", "3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填\"/\"）\n", "4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填\"/\"）\n", "7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "9. 交易费用：一般申购、赎回、买入、卖出交易中，会标明交易费用（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "\n", "=====================\n", "【重要注意事项】\n", "- 所有数字字段（投资标的金额、投资标的数量、交易费用）必须保持原始精度，不要进行四舍五入\n", "- 如果原始数据是31006.55，输出应该是\"31006.55\"，不要变成\"31007\"或\"31006\"\n", "- 如果原始数据是1386040，输出应该是\"1386040\"，不要变成\"1.38604e+06\"\n", "-\n", "\n", "=====================\n", "【业务类型映射】\n", "- 分红：分红派息、现金分红、股息分红等\n", "- 红利转投：红利再投资、分红转投等\n", "- 买入：买入、购买等\n", "- 卖出：卖出、赎回卖出等\n", "- 认购：认购、新基金认购等\n", "- 申购：申购、基金申购等\n", "- 赎回：赎回、基金赎回等\n", "\n", "=====================\n", "【输出JSON格式】\n", "如果文档包含多笔交易，请返回数组格式。确保数字字段保持原始精度，不要四舍五入。单笔交易示例：\n", "```json\n", "{\n", "  \"投资者名称\": \"某某资产管理计划\",\n", "  \"投资者账号\": \"123456789\",\n", "  \"业务日期\": \"2024-01-15\",\n", "  \"业务类型\": \"申购\",\n", "  \"投资标的名称\": \"某某货币基金\",\n", "  \"投资标的代码\": \"000001\",\n", "  \"投资标的金额\": \"1000000.00\",\n", "  \"投资标的数量\": \"1000000.00\",\n", "  \"交易费用\": \"0.00\"\n", "}\n", "```\n", "\n", "多笔交易示例：\n", "```json\n", "[\n", "  {\n", "    \"投资者名称\": \"某某资产管理计划\",\n", "    \"投资者账号\": \"123456789\",\n", "    \"业务日期\": \"2024-01-15\",\n", "    \"业务类型\": \"申购\",\n", "    \"投资标的名称\": \"某某货币基金A\",\n", "    \"投资标的代码\": \"000001\",\n", "    \"投资标的金额\": \"1000000.00\",\n", "    \"投资标的数量\": \"1000000.00\",\n", "    \"交易费用\": \"0.00\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"某某资产管理计划\",\n", "    \"投资者账号\": \"123456789\",\n", "    \"业务日期\": \"2024-01-16\",\n", "    \"业务类型\": \"赎回\",\n", "    \"投资标的名称\": \"某某货币基金B\",\n", "    \"投资标的代码\": \"000002\",\n", "    \"投资标的金额\": \"500000.00\",\n", "    \"投资标的数量\": \"500000.00\",\n", "    \"交易费用\": \"25.00\"\n", "  }\n", "]\n", "```\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 46, "id": "file_processor", "metadata": {}, "outputs": [], "source": ["def process_file_to_markdown(file_path):\n", "    \"\"\"\n", "    将不同格式的文件转换为markdown格式\n", "    支持：PDF、Excel、图片、Word文档、邮件文件\n", "    \"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.pdf':\n", "            # 使用本地OCR/VLM处理PDF\n", "            markdown_content, _ = fn_to_markdown_v2(file_path, convert_to_scanned=False, ai_seal=False)\n", "            return markdown_content\n", "\n", "        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:\n", "            # 使用本地OCR/VLM处理图片\n", "            markdown_content, _ = fn_to_markdown_v2(file_path, convert_to_scanned=True, ai_seal=False)\n", "            return markdown_content\n", "\n", "        elif file_ext in ['.xlsx', '.xls']:\n", "            # 处理Excel文件（支持新旧格式）\n", "            return process_excel_file(file_path)\n", "            \n", "        elif file_ext == '.docx':\n", "            # 处理Word文档\n", "            return process_docx_file(file_path)\n", "            \n", "        elif file_ext == '.eml':\n", "            # 处理邮件文件\n", "            return process_eml_file(file_path)\n", "            \n", "        else:\n", "            raise ValueError(f\"不支持的文件格式: {file_ext}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_excel_file(file_path):\n", "    \"\"\"\n", "    处理Excel文件，转换为markdown格式\n", "    支持.xlsx和.xls格式\n", "    \"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.xls':\n", "            # 处理老版Excel文件(.xls)\n", "            return process_xls_file(file_path)\n", "        \n", "        # 处理新版Excel文件(.xlsx) - 默认处理\n", "        # 读取Excel文件\n", "        workbook = load_workbook(file_path, data_only=True)\n", "        markdown_content = \"\"\n", "        \n", "        for sheet_name in workbook.sheetnames:\n", "            worksheet = workbook[sheet_name]\n", "            markdown_content += f\"\\n## {sheet_name}\\n\\n\"\n", "            \n", "            # 转换为DataFrame然后转为markdown表格\n", "            data = []\n", "            for row in worksheet.iter_rows(values_only=True):\n", "                if any(cell is not None for cell in row):\n", "                    data.append([str(cell) if cell is not None else \"\" for cell in row])\n", "            \n", "            if data:\n", "                df = pd.DataFrame(data[1:], columns=data[0] if data else [])\n", "                # 保持数字精度，不使用科学计数法，保留足够的小数位\n", "                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + \"\\n\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Excel文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_xls_file(file_path):\n", "    \"\"\"\n", "    处理.xls文件（老版Excel格式）\n", "    \"\"\"\n", "    try:\n", "        # 读取老版Excel文件\n", "        workbook = xlrd.open_workbook(file_path)\n", "        markdown_content = \"\"\n", "        \n", "        for sheet_index in range(workbook.nsheets):\n", "            worksheet = workbook.sheet_by_index(sheet_index)\n", "            sheet_name = workbook.sheet_names()[sheet_index]\n", "            markdown_content += f\"\\n## {sheet_name}\\n\\n\"\n", "            \n", "            # 转换为DataFrame然后转为markdown表格\n", "            data = []\n", "            for row_idx in range(worksheet.nrows):\n", "                row_data = []\n", "                for col_idx in range(worksheet.ncols):\n", "                    cell_value = worksheet.cell_value(row_idx, col_idx)\n", "                    # 处理不同类型的单元格值，保持数字精度\n", "                    if isinstance(cell_value, float):\n", "                        # 保持浮点数的原始精度，不进行四舍五入\n", "                        if cell_value.is_integer():\n", "                            # 只有当浮点数确实是整数时才转换为int\n", "                            cell_value = int(cell_value)\n", "                        else:\n", "                            # 保持原始浮点数精度，使用高精度字符串格式化\n", "                            # 使用.10g格式保留10位有效数字，避免四舍五入\n", "                            cell_value = f\"{cell_value:.10g}\"\n", "                    row_data.append(str(cell_value) if cell_value != '' else \"\")\n", "                \n", "                if any(cell != \"\" for cell in row_data):\n", "                    data.append(row_data)\n", "            \n", "            if data:\n", "                df = pd.DataFrame(data[1:], columns=data[0] if data else [])\n", "                # 保持数字精度，不使用科学计数法，保留足够的小数位\n", "                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + \"\\n\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\".xls文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_docx_file(file_path):\n", "    \"\"\"\n", "    处理Word文档，转换为markdown格式\n", "    \"\"\"\n", "    try:\n", "        doc = docx.Document(file_path)\n", "        markdown_content = \"\"\n", "        \n", "        # 处理段落\n", "        for paragraph in doc.paragraphs:\n", "            if paragraph.text.strip():\n", "                markdown_content += paragraph.text + \"\\n\"\n", "        \n", "        # 处理表格\n", "        for table in doc.tables:\n", "            markdown_content += \"\\n\"\n", "            for i, row in enumerate(table.rows):\n", "                row_data = []\n", "                for cell in row.cells:\n", "                    row_data.append(cell.text.strip())\n", "                \n", "                if i == 0:\n", "                    # 表头\n", "                    markdown_content += \"| \" + \" | \".join(row_data) + \" |\\n\"\n", "                    markdown_content += \"| \" + \" | \".join([\"---\"] * len(row_data)) + \" |\\n\"\n", "                else:\n", "                    # 数据行\n", "                    markdown_content += \"| \" + \" | \".join(row_data) + \" |\\n\"\n", "            markdown_content += \"\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Word文档处理错误: {e}\")\n", "        return None\n", "\n", "def process_eml_file(file_path):\n", "    \"\"\"\n", "    处理.eml文件（邮件格式），转换为markdown格式\n", "    \"\"\"\n", "    try:\n", "        # 读取邮件文件\n", "        with open(file_path, 'rb') as f:\n", "            msg = email.message_from_bytes(f.read())\n", "        \n", "        markdown_content = \"\"\n", "        \n", "        # 提取邮件头信息\n", "        markdown_content += f\"# 邮件信息\\n\\n\"\n", "        markdown_content += f\"**发件人**: {msg.get('From', 'N/A')}\\n\"\n", "        markdown_content += f\"**收件人**: {msg.get('To', 'N/A')}\\n\"\n", "        markdown_content += f\"**主题**: {msg.get('Subject', 'N/A')}\\n\"\n", "        markdown_content += f\"**日期**: {msg.get('Date', 'N/A')}\\n\\n\"\n", "        \n", "        # 提取邮件正文\n", "        markdown_content += f\"## 邮件正文\\n\\n\"\n", "        \n", "        if msg.is_multipart():\n", "            # 多部分邮件\n", "            for part in msg.walk():\n", "                content_type = part.get_content_type()\n", "                content_disposition = str(part.get(\"Content-Disposition\"))\n", "                \n", "                # 只处理正文，不处理附件\n", "                if content_type == \"text/plain\" and \"attachment\" not in content_disposition:\n", "                    try:\n", "                        body = part.get_payload(decode=True).decode('utf-8', errors='ignore')\n", "                        markdown_content += body + \"\\n\\n\"\n", "                    except:\n", "                        try:\n", "                            body = part.get_payload(decode=True).decode('gbk', errors='ignore')\n", "                            markdown_content += body + \"\\n\\n\"\n", "                        except:\n", "                            markdown_content += \"[无法解码的文本内容]\\n\\n\"\n", "                            \n", "                elif content_type == \"text/html\" and \"attachment\" not in content_disposition:\n", "                    try:\n", "                        html_body = part.get_payload(decode=True).decode('utf-8', errors='ignore')\n", "                    except:\n", "                        try:\n", "                            html_body = part.get_payload(decode=True).decode('gbk', errors='ignore')\n", "                        except:\n", "                            html_body = \"[无法解码的HTML内容]\"\n", "                    \n", "                    # 简单的HTML标签清理\n", "                    clean_text = re.sub('<[^<]+?>', '', html_body)\n", "                    # 清理多余的空白字符\n", "                    clean_text = re.sub(r'\\s+', ' ', clean_text).strip()\n", "                    markdown_content += clean_text + \"\\n\\n\"\n", "        else:\n", "            # 单部分邮件\n", "            try:\n", "                body = msg.get_payload(decode=True).decode('utf-8', errors='ignore')\n", "                markdown_content += body + \"\\n\\n\"\n", "            except:\n", "                try:\n", "                    body = msg.get_payload(decode=True).decode('gbk', errors='ignore')\n", "                    markdown_content += body + \"\\n\\n\"\n", "                except:\n", "                    markdown_content += \"[无法解码的邮件内容]\\n\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\".eml文件处理错误: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 47, "id": "30050809", "metadata": {}, "outputs": [], "source": ["def parse_transaction_document(file_path, save_result=True, output_dir=\"./output\"):\n", "    \"\"\"\n", "    解析非标交易确认单文档\n", "    \n", "    Args:\n", "        file_path: 文档文件路径\n", "        save_result: 是否保存解析结果\n", "        output_dir: 输出目录\n", "    \n", "    Returns:\n", "        dict: 解析结果\n", "    \"\"\"\n", "    print(f\"开始处理文件: {file_path}\")\n", "    \n", "    # 检查文件是否存在\n", "    if not os.path.exists(file_path):\n", "        print(f\"错误: 文件不存在 - {file_path}\")\n", "        return None\n", "    \n", "    # 转换文件为markdown格式\n", "    print(\"正在转换文件为markdown格式...\")\n", "    markdown_content = process_file_to_markdown(file_path)\n", "    \n", "    if not markdown_content:\n", "        print(\"错误: 无法转换文件为markdown格式\")\n", "        return None\n", "    \n", "    print(f\"文件转换完成，内容长度: {len(markdown_content)} 字符\")\n", "    \n", "    # 使用大模型解析内容\n", "    print(\"正在使用大模型解析交易信息...\")\n", "    try:\n", "        response = chatbot.chat(markdown_content)\n", "        print(\"大模型解析完成\")\n", "        \n", "        # 尝试解析JSON响应\n", "        # 移除可能的markdown代码块标记\n", "        json_str = response.strip()\n", "        if json_str.startswith('```json'):\n", "            json_str = json_str[7:]\n", "        if json_str.endswith('```'):\n", "            json_str = json_str[:-3]\n", "        json_str = json_str.strip()\n", "        \n", "        parsed_result = json.loads(json_str)\n", "        \n", "        # 保存结果\n", "        if save_result:\n", "            os.makedirs(output_dir, exist_ok=True)\n", "            \n", "            # 生成输出文件名\n", "            base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            \n", "            # 保存原始markdown内容\n", "            markdown_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_markdown.md\")\n", "            with open(markdown_file, 'w', encoding='utf-8') as f:\n", "                f.write(markdown_content)\n", "            \n", "            # 保存解析结果\n", "            result_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_result.json\")\n", "            with open(result_file, 'w', encoding='utf-8') as f:\n", "                json.dump(parsed_result, f, ensure_ascii=False, indent=2)\n", "            \n", "            print(f\"结果已保存到: {result_file}\")\n", "            print(f\"Markdown内容已保存到: {markdown_file}\")\n", "        \n", "        return {\n", "            'success': True,\n", "            'file_path': file_path,\n", "            'markdown_content': markdown_content,\n", "            'parsed_result': parsed_result,\n", "            'raw_response': response\n", "        }\n", "        \n", "    except json.JSONDecodeError as e:\n", "        print(f\"JSON解析错误: {e}\")\n", "        print(f\"原始响应: {response}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': f'JSON解析错误: {e}',\n", "            'raw_response': response\n", "        }\n", "    except Exception as e:\n", "        print(f\"解析过程中发生错误: {e}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": 48, "id": "batch_processor", "metadata": {}, "outputs": [], "source": ["def batch_process_documents(input_dir, output_dir=\"./output\", supported_extensions=None, recursive=True, batch_size=10):\n", "    \"\"\"\n", "    批量处理文档（支持递归处理子目录）\n", "    优化版本：每处理指定数量的文件就生成一个中间汇总JSON，最后生成完整汇总\n", "    \n", "    Args:\n", "        input_dir: 输入目录\n", "        output_dir: 输出目录\n", "        supported_extensions: 支持的文件扩展名列表\n", "        recursive: 是否递归处理子目录\n", "        batch_size: 每批处理的文件数量，默认10个文件生成一个中间汇总\n", "    \n", "    Returns:\n", "        dict: 处理结果字典，包含详细信息和汇总数据\n", "    \"\"\"\n", "    if supported_extensions is None:\n", "        supported_extensions = ['.pdf', '.xlsx', '.xls', '.jpg', '.jpeg', '.png', '.docx', '.bmp', '.gif', '.tiff', '.webp', '.eml']\n", "    \n", "    results = []\n", "    all_transactions = []  # 汇总所有交易数据\n", "    batch_summaries = []  # 存储中间批次汇总文件路径\n", "    current_batch_transactions = []  # 当前批次的交易数据\n", "    \n", "    if not os.path.exists(input_dir):\n", "        print(f\"错误: 输入目录不存在 - {input_dir}\")\n", "        return {'success': False, 'error': f'目录不存在: {input_dir}'}\n", "    \n", "    # 获取所有支持的文件（递归或非递归）\n", "    files_to_process = []\n", "    if recursive:\n", "        # 递归遍历所有子目录\n", "        for root, dirs, files in os.walk(input_dir):\n", "            for filename in files:\n", "                file_ext = os.path.splitext(filename)[1].lower()\n", "                if file_ext in supported_extensions:\n", "                    files_to_process.append(os.path.join(root, filename))\n", "    else:\n", "        # 只处理当前目录\n", "        for filename in os.listdir(input_dir):\n", "            if os.path.isfile(os.path.join(input_dir, filename)):\n", "                file_ext = os.path.splitext(filename)[1].lower()\n", "                if file_ext in supported_extensions:\n", "                    files_to_process.append(os.path.join(input_dir, filename))\n", "    \n", "    print(f\"找到 {len(files_to_process)} 个文件需要处理\")\n", "    print(f\"将每 {batch_size} 个文件生成一个中间汇总JSON\")\n", "    \n", "    # 创建输出目录和中间汇总目录\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    batch_summaries_dir = os.path.join(output_dir, \"batch_summaries\")\n", "    os.makedirs(batch_summaries_dir, exist_ok=True)\n", "    \n", "    # 生成时间戳（用于所有文件命名）\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 逐个处理文件\n", "    for i, file_path in enumerate(files_to_process, 1):\n", "        print(f\"\\n处理第 {i}/{len(files_to_process)} 个文件: {os.path.basename(file_path)}\")\n", "        \n", "        try:\n", "            result = parse_transaction_document(file_path, save_result=True, output_dir=output_dir)\n", "            \n", "            # 添加文件相对路径信息\n", "            if result:\n", "                result['relative_path'] = os.path.relpath(file_path, input_dir)\n", "                result['file_name'] = os.path.basename(file_path)\n", "            \n", "            results.append(result)\n", "            \n", "            if result and result.get('success'):\n", "                print(f\"✓ 处理成功\")\n", "                \n", "                # 收集交易数据用于汇总\n", "                parsed_data = result.get('parsed_result')\n", "                if parsed_data:\n", "                    if isinstance(parsed_data, list):\n", "                        for transaction in parsed_data:\n", "                            transaction['源文件'] = os.path.basename(file_path)\n", "                            transaction['文件路径'] = result['relative_path']\n", "                            all_transactions.append(transaction)\n", "                    else:\n", "                        parsed_data['源文件'] = os.path.basename(file_path)\n", "                        parsed_data['文件路径'] = result['relative_path']\n", "                        all_transactions.append(parsed_data)\n", "            else:\n", "                print(f\"✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "                \n", "        except Exception as e:\n", "            print(f\"✗ 处理异常: {e}\")\n", "            results.append({\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'file_path': file_path,\n", "                'relative_path': os.path.relpath(file_path, input_dir),\n", "                'file_name': os.path.basename(file_path),\n", "                'error': str(e)\n", "            })\n", "    \n", "    # 生成统计信息\n", "    successful_count = sum(1 for r in results if r and r.get('success'))\n", "    failed_count = len(files_to_process) - successful_count\n", "    \n", "    print(f\"\\n批量处理完成: {successful_count}/{len(files_to_process)} 个文件处理成功\")\n", "    print(f\"成功解析的交易记录总数: {len(all_transactions)}\")\n", "    \n", "    # 生成时间戳\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 保存详细批量处理报告\n", "    report_file = os.path.join(output_dir, f\"batch_report_{timestamp}.json\")\n", "    batch_report = {\n", "        'timestamp': timestamp,\n", "        'input_dir': input_dir,\n", "        'output_dir': output_dir,\n", "        'total_files': len(files_to_process),\n", "        'successful_files': successful_count,\n", "        'failed_files': failed_count,\n", "        'total_transactions': len(all_transactions),\n", "        'processing_details': results\n", "    }\n", "    \n", "    with open(report_file, 'w', encoding='utf-8') as f:\n", "        json.dump(batch_report, f, ensure_ascii=False, indent=2)\n", "    \n", "    # 保存交易数据汇总\n", "    summary_file = os.path.join(output_dir, f\"transactions_summary_{timestamp}.json\")\n", "    with open(summary_file, 'w', encoding='utf-8') as f:\n", "        json.dump(all_transactions, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"批量处理报告已保存到: {report_file}\")\n", "    print(f\"交易数据汇总已保存到: {summary_file}\")\n", "    \n", "    return {\n", "        'success': True,\n", "        'timestamp': timestamp,\n", "        'report_file': report_file,\n", "        'summary_file': summary_file,\n", "        'batch_report': batch_report,\n", "        'all_transactions': all_transactions,\n", "        'results': results\n", "    }\n", "\n", "def compare_batch_results(summary_file1, summary_file2, output_dir=\"./output\"):\n", "    \"\"\"\n", "    对比两次批量处理的结果\n", "    \n", "    Args:\n", "        summary_file1: 第一次处理的汇总文件路径\n", "        summary_file2: 第二次处理的汇总文件路径\n", "        output_dir: 输出目录\n", "    \n", "    Returns:\n", "        dict: 对比结果\n", "    \"\"\"\n", "    try:\n", "        # 读取两个汇总文件\n", "        with open(summary_file1, 'r', encoding='utf-8') as f:\n", "            data1 = json.load(f)\n", "        \n", "        with open(summary_file2, 'r', encoding='utf-8') as f:\n", "            data2 = json.load(f)\n", "        \n", "        # 创建文件名到交易数据的映射\n", "        def create_file_mapping(data):\n", "            mapping = {}\n", "            for transaction in data:\n", "                file_name = transaction.get('源文件', 'unknown')\n", "                if file_name not in mapping:\n", "                    mapping[file_name] = []\n", "                mapping[file_name].append(transaction)\n", "            return mapping\n", "        \n", "        mapping1 = create_file_mapping(data1)\n", "        mapping2 = create_file_mapping(data2)\n", "        \n", "        # 对比分析\n", "        all_files = set(mapping1.keys()) | set(mapping2.keys())\n", "        \n", "        comparison_result = {\n", "            'summary': {\n", "                'first_batch': {\n", "                    'file': summary_file1,\n", "                    'total_transactions': len(data1),\n", "                    'total_files': len(mapping1)\n", "                },\n", "                'second_batch': {\n", "                    'file': summary_file2,\n", "                    'total_transactions': len(data2),\n", "                    'total_files': len(mapping2)\n", "                },\n", "                'differences': {\n", "                    'transaction_count_diff': len(data2) - len(data1),\n", "                    'file_count_diff': len(mapping2) - len(mapping1)\n", "                }\n", "            },\n", "            'file_level_comparison': {},\n", "            'new_files': [],\n", "            'removed_files': [],\n", "            'modified_files': []\n", "        }\n", "        \n", "        # 文件级别对比\n", "        for file_name in all_files:\n", "            transactions1 = mapping1.get(file_name, [])\n", "            transactions2 = mapping2.get(file_name, [])\n", "            \n", "            if not transactions1 and transactions2:\n", "                # 新增文件\n", "                comparison_result['new_files'].append({\n", "                    'file_name': file_name,\n", "                    'transaction_count': len(transactions2)\n", "                })\n", "            elif transactions1 and not transactions2:\n", "                # 删除文件\n", "                comparison_result['removed_files'].append({\n", "                    'file_name': file_name,\n", "                    'transaction_count': len(transactions1)\n", "                })\n", "            elif transactions1 and transactions2:\n", "                # 对比交易数据\n", "                if len(transactions1) != len(transactions2):\n", "                    comparison_result['modified_files'].append({\n", "                        'file_name': file_name,\n", "                        'first_batch_count': len(transactions1),\n", "                        'second_batch_count': len(transactions2),\n", "                        'difference': len(transactions2) - len(transactions1)\n", "                    })\n", "                \n", "                # 详细对比（简化版，只对比数量和基本信息）\n", "                comparison_result['file_level_comparison'][file_name] = {\n", "                    'first_batch_transactions': len(transactions1),\n", "                    'second_batch_transactions': len(transactions2),\n", "                    'difference': len(transactions2) - len(transactions1)\n", "                }\n", "        \n", "        # 保存对比结果\n", "        timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "        comparison_file = os.path.join(output_dir, f\"batch_comparison_{timestamp}.json\")\n", "        \n", "        with open(comparison_file, 'w', encoding='utf-8') as f:\n", "            json.dump(comparison_result, f, ensure_ascii=False, indent=2)\n", "        \n", "        print(f\"\\n=== 批量处理结果对比 ===\")\n", "        print(f\"第一次处理: {len(data1)} 笔交易，{len(mapping1)} 个文件\")\n", "        print(f\"第二次处理: {len(data2)} 笔交易，{len(mapping2)} 个文件\")\n", "        print(f\"交易数量变化: {len(data2) - len(data1):+d}\")\n", "        print(f\"文件数量变化: {len(mapping2) - len(mapping1):+d}\")\n", "        \n", "        if comparison_result['new_files']:\n", "            print(f\"\\n新增文件 ({len(comparison_result['new_files'])} 个):\")\n", "            for file_info in comparison_result['new_files'][:5]:  # 只显示前5个\n", "                print(f\"  + {file_info['file_name']} ({file_info['transaction_count']} 笔交易)\")\n", "            if len(comparison_result['new_files']) > 5:\n", "                print(f\"  ... 还有 {len(comparison_result['new_files']) - 5} 个新增文件\")\n", "        \n", "        if comparison_result['removed_files']:\n", "            print(f\"\\n删除文件 ({len(comparison_result['removed_files'])} 个):\")\n", "            for file_info in comparison_result['removed_files'][:5]:\n", "                print(f\"  - {file_info['file_name']} ({file_info['transaction_count']} 笔交易)\")\n", "            if len(comparison_result['removed_files']) > 5:\n", "                print(f\"  ... 还有 {len(comparison_result['removed_files']) - 5} 个删除文件\")\n", "        \n", "        if comparison_result['modified_files']:\n", "            print(f\"\\n修改文件 ({len(comparison_result['modified_files'])} 个):\")\n", "            for file_info in comparison_result['modified_files'][:5]:\n", "                print(f\"  ~ {file_info['file_name']} ({file_info['first_batch_count']} -> {file_info['second_batch_count']})\")\n", "            if len(comparison_result['modified_files']) > 5:\n", "                print(f\"  ... 还有 {len(comparison_result['modified_files']) - 5} 个修改文件\")\n", "        \n", "        print(f\"\\n对比结果已保存到: {comparison_file}\")\n", "        \n", "        return {\n", "            'success': True,\n", "            'comparison_file': comparison_file,\n", "            'comparison_result': comparison_result\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"对比过程中发生错误: {e}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": 7, "id": "b763b54f", "metadata": {}, "outputs": [], "source": ["## 使用示例\n", "\n", "### 1. 单个文件处理示例"]}, {"cell_type": "code", "execution_count": 49, "id": "4f5fbba2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: d:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\n", "尝试文件路径: ../大模型样例/POC脱敏材料/非标分红（脱敏）/春风41分红情况.xlsx\n", "文件是否存在: True\n", "\n", "开始解析文档...\n", "开始处理文件: ../大模型样例/POC脱敏材料/非标分红（脱敏）/春风41分红情况.xlsx\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 2713 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: ./output\\春风41分红情况_20250731_152425_result.json\n", "Markdown内容已保存到: ./output\\春风41分红情况_20250731_152425_markdown.md\n", "\n", "解析成功!\n", "解析结果:\n", "[\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010008026\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列天天金1号人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"12472553.63\",\n", "    \"投资标的数量\": \"329961736.1\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010007993\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列天天金2号人民币理财产品（安享款）\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"46184353.62\",\n", "    \"投资标的数量\": \"1221808297\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010008086\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫享利22001期（最低持有30天之月月鑫）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"24967676.97\",\n", "    \"投资标的数量\": \"660520554.8\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010008244\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫享利23001期（最低持有7天之周周鑫）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"41268025.67\",\n", "    \"投资标的数量\": \"1091746711\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010011389\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫享利23002期（最低持有60天之双月鑫）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1472411.5\",\n", "    \"投资标的数量\": \"38952685.06\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010070279\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利20016期（6个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"300040.96\",\n", "    \"投资标的数量\": \"7937591.42\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010068284\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利20056期（6个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1819805.57\",\n", "    \"投资标的数量\": \"48143004.57\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010000822\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21031期（3个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"5096525.74\",\n", "    \"投资标的数量\": \"134828723.2\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010080988\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21059期（3个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1778382.82\",\n", "    \"投资标的数量\": \"47047164.46\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010000922\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21282期（3个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"5701.87\",\n", "    \"投资标的数量\": \"150843.13\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010007188\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21321期人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"40344.03\",\n", "    \"投资标的数量\": \"1067302.33\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010080300\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21357期（3个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"150992.32\",\n", "    \"投资标的数量\": \"3994505.81\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010001154\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21393期（3个月）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"3357392.22\",\n", "    \"投资标的数量\": \"88819899.88\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010007299\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行“鑫利”系列鑫增利21405期人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1957035.18\",\n", "    \"投资标的数量\": \"51773417.33\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010011825\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行鑫安利封闭式24024期（安享低波）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"3039964.48\",\n", "    \"投资标的数量\": \"80422340.64\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010012149\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行鑫安利封闭式24030期（安享低波）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1001201.26\",\n", "    \"投资标的数量\": \"26486805.83\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010012253\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行鑫安利封闭式24036期（乡村振兴特别款）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"1022526.07\",\n", "    \"投资标的数量\": \"27050954.16\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010012371\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行鑫安利封闭式24037期（劳动节特别款）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"219141.2\",\n", "    \"投资标的数量\": \"5797386.26\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"上海农—商业银行股份有限公司\",\n", "    \"投资者账号\": \"ZS010012878\",\n", "    \"业务日期\": \"/\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"上海农商银行鑫安利封闭式24046期（安享低波）人民币理财产品\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"922402.98\",\n", "    \"投资标的数量\": \"24402195.22\",\n", "    \"交易费用\": \"/\"\n", "  }\n", "]\n"]}], "source": ["import os,sys,json\n", "# 示例1: 处理PDF文件 - 使用正确的相对路径\n", "pdf_file_path = \"../大模型样例/POC脱敏材料/非标分红（脱敏）/春风41分红情况.xlsx\"\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"尝试文件路径: {pdf_file_path}\")\n", "print(f\"文件是否存在: {os.path.exists(pdf_file_path)}\")\n", "\n", "# 如果文件存在，则处理\n", "if os.path.exists(pdf_file_path):\n", "    print(\"\\n开始解析文档...\")\n", "    result = parse_transaction_document(pdf_file_path)\n", "    \n", "    if result and result['success']:\n", "        print(\"\\n解析成功!\")\n", "        print(\"解析结果:\")\n", "        print(json.dumps(result['parsed_result'], ensure_ascii=False, indent=2))\n", "    else:\n", "        print(f\"\\n解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "else:\n", "    print(f\"\\n文件不存在: {pdf_file_path}\")\n", "    print(\"请检查文件路径是否正确\")\n", "    print(\"提示: 从ipynb目录运行时，需要使用 '../大模型样例/...' 路径\")"]}, {"cell_type": "code", "execution_count": 21, "id": "fd03ef6a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["目标目录: ../大模型样例/非标分类/分类\n", "输出目录: check/test\n", "目录是否存在: True\n", "\n", "开始批量处理目录: ../大模型样例/非标分类/分类\n", "这将处理所有子目录中的支持文件格式...\n", "开始批量处理目录: ../大模型样例/非标分类/分类\n", "输出目录: check/test\n", "递归处理: True\n", "找到 174 个文件需要处理\n", "文件分布:\n", "  原始文件非标资料收集: 58 个文件\n", "  认购申购确认: 67 个文件\n", "  赎回确认: 29 个文件\n", "  非标买入: 1 个文件\n", "  非标分红: 14 个文件\n", "  非标卖出: 3 个文件\n", "  非标红利转投: 2 个文件\n", "\n", "处理第 1/174 个文件: 业务确认单-国通信托-锦鳞2号20241113.pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\业务确认单-国通信托-锦鳞2号20241113.pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 1161 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\业务确认单-国通信托-锦鳞2号20241113_20250730_103142_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\业务确认单-国通信托-锦鳞2号20241113_20250730_103142_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 2/174 个文件: 华润信托_平安宁丰纯债3M5号集合资金信托计划申购如意52号(ZS0292)确认书202410300014312001(1).pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\华润信托_平安宁丰纯债3M5号集合资金信托计划申购如意52号(ZS0292)确认书202410300014312001(1).pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 899 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\华润信托_平安宁丰纯债3M5号集合资金信托计划申购如意52号(ZS0292)确认书202410300014312001(1)_20250730_103149_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\华润信托_平安宁丰纯债3M5号集合资金信托计划申购如意52号(ZS0292)确认书202410300014312001(1)_20250730_103149_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 3/174 个文件: 华润信托宁禧12号-资金对账单_含OTC模板-52600006843_20241112.xlsx\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\华润信托宁禧12号-资金对账单_含OTC模板-52600006843_20241112.xlsx\n", "正在转换文件为markdown格式...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["d:\\anaconda\\envs\\ocr\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py:237: UserWarning: Workbook contains no default style, apply openpyxl's default\n", "  warn(\"Workbook contains no default style, apply openpyxl's default\")\n"]}, {"name": "stdout", "output_type": "stream", "text": ["文件转换完成，内容长度: 10442 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\华润信托宁禧12号-资金对账单_含OTC模板-52600006843_20241112_20250730_103200_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\华润信托宁禧12号-资金对账单_含OTC模板-52600006843_20241112_20250730_103200_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 4/174 个文件: 国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 804 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14_20250730_103205_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14_20250730_103205_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 5/174 个文件: 收益凭证兴尚自动赎回49号兑付公告-20241111-SBA031.pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\收益凭证兴尚自动赎回49号兑付公告-20241111-SBA031.pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 268 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\收益凭证兴尚自动赎回49号兑付公告-20241111-SBA031_20250730_103210_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\收益凭证兴尚自动赎回49号兑付公告-20241111-SBA031_20250730_103210_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 6/174 个文件: 银河证券收益凭证结算报告-CF0319【20241113】.docx\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\兰晶晶\\银河证券收益凭证结算报告-CF0319【20241113】.docx\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 736 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\银河证券收益凭证结算报告-CF0319【20241113】_20250730_103216_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\银河证券收益凭证结算报告-CF0319【20241113】_20250730_103216_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 7/174 个文件: 0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-周周盈-申购-确认单.pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\包丽莉\\非标\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-周周盈-申购-确认单.pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 862 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-周周盈-申购-确认单_20250730_103223_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-周周盈-申购-确认单_20250730_103223_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 8/174 个文件: 0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-民汇20号-申购-确认单.pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\包丽莉\\非标\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-民汇20号-申购-确认单.pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 462 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: check/test\\原始文件非标资料收集\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-民汇20号-申购-确认单_20250730_103229_result.json\n", "Markdown内容已保存到: check/test\\原始文件非标资料收集\\0000000006730-嘉银红船-双季盈开放式净值型理财产品（182天）-民汇20号-申购-确认单_20250730_103229_markdown.md\n", "  ✓ 处理成功，输出到: check/test\\原始文件非标资料收集\n", "\n", "处理第 9/174 个文件: 20241107_外贸信托-鑫安1号集合资金信托计划_中国对外经济贸易信托有限公司（代“外贸信托-中金鑫安197号集合资金信托计划”）_认购_份....pdf\n", "  来源目录: 原始文件非标资料收集\n", "开始处理文件: ../大模型样例/非标分类/分类\\原始文件非标资料收集\\包丽莉\\非标\\20241107_外贸信托-鑫安1号集合资金信托计划_中国对外经济贸易信托有限公司（代“外贸信托-中金鑫安197号集合资金信托计划”）_认购_份....pdf\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 607 字符\n", "正在使用大模型解析交易信息...\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mKeyboardInterrupt\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 293\u001b[39m\n\u001b[32m    290\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m这将处理所有子目录中的支持文件格式...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    292\u001b[39m \u001b[38;5;66;03m# 执行批量处理（按目录结构组织输出）\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m293\u001b[39m batch_result = batch_process_documents_with_structure(\n\u001b[32m    294\u001b[39m     input_dir=input_directory, \n\u001b[32m    295\u001b[39m     output_dir=output_directory,\n\u001b[32m    296\u001b[39m     recursive=\u001b[38;5;28;01mTrue\u001b[39;00m,  \u001b[38;5;66;03m# 递归处理子目录\u001b[39;00m\n\u001b[32m    297\u001b[39m     batch_size=\u001b[32m10\u001b[39m    \u001b[38;5;66;03m# 每10个文件生成一个中间汇总\u001b[39;00m\n\u001b[32m    298\u001b[39m )\n\u001b[32m    300\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m batch_result.get(\u001b[33m'\u001b[39m\u001b[33msuccess\u001b[39m\u001b[33m'\u001b[39m):\n\u001b[32m    301\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m=== 批量处理完成 ===\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 108\u001b[39m, in \u001b[36mbatch_process_documents_with_structure\u001b[39m\u001b[34m(input_dir, output_dir, supported_extensions, recursive, batch_size)\u001b[39m\n\u001b[32m    105\u001b[39m os.makedirs(file_output_dir, exist_ok=\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[32m    107\u001b[39m \u001b[38;5;66;03m# 处理文件\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m108\u001b[39m result = parse_transaction_document(file_path, save_result=\u001b[38;5;28;01mTrue\u001b[39;00m, output_dir=file_output_dir)\n\u001b[32m    110\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m result:\n\u001b[32m    111\u001b[39m     \u001b[38;5;66;03m# 添加文件信息\u001b[39;00m\n\u001b[32m    112\u001b[39m     result[\u001b[33m'\u001b[39m\u001b[33msource_file\u001b[39m\u001b[33m'\u001b[39m] = file_path\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[19]\u001b[39m\u001b[32m, line 33\u001b[39m, in \u001b[36mparse_transaction_document\u001b[39m\u001b[34m(file_path, save_result, output_dir)\u001b[39m\n\u001b[32m     31\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m正在使用大模型解析交易信息...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m33\u001b[39m     response = chatbot.chat(markdown_content)\n\u001b[32m     34\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33m大模型解析完成\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     36\u001b[39m     \u001b[38;5;66;03m# 尝试解析JSON响应\u001b[39;00m\n\u001b[32m     37\u001b[39m     \u001b[38;5;66;03m# 移除可能的markdown代码块标记\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\\..\\util_ai.py:36\u001b[39m, in \u001b[36mChatBot.chat\u001b[39m\u001b[34m(self, messages, temperature, max_tokens, top_p)\u001b[39m\n\u001b[32m     34\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m     35\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mmessages must be a string or a list of dict\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m36\u001b[39m response = \u001b[38;5;28mself\u001b[39m.client.chat.completions.create(\n\u001b[32m     37\u001b[39m     model=\u001b[38;5;28mself\u001b[39m.model,\n\u001b[32m     38\u001b[39m     messages=\u001b[38;5;28mself\u001b[39m.history + messages,\n\u001b[32m     39\u001b[39m     temperature=temperature,\n\u001b[32m     40\u001b[39m     max_tokens=max_tokens,\n\u001b[32m     41\u001b[39m     top_p=top_p)\n\u001b[32m     42\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m response.choices[\u001b[32m0\u001b[39m].message.content\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\openai\\_utils\\_utils.py:287\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    285\u001b[39m             msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mMissing required argument: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquote(missing[\u001b[32m0\u001b[39m])\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    286\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m287\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m func(*args, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py:1087\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m   1044\u001b[39m \u001b[38;5;129m@required_args\u001b[39m([\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m], [\u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m])\n\u001b[32m   1045\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mcreate\u001b[39m(\n\u001b[32m   1046\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1084\u001b[39m     timeout: \u001b[38;5;28mfloat\u001b[39m | httpx.Timeout | \u001b[38;5;28;01mNone\u001b[39;00m | NotGiven = NOT_GIVEN,\n\u001b[32m   1085\u001b[39m ) -> ChatCompletion | Stream[ChatCompletionChunk]:\n\u001b[32m   1086\u001b[39m     validate_response_format(response_format)\n\u001b[32m-> \u001b[39m\u001b[32m1087\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._post(\n\u001b[32m   1088\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m/chat/completions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m   1089\u001b[39m         body=maybe_transform(\n\u001b[32m   1090\u001b[39m             {\n\u001b[32m   1091\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: messages,\n\u001b[32m   1092\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m: model,\n\u001b[32m   1093\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33maudio\u001b[39m\u001b[33m\"\u001b[39m: audio,\n\u001b[32m   1094\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfrequency_penalty\u001b[39m\u001b[33m\"\u001b[39m: frequency_penalty,\n\u001b[32m   1095\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfunction_call\u001b[39m\u001b[33m\"\u001b[39m: function_call,\n\u001b[32m   1096\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mfunctions\u001b[39m\u001b[33m\"\u001b[39m: functions,\n\u001b[32m   1097\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mlogit_bias\u001b[39m\u001b[33m\"\u001b[39m: logit_bias,\n\u001b[32m   1098\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mlogprobs\u001b[39m\u001b[33m\"\u001b[39m: logprobs,\n\u001b[32m   1099\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmax_completion_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_completion_tokens,\n\u001b[32m   1100\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmax_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_tokens,\n\u001b[32m   1101\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmetadata\u001b[39m\u001b[33m\"\u001b[39m: metadata,\n\u001b[32m   1102\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mmodalities\u001b[39m\u001b[33m\"\u001b[39m: modalities,\n\u001b[32m   1103\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mn\u001b[39m\u001b[33m\"\u001b[39m: n,\n\u001b[32m   1104\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mparallel_tool_calls\u001b[39m\u001b[33m\"\u001b[39m: parallel_tool_calls,\n\u001b[32m   1105\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mprediction\u001b[39m\u001b[33m\"\u001b[39m: prediction,\n\u001b[32m   1106\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mpresence_penalty\u001b[39m\u001b[33m\"\u001b[39m: presence_penalty,\n\u001b[32m   1107\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mreasoning_effort\u001b[39m\u001b[33m\"\u001b[39m: reasoning_effort,\n\u001b[32m   1108\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mresponse_format\u001b[39m\u001b[33m\"\u001b[39m: response_format,\n\u001b[32m   1109\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mseed\u001b[39m\u001b[33m\"\u001b[39m: seed,\n\u001b[32m   1110\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mservice_tier\u001b[39m\u001b[33m\"\u001b[39m: service_tier,\n\u001b[32m   1111\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstop\u001b[39m\u001b[33m\"\u001b[39m: stop,\n\u001b[32m   1112\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstore\u001b[39m\u001b[33m\"\u001b[39m: store,\n\u001b[32m   1113\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m: stream,\n\u001b[32m   1114\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mstream_options\u001b[39m\u001b[33m\"\u001b[39m: stream_options,\n\u001b[32m   1115\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtemperature\u001b[39m\u001b[33m\"\u001b[39m: temperature,\n\u001b[32m   1116\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtool_choice\u001b[39m\u001b[33m\"\u001b[39m: tool_choice,\n\u001b[32m   1117\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtools\u001b[39m\u001b[33m\"\u001b[39m: tools,\n\u001b[32m   1118\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtop_logprobs\u001b[39m\u001b[33m\"\u001b[39m: top_logprobs,\n\u001b[32m   1119\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mtop_p\u001b[39m\u001b[33m\"\u001b[39m: top_p,\n\u001b[32m   1120\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33muser\u001b[39m\u001b[33m\"\u001b[39m: user,\n\u001b[32m   1121\u001b[39m                 \u001b[33m\"\u001b[39m\u001b[33mweb_search_options\u001b[39m\u001b[33m\"\u001b[39m: web_search_options,\n\u001b[32m   1122\u001b[39m             },\n\u001b[32m   1123\u001b[39m             completion_create_params.CompletionCreateParamsStreaming\n\u001b[32m   1124\u001b[39m             \u001b[38;5;28;01mif\u001b[39;00m stream\n\u001b[32m   1125\u001b[39m             \u001b[38;5;28;01melse\u001b[39;00m completion_create_params.CompletionCreateParamsNonStreaming,\n\u001b[32m   1126\u001b[39m         ),\n\u001b[32m   1127\u001b[39m         options=make_request_options(\n\u001b[32m   1128\u001b[39m             extra_headers=extra_headers, extra_query=extra_query, extra_body=extra_body, timeout=timeout\n\u001b[32m   1129\u001b[39m         ),\n\u001b[32m   1130\u001b[39m         cast_to=ChatCompletion,\n\u001b[32m   1131\u001b[39m         stream=stream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m   1132\u001b[39m         stream_cls=Stream[ChatCompletionChunk],\n\u001b[32m   1133\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\openai\\_base_client.py:1249\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1235\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mpost\u001b[39m(\n\u001b[32m   1236\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m   1237\u001b[39m     path: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1244\u001b[39m     stream_cls: \u001b[38;5;28mtype\u001b[39m[_StreamT] | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m   1245\u001b[39m ) -> ResponseT | _StreamT:\n\u001b[32m   1246\u001b[39m     opts = FinalRequestOptions.construct(\n\u001b[32m   1247\u001b[39m         method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1248\u001b[39m     )\n\u001b[32m-> \u001b[39m\u001b[32m1249\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\openai\\_base_client.py:972\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m    970\u001b[39m response = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    971\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m972\u001b[39m     response = \u001b[38;5;28mself\u001b[39m._client.send(\n\u001b[32m    973\u001b[39m         request,\n\u001b[32m    974\u001b[39m         stream=stream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._should_stream_response_body(request=request),\n\u001b[32m    975\u001b[39m         **kwargs,\n\u001b[32m    976\u001b[39m     )\n\u001b[32m    977\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m httpx.TimeoutException \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[32m    978\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mEncountered httpx.TimeoutException\u001b[39m\u001b[33m\"\u001b[39m, exc_info=\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpx\\_client.py:914\u001b[39m, in \u001b[36mClient.send\u001b[39m\u001b[34m(self, request, stream, auth, follow_redirects)\u001b[39m\n\u001b[32m    910\u001b[39m \u001b[38;5;28mself\u001b[39m._set_timeout(request)\n\u001b[32m    912\u001b[39m auth = \u001b[38;5;28mself\u001b[39m._build_request_auth(request, auth)\n\u001b[32m--> \u001b[39m\u001b[32m914\u001b[39m response = \u001b[38;5;28mself\u001b[39m._send_handling_auth(\n\u001b[32m    915\u001b[39m     request,\n\u001b[32m    916\u001b[39m     auth=auth,\n\u001b[32m    917\u001b[39m     follow_redirects=follow_redirects,\n\u001b[32m    918\u001b[39m     history=[],\n\u001b[32m    919\u001b[39m )\n\u001b[32m    920\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    921\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m stream:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpx\\_client.py:942\u001b[39m, in \u001b[36mClient._send_handling_auth\u001b[39m\u001b[34m(self, request, auth, follow_redirects, history)\u001b[39m\n\u001b[32m    939\u001b[39m request = \u001b[38;5;28mnext\u001b[39m(auth_flow)\n\u001b[32m    941\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m942\u001b[39m     response = \u001b[38;5;28mself\u001b[39m._send_handling_redirects(\n\u001b[32m    943\u001b[39m         request,\n\u001b[32m    944\u001b[39m         follow_redirects=follow_redirects,\n\u001b[32m    945\u001b[39m         history=history,\n\u001b[32m    946\u001b[39m     )\n\u001b[32m    947\u001b[39m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    948\u001b[39m         \u001b[38;5;28;01mtry\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpx\\_client.py:979\u001b[39m, in \u001b[36mClient._send_handling_redirects\u001b[39m\u001b[34m(self, request, follow_redirects, history)\u001b[39m\n\u001b[32m    976\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mrequest\u001b[39m\u001b[33m\"\u001b[39m]:\n\u001b[32m    977\u001b[39m     hook(request)\n\u001b[32m--> \u001b[39m\u001b[32m979\u001b[39m response = \u001b[38;5;28mself\u001b[39m._send_single_request(request)\n\u001b[32m    980\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    981\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m hook \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m._event_hooks[\u001b[33m\"\u001b[39m\u001b[33mresponse\u001b[39m\u001b[33m\"\u001b[39m]:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpx\\_client.py:1014\u001b[39m, in \u001b[36mClient._send_single_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m   1009\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mRuntimeError\u001b[39;00m(\n\u001b[32m   1010\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mAttempted to send an async request with a sync Client instance.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m   1011\u001b[39m     )\n\u001b[32m   1013\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m request_context(request=request):\n\u001b[32m-> \u001b[39m\u001b[32m1014\u001b[39m     response = transport.handle_request(request)\n\u001b[32m   1016\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, SyncByteStream)\n\u001b[32m   1018\u001b[39m response.request = request\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpx\\_transports\\default.py:250\u001b[39m, in \u001b[36mHTTPTransport.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    237\u001b[39m req = httpcore.Request(\n\u001b[32m    238\u001b[39m     method=request.method,\n\u001b[32m    239\u001b[39m     url=httpcore.URL(\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m     extensions=request.extensions,\n\u001b[32m    248\u001b[39m )\n\u001b[32m    249\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_httpcore_exceptions():\n\u001b[32m--> \u001b[39m\u001b[32m250\u001b[39m     resp = \u001b[38;5;28mself\u001b[39m._pool.handle_request(req)\n\u001b[32m    252\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(resp.stream, typing.Iterable)\n\u001b[32m    254\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m Response(\n\u001b[32m    255\u001b[39m     status_code=resp.status,\n\u001b[32m    256\u001b[39m     headers=resp.headers,\n\u001b[32m    257\u001b[39m     stream=ResponseStream(resp.stream),\n\u001b[32m    258\u001b[39m     extensions=resp.extensions,\n\u001b[32m    259\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py:256\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    253\u001b[39m         closing = \u001b[38;5;28mself\u001b[39m._assign_requests_to_connections()\n\u001b[32m    255\u001b[39m     \u001b[38;5;28mself\u001b[39m._close_connections(closing)\n\u001b[32m--> \u001b[39m\u001b[32m256\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m\n\u001b[32m    258\u001b[39m \u001b[38;5;66;03m# Return the response. Note that in this case we still have to manage\u001b[39;00m\n\u001b[32m    259\u001b[39m \u001b[38;5;66;03m# the point at which the response is closed.\u001b[39;00m\n\u001b[32m    260\u001b[39m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(response.stream, typing.Iterable)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\connection_pool.py:236\u001b[39m, in \u001b[36mConnectionPool.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    232\u001b[39m connection = pool_request.wait_for_connection(timeout=timeout)\n\u001b[32m    234\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m    235\u001b[39m     \u001b[38;5;66;03m# Send the request on the assigned connection.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m236\u001b[39m     response = connection.handle_request(\n\u001b[32m    237\u001b[39m         pool_request.request\n\u001b[32m    238\u001b[39m     )\n\u001b[32m    239\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m ConnectionNotAvailable:\n\u001b[32m    240\u001b[39m     \u001b[38;5;66;03m# In some cases a connection may initially be available to\u001b[39;00m\n\u001b[32m    241\u001b[39m     \u001b[38;5;66;03m# handle a request, but then become unavailable.\u001b[39;00m\n\u001b[32m    242\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    243\u001b[39m     \u001b[38;5;66;03m# In this case we clear the connection and try again.\u001b[39;00m\n\u001b[32m    244\u001b[39m     pool_request.clear_connection()\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\connection.py:103\u001b[39m, in \u001b[36mHTTPConnection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    100\u001b[39m     \u001b[38;5;28mself\u001b[39m._connect_failed = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    101\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exc\n\u001b[32m--> \u001b[39m\u001b[32m103\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._connection.handle_request(request)\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\http11.py:136\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    134\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m Trace(\u001b[33m\"\u001b[39m\u001b[33mresponse_closed\u001b[39m\u001b[33m\"\u001b[39m, logger, request) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    135\u001b[39m         \u001b[38;5;28mself\u001b[39m._response_closed()\n\u001b[32m--> \u001b[39m\u001b[32m136\u001b[39m \u001b[38;5;28;01mraise\u001b[39;00m exc\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\http11.py:106\u001b[39m, in \u001b[36mHTTP11Connection.handle_request\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m     95\u001b[39m     \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[32m     97\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m Trace(\n\u001b[32m     98\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33mreceive_response_headers\u001b[39m\u001b[33m\"\u001b[39m, logger, request, kwargs\n\u001b[32m     99\u001b[39m ) \u001b[38;5;28;01mas\u001b[39;00m trace:\n\u001b[32m    100\u001b[39m     (\n\u001b[32m    101\u001b[39m         http_version,\n\u001b[32m    102\u001b[39m         status,\n\u001b[32m    103\u001b[39m         reason_phrase,\n\u001b[32m    104\u001b[39m         headers,\n\u001b[32m    105\u001b[39m         trailing_data,\n\u001b[32m--> \u001b[39m\u001b[32m106\u001b[39m     ) = \u001b[38;5;28mself\u001b[39m._receive_response_headers(**kwargs)\n\u001b[32m    107\u001b[39m     trace.return_value = (\n\u001b[32m    108\u001b[39m         http_version,\n\u001b[32m    109\u001b[39m         status,\n\u001b[32m    110\u001b[39m         reason_phrase,\n\u001b[32m    111\u001b[39m         headers,\n\u001b[32m    112\u001b[39m     )\n\u001b[32m    114\u001b[39m network_stream = \u001b[38;5;28mself\u001b[39m._network_stream\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\http11.py:177\u001b[39m, in \u001b[36mHTTP11Connection._receive_response_headers\u001b[39m\u001b[34m(self, request)\u001b[39m\n\u001b[32m    174\u001b[39m timeout = timeouts.get(\u001b[33m\"\u001b[39m\u001b[33mread\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    176\u001b[39m \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m177\u001b[39m     event = \u001b[38;5;28mself\u001b[39m._receive_event(timeout=timeout)\n\u001b[32m    178\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(event, h11.Response):\n\u001b[32m    179\u001b[39m         \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_sync\\http11.py:217\u001b[39m, in \u001b[36mHTTP11Connection._receive_event\u001b[39m\u001b[34m(self, timeout)\u001b[39m\n\u001b[32m    214\u001b[39m     event = \u001b[38;5;28mself\u001b[39m._h11_state.next_event()\n\u001b[32m    216\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m event \u001b[38;5;129;01mis\u001b[39;00m h11.NEED_DATA:\n\u001b[32m--> \u001b[39m\u001b[32m217\u001b[39m     data = \u001b[38;5;28mself\u001b[39m._network_stream.read(\n\u001b[32m    218\u001b[39m         \u001b[38;5;28mself\u001b[39m.READ_NUM_BYTES, timeout=timeout\n\u001b[32m    219\u001b[39m     )\n\u001b[32m    221\u001b[39m     \u001b[38;5;66;03m# If we feed this case through h11 we'll raise an exception like:\u001b[39;00m\n\u001b[32m    222\u001b[39m     \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[32m    223\u001b[39m     \u001b[38;5;66;03m#     httpcore.RemoteProtocolError: can't handle event type\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    227\u001b[39m     \u001b[38;5;66;03m# perspective. Instead we handle this case distinctly and treat\u001b[39;00m\n\u001b[32m    228\u001b[39m     \u001b[38;5;66;03m# it as a ConnectError.\u001b[39;00m\n\u001b[32m    229\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m data == \u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m._h11_state.their_state == h11.SEND_RESPONSE:\n", "\u001b[36mFile \u001b[39m\u001b[32md:\\anaconda\\envs\\ocr\\Lib\\site-packages\\httpcore\\_backends\\sync.py:128\u001b[39m, in \u001b[36mSyncStream.read\u001b[39m\u001b[34m(self, max_bytes, timeout)\u001b[39m\n\u001b[32m    126\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m map_exceptions(exc_map):\n\u001b[32m    127\u001b[39m     \u001b[38;5;28mself\u001b[39m._sock.settimeout(timeout)\n\u001b[32m--> \u001b[39m\u001b[32m128\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._sock.recv(max_bytes)\n", "\u001b[31mKeyboardInterrupt\u001b[39m: "]}], "source": ["# 批量处理非标分类文件夹中的所有文档（优化版 - 按目录结构组织输出）\n", "\n", "def batch_process_documents_with_structure(input_dir, output_dir=\"../test\", supported_extensions=None, recursive=True, batch_size=10):\n", "    \"\"\"\n", "    批量处理文档（按输入目录结构组织输出）\n", "    \n", "    Args:\n", "        input_dir: 输入目录\n", "        output_dir: 输出目录\n", "        supported_extensions: 支持的文件扩展名列表\n", "        recursive: 是否递归处理子目录\n", "        batch_size: 每批处理的文件数量\n", "    \n", "    Returns:\n", "        dict: 处理结果字典\n", "    \"\"\"\n", "    if supported_extensions is None:\n", "        supported_extensions = ['.pdf', '.xlsx', '.xls', '.jpg', '.jpeg', '.png', '.docx', '.bmp', '.gif', '.tiff', '.webp', '.eml']\n", "    \n", "    print(f\"开始批量处理目录: {input_dir}\")\n", "    print(f\"输出目录: {output_dir}\")\n", "    print(f\"递归处理: {recursive}\")\n", "    \n", "    # 检查输入目录\n", "    if not os.path.exists(input_dir):\n", "        return {'success': False, 'error': f'输入目录不存在: {input_dir}'}\n", "    \n", "    # 创建输出目录\n", "    os.makedirs(output_dir, exist_ok=True)\n", "    \n", "    # 收集所有需要处理的文件及其相对路径信息\n", "    files_to_process = []\n", "    \n", "    if recursive:\n", "        for root, dirs, files in os.walk(input_dir):\n", "            for filename in files:\n", "                file_ext = os.path.splitext(filename)[1].lower()\n", "                if file_ext in supported_extensions:\n", "                    full_path = os.path.join(root, filename)\n", "                    # 计算相对于输入目录的路径\n", "                    rel_path = os.path.relpath(root, input_dir)\n", "                    # 获取第一层子目录名称\n", "                    first_level_dir = rel_path.split(os.sep)[0] if rel_path != '.' else ''\n", "                    \n", "                    files_to_process.append({\n", "                        'full_path': full_path,\n", "                        'filename': filename,\n", "                        'first_level_dir': first_level_dir,\n", "                        'relative_path': rel_path\n", "                    })\n", "    else:\n", "        for filename in os.listdir(input_dir):\n", "            if os.path.isfile(os.path.join(input_dir, filename)):\n", "                file_ext = os.path.splitext(filename)[1].lower()\n", "                if file_ext in supported_extensions:\n", "                    files_to_process.append({\n", "                        'full_path': os.path.join(input_dir, filename),\n", "                        'filename': filename,\n", "                        'first_level_dir': '',\n", "                        'relative_path': '.'\n", "                    })\n", "    \n", "    print(f\"找到 {len(files_to_process)} 个文件需要处理\")\n", "    \n", "    # 按第一层目录分组显示\n", "    dir_groups = {}\n", "    for file_info in files_to_process:\n", "        dir_name = file_info['first_level_dir'] or '根目录'\n", "        if dir_name not in dir_groups:\n", "            dir_groups[dir_name] = 0\n", "        dir_groups[dir_name] += 1\n", "    \n", "    print(\"文件分布:\")\n", "    for dir_name, count in dir_groups.items():\n", "        print(f\"  {dir_name}: {count} 个文件\")\n", "    \n", "    # 处理结果存储\n", "    results = []\n", "    all_transactions = []\n", "    batch_summaries = []\n", "    # 按目录分组存储交易数据和处理结果\n", "    dir_transactions = {}  # 每个目录的交易数据\n", "    dir_results = {}       # 每个目录的处理结果\n", "    \n", "    # 生成时间戳\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    \n", "    # 逐个处理文件\n", "    for i, file_info in enumerate(files_to_process, 1):\n", "        file_path = file_info['full_path']\n", "        filename = file_info['filename']\n", "        first_level_dir = file_info['first_level_dir']\n", "        \n", "        print(f\"\\n处理第 {i}/{len(files_to_process)} 个文件: {filename}\")\n", "        print(f\"  来源目录: {first_level_dir or '根目录'}\")\n", "        \n", "        try:\n", "            # 确定输出子目录\n", "            if first_level_dir:\n", "                file_output_dir = os.path.join(output_dir, first_level_dir)\n", "            else:\n", "                file_output_dir = output_dir\n", "            \n", "            # 创建输出子目录\n", "            os.makedirs(file_output_dir, exist_ok=True)\n", "            \n", "            # 处理文件\n", "            result = parse_transaction_document(file_path, save_result=True, output_dir=file_output_dir)\n", "            \n", "            if result:\n", "                # 添加文件信息\n", "                result['source_file'] = file_path\n", "                result['filename'] = filename\n", "                result['first_level_dir'] = first_level_dir\n", "                result['output_dir'] = file_output_dir\n", "                \n", "                # 初始化目录分组\n", "                dir_key = first_level_dir or '根目录'\n", "                if dir_key not in dir_transactions:\n", "                    dir_transactions[dir_key] = []\n", "                if dir_key not in dir_results:\n", "                    dir_results[dir_key] = []\n", "                \n", "                # 添加到目录结果中\n", "                dir_results[dir_key].append(result)\n", "                \n", "                # 如果解析成功，收集交易数据\n", "                if result.get('success') and result.get('parsed_result'):\n", "                    parsed_data = result['parsed_result']\n", "                    if isinstance(parsed_data, list):\n", "                        for transaction in parsed_data:\n", "                            if isinstance(transaction, dict):\n", "                                transaction['源文件'] = filename\n", "                                transaction['源目录'] = first_level_dir or '根目录'\n", "                                transaction['处理时间'] = timestamp\n", "                                all_transactions.append(transaction)\n", "                                dir_transactions[dir_key].append(transaction)\n", "                    elif isinstance(parsed_data, dict):\n", "                        parsed_data['源文件'] = filename\n", "                        parsed_data['源目录'] = first_level_dir or '根目录'\n", "                        parsed_data['处理时间'] = timestamp\n", "                        all_transactions.append(parsed_data)\n", "                        dir_transactions[dir_key].append(parsed_data)\n", "            \n", "            results.append(result)\n", "            \n", "            if result and result.get('success'):\n", "                print(f\"  ✓ 处理成功，输出到: {file_output_dir}\")\n", "            else:\n", "                print(f\"  ✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "                \n", "        except Exception as e:\n", "            print(f\"  ✗ 处理异常: {e}\")\n", "            error_result = {\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'source_file': file_path,\n", "                'filename': filename,\n", "                'first_level_dir': first_level_dir,\n", "                'error': str(e)\n", "            }\n", "            results.append(error_result)\n", "            \n", "            # 添加到目录结果中\n", "            dir_key = first_level_dir or '根目录'\n", "            if dir_key not in dir_results:\n", "                dir_results[dir_key] = []\n", "            dir_results[dir_key].append(error_result)\n", "        \n", "        # 每处理batch_size个文件生成一个中间汇总\n", "        if i % batch_size == 0 or i == len(files_to_process):\n", "            batch_num = (i - 1) // batch_size + 1\n", "            batch_summary = {\n", "                'batch_number': batch_num,\n", "                'processed_files': i,\n", "                'total_files': len(files_to_process),\n", "                'timestamp': datetime.now().strftime(\"%Y%m%d_%H%M%S\"),\n", "                'transactions_count': len(all_transactions)\n", "            }\n", "            batch_summaries.append(batch_summary)\n", "            print(f\"\\n  批次 {batch_num} 完成: {i}/{len(files_to_process)} 个文件\")\n", "    \n", "    # 为每个目录生成汇总JSON文件\n", "    print(f\"\\n=== 生成各目录汇总文件 ===\")\n", "    dir_summary_files = {}\n", "    \n", "    for dir_name, transactions in dir_transactions.items():\n", "        if transactions:  # 只为有交易数据的目录生成汇总\n", "            # 确定输出路径\n", "            if dir_name == '根目录':\n", "                dir_output_path = output_dir\n", "            else:\n", "                dir_output_path = os.path.join(output_dir, dir_name)\n", "            \n", "            # 生成目录汇总文件\n", "            dir_summary_file = os.path.join(dir_output_path, f\"{dir_name}_transactions_summary_{timestamp}.json\")\n", "            \n", "            # 目录统计信息\n", "            dir_stats = {\n", "                'directory_name': dir_name,\n", "                'timestamp': timestamp,\n", "                'total_files_processed': len(dir_results.get(dir_name, [])),\n", "                'successful_files': len([r for r in dir_results.get(dir_name, []) if r.get('success')]),\n", "                'failed_files': len([r for r in dir_results.get(dir_name, []) if not r.get('success')]),\n", "                'total_transactions': len(transactions),\n", "                'transactions': transactions\n", "            }\n", "            \n", "            with open(dir_summary_file, 'w', encoding='utf-8') as f:\n", "                json.dump(dir_stats, f, ensure_ascii=False, indent=2)\n", "            \n", "            dir_summary_files[dir_name] = dir_summary_file\n", "            print(f\"  {dir_name}: {len(transactions)} 笔交易 -> {dir_summary_file}\")\n", "    \n", "    # 生成统计信息\n", "    successful_count = sum(1 for r in results if r and r.get('success'))\n", "    failed_count = len(files_to_process) - successful_count\n", "    \n", "    print(f\"\\n批量处理完成: {successful_count}/{len(files_to_process)} 个文件处理成功\")\n", "    print(f\"成功解析的交易记录总数: {len(all_transactions)}\")\n", "    \n", "    # 按目录统计处理结果\n", "    dir_stats = {}\n", "    for result in results:\n", "        if result:\n", "            dir_name = result.get('first_level_dir', '') or '根目录'\n", "            if dir_name not in dir_stats:\n", "                dir_stats[dir_name] = {'total': 0, 'success': 0, 'failed': 0}\n", "            dir_stats[dir_name]['total'] += 1\n", "            if result.get('success'):\n", "                dir_stats[dir_name]['success'] += 1\n", "            else:\n", "                dir_stats[dir_name]['failed'] += 1\n", "    \n", "    print(f\"\\n各目录处理统计:\")\n", "    for dir_name, stats in dir_stats.items():\n", "        print(f\"  {dir_name}: {stats['success']}/{stats['total']} 成功\")\n", "    \n", "    # 保存详细批量处理报告\n", "    report_file = os.path.join(output_dir, f\"batch_report_{timestamp}.json\")\n", "    batch_report = {\n", "        'timestamp': timestamp,\n", "        'input_dir': input_dir,\n", "        'output_dir': output_dir,\n", "        'total_files': len(files_to_process),\n", "        'successful_files': successful_count,\n", "        'failed_files': failed_count,\n", "        'total_transactions': len(all_transactions),\n", "        'directory_stats': dir_stats,\n", "        'directory_summary_files': dir_summary_files,\n", "        'processing_details': results,\n", "        'batch_summaries': batch_summaries\n", "    }\n", "    \n", "    with open(report_file, 'w', encoding='utf-8') as f:\n", "        json.dump(batch_report, f, ensure_ascii=False, indent=2)\n", "    \n", "    # 保存全局交易数据汇总\n", "    summary_file = os.path.join(output_dir, f\"transactions_summary_{timestamp}.json\")\n", "    with open(summary_file, 'w', encoding='utf-8') as f:\n", "        json.dump(all_transactions, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"\\n=== 文件生成完成 ===\")\n", "    print(f\"全局批量处理报告: {report_file}\")\n", "    print(f\"全局交易数据汇总: {summary_file}\")\n", "    print(f\"各目录汇总文件: {len(dir_summary_files)} 个\")\n", "    \n", "    return {\n", "        'success': True,\n", "        'timestamp': timestamp,\n", "        'report_file': report_file,\n", "        'summary_file': summary_file,\n", "        'directory_summary_files': dir_summary_files,\n", "        'batch_report': batch_report,\n", "        'all_transactions': all_transactions,\n", "        'results': results,\n", "        'directory_stats': dir_stats,\n", "        'dir_transactions': dir_transactions\n", "    }\n", "\n", "# 执行新的批量处理（按目录结构组织输出）\n", "input_directory = \"../大模型样例/非标分类/分类\"\n", "output_directory = \"check/test\"\n", "\n", "print(f\"目标目录: {input_directory}\")\n", "print(f\"输出目录: {output_directory}\")\n", "print(f\"目录是否存在: {os.path.exists(input_directory)}\")\n", "\n", "if os.path.exists(input_directory):\n", "    print(f\"\\n开始批量处理目录: {input_directory}\")\n", "    print(\"这将处理所有子目录中的支持文件格式...\")\n", "    \n", "    # 执行批量处理（按目录结构组织输出）\n", "    batch_result = batch_process_documents_with_structure(\n", "        input_dir=input_directory, \n", "        output_dir=output_directory,\n", "        recursive=True,  # 递归处理子目录\n", "        batch_size=10    # 每10个文件生成一个中间汇总\n", "    )\n", "    \n", "    if batch_result.get('success'):\n", "        print(f\"\\n=== 批量处理完成 ===\")\n", "        print(f\"处理时间戳: {batch_result['timestamp']}\")\n", "        print(f\"详细报告: {batch_result['report_file']}\")\n", "        print(f\"交易汇总: {batch_result['summary_file']}\")\n", "        \n", "        # 显示统计信息\n", "        report = batch_result['batch_report']\n", "        print(f\"\\n统计信息:\")\n", "        print(f\"  总文件数: {report['total_files']}\")\n", "        print(f\"  成功处理: {report['successful_files']}\")\n", "        print(f\"  处理失败: {report['failed_files']}\")\n", "        print(f\"  交易记录: {report['total_transactions']}\")\n", "        \n", "        # 显示各目录统计\n", "        print(f\"\\n各目录处理结果:\")\n", "        for dir_name, stats in batch_result['directory_stats'].items():\n", "            print(f\"  {dir_name}: {stats['success']}/{stats['total']} 成功\")\n", "        \n", "        # 显示各目录汇总文件\n", "        print(f\"\\n各目录汇总文件:\")\n", "        for dir_name, summary_file in batch_result['directory_summary_files'].items():\n", "            print(f\"  {dir_name}: {summary_file}\")\n", "        \n", "        # 显示部分交易数据\n", "        transactions = batch_result['all_transactions']\n", "        if transactions:\n", "            print(f\"\\n前3笔交易记录示例:\")\n", "            for i, transaction in enumerate(transactions[:3], 1):\n", "                print(f\"第{i}笔 - {transaction.get('投资者名称', 'N/A')} | {transaction.get('业务类型', 'N/A')} | {transaction.get('源目录', 'N/A')} | {transaction.get('源文件', 'N/A')}\")\n", "        \n", "        # 保存当前批次信息供后续对比使用\n", "        globals()['last_batch_summary'] = batch_result['summary_file']\n", "        globals()['last_batch_timestamp'] = batch_result['timestamp']\n", "        print(f\"\\n✓ 批次信息已保存，可用于后续对比\")\n", "        \n", "    else:\n", "        print(f\"\\n批量处理失败: {batch_result.get('error', '未知错误')}\")\n", "else:\n", "    print(f\"\\n目录不存在: {input_directory}\")\n", "    print(\"请检查路径是否正确\")"]}, {"cell_type": "markdown", "id": "usage_examples", "metadata": {}, "source": ["## 使用示例\n", "\n", "### 1. 单个文件处理示例"]}, {"cell_type": "code", "execution_count": 18, "id": "single_file_example", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: d:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\n", "尝试文件路径: ../大模型样例/非标分类/分类/非标分红/龙银理财睿元理财产品(行内标识码RY23022)_2024-11-04_创金合信基金-********-龙银理财睿元理财产品（行内标识码RY23022）-分红-睿存2号.pdf_4111953_612b8352-9276-4600-8a66-4600720.pdf\n", "文件是否存在: True\n", "\n", "开始解析文档...\n"]}, {"ename": "NameError", "evalue": "name 'parse_transaction_document' is not defined", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[18]\u001b[39m\u001b[32m, line 12\u001b[39m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m os.path.exists(pdf_file_path):\n\u001b[32m     11\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m开始解析文档...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m12\u001b[39m     result = parse_transaction_document(pdf_file_path)\n\u001b[32m     14\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m result \u001b[38;5;129;01mand\u001b[39;00m result[\u001b[33m'\u001b[39m\u001b[33msuccess\u001b[39m\u001b[33m'\u001b[39m]:\n\u001b[32m     15\u001b[39m         \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33m解析成功!\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[31mNameError\u001b[39m: name 'parse_transaction_document' is not defined"]}], "source": ["import os,sys,json\n", "# 示例1: 处理PDF文件 - 使用正确的相对路径\n", "pdf_file_path = \"../大模型样例/非标分类/分类/非标分红/龙银理财睿元理财产品(行内标识码RY23022)_2024-11-04_创金合信基金-********-龙银理财睿元理财产品（行内标识码RY23022）-分红-睿存2号.pdf_4111953_612b8352-9276-4600-8a66-4600720.pdf\"\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"尝试文件路径: {pdf_file_path}\")\n", "print(f\"文件是否存在: {os.path.exists(pdf_file_path)}\")\n", "\n", "# 如果文件存在，则处理\n", "if os.path.exists(pdf_file_path):\n", "    print(\"\\n开始解析文档...\")\n", "    result = parse_transaction_document(pdf_file_path)\n", "    \n", "    if result and result['success']:\n", "        print(\"\\n解析成功!\")\n", "        print(\"解析结果:\")\n", "        print(json.dumps(result['parsed_result'], ensure_ascii=False, indent=2))\n", "    else:\n", "        print(f\"\\n解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "else:\n", "    print(f\"\\n文件不存在: {pdf_file_path}\")\n", "    print(\"请检查文件路径是否正确\")\n", "    print(\"提示: 从ipynb目录运行时，需要使用 '../大模型样例/...' 路径\")"]}, {"cell_type": "code", "execution_count": 25, "id": "direct_fix_test", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始处理文件: ../大模型样例/非标红利转投（脱敏）/百瑞安鑫悦盈项目红利再投数据20241102.xls\n", "\n", "原始Excel数据（保持精度）:\n", "第1行: {'客户名称': '百瑞至臻佳选1号集合资金信托计划', '投资产品': '安鑫悦盈集合资金信托计划A类信托单位', '日期': '20241102', '七日年化收益率': '2.5821', '万份收益': '0.6982', '持有份额': '657713.67', '当日分红': '45.93'}\n", "第2行: {'客户名称': '百瑞至诚佳选1号集合资金信托计划', '投资产品': '安鑫悦盈集合资金信托计划A类信托单位', '日期': '20241102', '七日年化收益率': '2.5821', '万份收益': '0.6982', '持有份额': '1386036.68', '当日分红': '96.79'}\n", "第3行: {'客户名称': '百瑞至远佳选1号集合资金信托计划', '投资产品': '安鑫悦盈集合资金信托计划A类信托单位', '日期': '20241102', '七日年化收益率': '2.5821', '万份收益': '0.6982', '持有份额': '31006.47', '当日分红': '2.16'}\n", "\n", "手动解析结果（保持精度）:\n", "[\n", "  {\n", "    \"投资者名称\": \"百瑞至臻佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"20241102\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"657713.67\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"百瑞至诚佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"20241102\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"1386036.68\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"百瑞至远佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"20241102\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"31006.47\",\n", "    \"交易费用\": \"/\"\n", "  }\n", "]\n", "\n", "结果已保存到: ./output\\百瑞安鑫悦盈项目红利再投数据20241102_手动修复_result.json\n"]}], "source": ["# 直接修复特定文件的精度问题\n", "import os\n", "import xlrd\n", "import json\n", "import pandas as pd\n", "\n", "# 目标文件\n", "target_file = \"../大模型样例/非标红利转投（脱敏）/百瑞安鑫悦盈项目红利再投数据20241102.xls\"\n", "\n", "if os.path.exists(target_file):\n", "    print(f\"开始处理文件: {target_file}\")\n", "    \n", "    # 直接读取Excel文件并保持精度\n", "    try:\n", "        # 读取Excel文件\n", "        workbook = xlrd.open_workbook(target_file)\n", "        worksheet = workbook.sheet_by_index(0)\n", "        \n", "        # 获取列名\n", "        headers = [str(worksheet.cell_value(0, col_idx)) for col_idx in range(worksheet.ncols)]\n", "        \n", "        # 读取数据并保持精度\n", "        data = []\n", "        for row_idx in range(1, worksheet.nrows):  # 跳过表头\n", "            row_data = {}\n", "            for col_idx in range(worksheet.ncols):\n", "                header = headers[col_idx]\n", "                cell_value = worksheet.cell_value(row_idx, col_idx)\n", "                \n", "                # 特殊处理浮点数，保持精度\n", "                if isinstance(cell_value, float):\n", "                    if cell_value.is_integer():\n", "                        cell_value = int(cell_value)\n", "                    else:\n", "                        # 使用字符串格式化保持精度\n", "                        cell_value = f\"{cell_value:.15g}\"\n", "                \n", "                row_data[header] = cell_value\n", "            data.append(row_data)\n", "        \n", "        # 打印原始数据\n", "        print(\"\\n原始Excel数据（保持精度）:\")\n", "        for i, row in enumerate(data):\n", "            print(f\"第{i+1}行: {row}\")\n", "        \n", "        # 构建解析结果\n", "        parsed_result = []\n", "        for row in data:\n", "            transaction = {\n", "                \"投资者名称\": row.get(\"客户名称\", \"/\"),\n", "                \"投资者账号\": \"/\",\n", "                \"业务日期\": str(int(row.get(\"日期\"))) if isinstance(row.get(\"日期\"), (int, float)) else row.get(\"日期\", \"/\"),\n", "                \"业务类型\": \"分红\",\n", "                \"投资标的名称\": row.get(\"投资产品\", \"/\"),\n", "                \"投资标的代码\": \"/\",\n", "                \"投资标的金额\": \"/\",\n", "                \"投资标的数量\": row.get(\"持有份额\", \"/\"),\n", "                \"交易费用\": \"/\"\n", "            }\n", "            parsed_result.append(transaction)\n", "        \n", "        # 打印解析结果\n", "        print(\"\\n手动解析结果（保持精度）:\")\n", "        print(json.dumps(parsed_result, ensure_ascii=False, indent=2))\n", "        \n", "        # 保存结果\n", "        output_dir = \"./output\"\n", "        os.makedirs(output_dir, exist_ok=True)\n", "        result_file = os.path.join(output_dir, \"百瑞安鑫悦盈项目红利再投数据20241102_手动修复_result.json\")\n", "        with open(result_file, 'w', encoding='utf-8') as f:\n", "            json.dump(parsed_result, f, ensure_ascii=False, indent=2)\n", "        print(f\"\\n结果已保存到: {result_file}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"处理失败: {e}\")\n", "else:\n", "    print(f\"文件不存在: {target_file}\")"]}, {"cell_type": "markdown", "id": "batch_usage", "metadata": {}, "source": ["### 4. 批量处理示例"]}, {"cell_type": "code", "execution_count": null, "id": "manual_comparison", "metadata": {}, "outputs": [], "source": ["# 手动对比任意两个汇总文件\n", "import glob\n", "\n", "print(\"=== 手动对比汇总文件 ===\")\n", "\n", "# 查找所有汇总文件\n", "summary_files = glob.glob(\"./output/batch_results/transactions_summary_*.json\")\n", "summary_files.sort(key=os.path.getctime)  # 按创建时间排序\n", "\n", "if len(summary_files) >= 2:\n", "    print(f\"找到 {len(summary_files)} 个汇总文件:\")\n", "    for i, file in enumerate(summary_files, 1):\n", "        file_time = os.path.getctime(file)\n", "        time_str = datetime.fromtimestamp(file_time).strftime('%Y-%m-%d %H:%M:%S')\n", "        print(f\"  {i}. {os.path.basename(file)} (创建时间: {time_str})\")\n", "    \n", "    # 自动选择最新的两个文件进行对比\n", "    file1 = summary_files[-2]  # 倒数第二个（较早的）\n", "    file2 = summary_files[-1]  # 最新的\n", "    \n", "    print(f\"\\n自动选择对比文件:\")\n", "    print(f\"  较早批次: {os.path.basename(file1)}\")\n", "    print(f\"  较新批次: {os.path.basename(file2)}\")\n", "    \n", "    # 执行对比\n", "    comparison_result = compare_batch_results(\n", "        summary_file1=file1,\n", "        summary_file2=file2,\n", "        output_dir=\"./output/batch_results\"\n", "    )\n", "    \n", "    if comparison_result.get('success'):\n", "        print(f\"\\n✓ 对比完成，详细结果已保存到: {comparison_result['comparison_file']}\")\n", "        \n", "        # 显示对比摘要\n", "        comp_data = comparison_result['comparison_result']\n", "        summary = comp_data['summary']\n", "        \n", "        print(f\"\\n=== 对比摘要 ===\")\n", "        print(f\"第一批次: {summary['first_batch']['total_transactions']} 笔交易, {summary['first_batch']['total_files']} 个文件\")\n", "        print(f\"第二批次: {summary['second_batch']['total_transactions']} 笔交易, {summary['second_batch']['total_files']} 个文件\")\n", "        print(f\"变化: 交易数量 {summary['differences']['transaction_count_diff']:+d}, 文件数量 {summary['differences']['file_count_diff']:+d}\")\n", "        \n", "        if comp_data['new_files']:\n", "            print(f\"新增文件: {len(comp_data['new_files'])} 个\")\n", "        if comp_data['removed_files']:\n", "            print(f\"删除文件: {len(comp_data['removed_files'])} 个\")\n", "        if comp_data['modified_files']:\n", "            print(f\"修改文件: {len(comp_data['modified_files'])} 个\")\n", "            \n", "    else:\n", "        print(f\"\\n✗ 对比失败: {comparison_result.get('error', '未知错误')}\")\n", "        \n", "elif len(summary_files) == 1:\n", "    print(f\"只找到1个汇总文件，需要至少2个文件才能进行对比\")\n", "    print(f\"现有文件: {os.path.basename(summary_files[0])}\")\n", "    print(\"请先执行第二次批量处理\")\n", "else:\n", "    print(\"未找到任何汇总文件，请先执行批量处理\")"]}, {"cell_type": "markdown", "id": "utility_functions", "metadata": {}, "source": ["### 5. 实用工具函数"]}, {"cell_type": "code", "execution_count": null, "id": "utility_functions_code", "metadata": {}, "outputs": [], "source": ["def display_parsing_result(result):\n", "    \"\"\"\n", "    美化显示解析结果\n", "    \"\"\"\n", "    if not result or not result.get('success'):\n", "        print(f\"解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "        return\n", "    \n", "    parsed_data = result['parsed_result']\n", "    \n", "    print(\"=\" * 50)\n", "    print(\"交易确认单解析结果\")\n", "    print(\"=\" * 50)\n", "    \n", "    if isinstance(parsed_data, list):\n", "        print(f\"共发现 {len(parsed_data)} 笔交易\\n\")\n", "        for i, transaction in enumerate(parsed_data, 1):\n", "            print(f\"第 {i} 笔交易:\")\n", "            print(\"-\" * 30)\n", "            for key, value in transaction.items():\n", "                print(f\"{key:12}: {value}\")\n", "            print()\n", "    else:\n", "        print(\"单笔交易:\")\n", "        print(\"-\" * 30)\n", "        for key, value in parsed_data.items():\n", "            print(f\"{key:12}: {value}\")\n", "    \n", "    print(\"=\" * 50)\n", "\n", "def export_to_excel(results, output_file):\n", "    \"\"\"\n", "    将解析结果导出到Excel文件\n", "    \"\"\"\n", "    all_transactions = []\n", "    \n", "    for result in results:\n", "        if result and result.get('success'):\n", "            parsed_data = result['parsed_result']\n", "            file_name = os.path.basename(result.get('file_path', '未知文件'))\n", "            \n", "            if isinstance(parsed_data, list):\n", "                for transaction in parsed_data:\n", "                    transaction['源文件'] = file_name\n", "                    all_transactions.append(transaction)\n", "            else:\n", "                parsed_data['源文件'] = file_name\n", "                all_transactions.append(parsed_data)\n", "    \n", "    if all_transactions:\n", "        df = pd.DataFrame(all_transactions)\n", "        # 重新排列列的顺序\n", "        columns_order = ['源文件', '投资者名称', '投资者账号', '业务日期', '业务类型', \n", "                        '投资标的名称', '投资标的代码', '投资标的金额', '投资标的数量', '交易费用']\n", "        \n", "        # 只保留存在的列\n", "        existing_columns = [col for col in columns_order if col in df.columns]\n", "        df = df[existing_columns]\n", "        \n", "        df.to_excel(output_file, index=False)\n", "        print(f\"解析结果已导出到Excel文件: {output_file}\")\n", "        print(f\"共导出 {len(all_transactions)} 笔交易记录\")\n", "    else:\n", "        print(\"没有成功解析的交易记录可以导出\")\n", "\n", "def validate_parsing_result(parsed_data):\n", "    \"\"\"\n", "    验证解析结果的完整性\n", "    \"\"\"\n", "    required_fields = ['投资者名称', '投资者账号', '业务日期', '业务类型', '投资标的名称']\n", "    \n", "    if isinstance(parsed_data, list):\n", "        for i, transaction in enumerate(parsed_data):\n", "            missing_fields = [field for field in required_fields if field not in transaction or transaction[field] == \"/\"]\n", "            if missing_fields:\n", "                print(f\"第 {i+1} 笔交易缺少必要字段: {', '.join(missing_fields)}\")\n", "    else:\n", "        missing_fields = [field for field in required_fields if field not in parsed_data or parsed_data[field] == \"/\"]\n", "        if missing_fields:\n", "            print(f\"交易记录缺少必要字段: {', '.join(missing_fields)}\")\n", "        else:\n", "            print(\"交易记录包含所有必要字段\")"]}, {"cell_type": "markdown", "id": "test_section", "metadata": {}, "source": ["### 6. 测试区域\n", "\n", "在这里可以测试您的文档解析功能："]}, {"cell_type": "code", "execution_count": null, "id": "test_area", "metadata": {}, "outputs": [], "source": ["# 测试区域 - 路径诊断和文件处理\n", "print(\"=== 路径诊断信息 ===\")\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"Python路径: {sys.path[0] if sys.path else 'N/A'}\")\n", "\n", "# 测试文件路径（多种可能的路径）\n", "possible_paths = [\n", "    \"大模型样例/非标红利转投（脱敏）/源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\",\n", "    \"../大模型样例/非标红利转投（脱敏）/源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\",\n", "    os.path.join(os.getcwd(), \"大模型样例\", \"非标红利转投（脱敏）\", \"源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\"),\n", "    os.path.join(os.path.dirname(os.getcwd()), \"大模型样例\", \"非标红利转投（脱敏）\", \"源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\")\n", "]\n", "\n", "test_file_path = None\n", "print(\"\\n=== 检查可能的文件路径 ===\")\n", "for i, path in enumerate(possible_paths, 1):\n", "    print(f\"{i}. 检查路径: {path}\")\n", "    if os.path.exists(path):\n", "        print(f\"   ✓ 文件存在!\")\n", "        test_file_path = path\n", "        break\n", "    else:\n", "        print(f\"   ✗ 文件不存在\")\n", "\n", "# 如果找到文件，则进行处理\n", "if test_file_path:\n", "    print(f\"\\n=== 开始处理文件 ===\")\n", "    print(f\"使用路径: {test_file_path}\")\n", "    \n", "    try:\n", "        # 解析文档\n", "        result = parse_transaction_document(test_file_path)\n", "        \n", "        # 显示结果\n", "        display_parsing_result(result)\n", "        \n", "        # 验证结果\n", "        if result and result.get('success'):\n", "            validate_parsing_result(result['parsed_result'])\n", "            \n", "            # 导出到Excel（可选）\n", "            # export_to_excel([result], \"test_result.xlsx\")\n", "            \n", "    except Exception as e:\n", "        print(f\"处理文件时发生错误: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        \n", "else:\n", "    print(\"\\n=== 未找到测试文件 ===\")\n", "    print(\"请检查以下事项:\")\n", "    print(\"1. 确认文件确实存在于指定位置\")\n", "    print(\"2. 检查文件路径是否正确（注意中文字符和特殊字符）\")\n", "    print(\"3. 确认当前工作目录是否正确\")\n", "    print(\"4. 尝试使用绝对路径\")\n", "    print(\"\\n支持的文件格式: PDF, Excel(.xlsx), 图片(.jpg, .png等), Word(.docx)\")\n", "    \n", "    # 列出当前目录的内容以供参考\n", "    print(\"\\n=== 当前目录内容 ===\")\n", "    try:\n", "        current_dir_contents = os.listdir('.')\n", "        for item in sorted(current_dir_contents)[:10]:  # 只显示前10个项目\n", "            item_path = os.path.join('.', item)\n", "            item_type = \"[DIR]\" if os.path.isdir(item_path) else \"[FILE]\"\n", "            print(f\"  {item_type} {item}\")\n", "        if len(current_dir_contents) > 10:\n", "            print(f\"  ... 还有 {len(current_dir_contents) - 10} 个项目\")\n", "    except Exception as e:\n", "        print(f\"无法列出目录内容: {e}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "eml_file_test", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 测试.eml文件处理功能 ===\n", "找到 2 个.eml文件:\n", "  1. 国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "  2. 国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "\n", "测试文件: 国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "\n", "--- 测试邮件内容提取 ---\n", "✓ 邮件内容提取成功\n", "内容长度: 804 字符\n", "\n", "前500字符预览:\n", "# 邮件信息\n", "\n", "**发件人**: <EMAIL>\n", "**收件人**: <EMAIL>, <EMAIL>, <EMAIL>,\n", "\t<EMAIL>, z<PERSON><PERSON><PERSON><PERSON>@crctrust.com, ny-zhang<PERSON>@crctrust.com,\n", "\t<EMAIL>, <EMAIL>, <EMAIL>,\n", "\t<EMAIL>\n", "**主题**: =?UTF-8?B?5Zu95rOw5ZCb5a6J5pS255uK5Yet6K+B5YWR5LuY6YCa55+lKOWNjua2pg==?=\n", " =?UTF-8?B?5rex5Zu95oqV5L+h5omY5pyJ6ZmQ5YWs5Y+477yN5Y2O5ram5L+h5omYwrc=?=\n", " =?UTF-8?B?5Lit6K+BNTAw5a6B5oOg5a6J5LqrMTHlj7fpm4blkIjotYQ=?=\n", " =?UTF-8?B?...\n", "\n", "--- 测试完整解析流程 ---\n", "开始处理文件: ../大模型样例\\非标分类\\分类\\原始文件非标资料收集\\兰晶晶\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14.eml\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 804 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: ./output\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14_20250723_104039_result.json\n", "Markdown内容已保存到: ./output\\国泰君安收益凭证兑付通知(华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划)2024-11-14_20250723_104039_markdown.md\n", "✓ .eml文件解析成功!\n", "发现 1 笔交易:\n", "\n", "第 1 笔交易:\n", "  投资者名称: 华润深国投信托有限公司－华润信托·中证500宁惠安享11号集合资金信托计划\n", "  投资者账号: /\n", "  业务日期: 2024-11-14\n", "  业务类型: 认购\n", "  投资标的名称: 凤玺伍佰定制\n", "  投资标的代码: 2318S2U784\n", "  投资标的金额: 63768801.37\n", "  投资标的数量: /\n", "  交易费用: /\n"]}], "source": ["# 测试.eml文件处理功能\n", "print(\"=== 测试.eml文件处理功能 ===\")\n", "\n", "# 查找.eml文件\n", "import glob\n", "eml_files = glob.glob(\"../大模型样例/**/*.eml\", recursive=True)\n", "\n", "if eml_files:\n", "    print(f\"找到 {len(eml_files)} 个.eml文件:\")\n", "    for i, file in enumerate(eml_files[:5], 1):  # 只显示前5个\n", "        print(f\"  {i}. {os.path.basename(file)}\")\n", "    \n", "    # 测试第一个.eml文件\n", "    test_eml_file = eml_files[0]\n", "    print(f\"\\n测试文件: {os.path.basename(test_eml_file)}\")\n", "    \n", "    # 测试邮件内容提取\n", "    print(\"\\n--- 测试邮件内容提取 ---\")\n", "    markdown_content = process_eml_file(test_eml_file)\n", "    \n", "    if markdown_content:\n", "        print(\"✓ 邮件内容提取成功\")\n", "        print(f\"内容长度: {len(markdown_content)} 字符\")\n", "        print(\"\\n前500字符预览:\")\n", "        print(markdown_content[:500] + \"...\" if len(markdown_content) > 500 else markdown_content)\n", "        \n", "        # 测试完整解析流程\n", "        print(\"\\n--- 测试完整解析流程 ---\")\n", "        result = parse_transaction_document(test_eml_file)\n", "        \n", "        if result and result.get('success'):\n", "            print(\"✓ .eml文件解析成功!\")\n", "            parsed_data = result['parsed_result']\n", "            \n", "            # 显示解析结果\n", "            if isinstance(parsed_data, list):\n", "                print(f\"发现 {len(parsed_data)} 笔交易:\")\n", "                for i, transaction in enumerate(parsed_data[:2], 1):  # 只显示前2笔\n", "                    print(f\"\\n第 {i} 笔交易:\")\n", "                    for key, value in transaction.items():\n", "                        print(f\"  {key}: {value}\")\n", "                if len(parsed_data) > 2:\n", "                    print(f\"\\n... 还有 {len(parsed_data) - 2} 笔交易\")\n", "            else:\n", "                print(\"单笔交易:\")\n", "                for key, value in parsed_data.items():\n", "                    print(f\"  {key}: {value}\")\n", "        else:\n", "            print(f\"✗ .eml文件解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "    else:\n", "        print(\"✗ 邮件内容提取失败\")\n", "        \n", "else:\n", "    print(\"未找到.eml文件进行测试\")\n", "    print(\"如果您有.eml文件，请将其放在../大模型样例/目录下\")\n", "    print(\"支持的.eml文件格式: 标准邮件格式文件\")\n", "    \n", "    # 提供手动测试的代码模板\n", "    print(\"\\n手动测试代码模板:\")\n", "    print('eml_file_path = \"path/to/your/email.eml\"')\n", "    print('result = parse_transaction_document(eml_file_path)')\n", "    print('print(result)')"]}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
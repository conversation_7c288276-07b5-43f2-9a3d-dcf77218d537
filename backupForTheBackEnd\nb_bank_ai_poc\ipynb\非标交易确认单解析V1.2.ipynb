{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 非标交易确认单文档解析 V1.2\n", "\n", "## 功能概述\n", "\n", "通过大模型对客户发送的非标交易确认单进行解析，支持多种文档格式：\n", "\n", "- PDF文件\n", "- Excel文件(.xlsx, .xls)\n", "- 图片文件(.jpg, .png等)\n", "- Word文档(.docx)\n", "\n", "## V1.1版本更新\n", "\n", "**主要修复了数值精度问题：**\n", "- 修复前：`329961736.1` ❌ (丢失最后一位小数)\n", "- 修复后：`329961736.14` ✅ (保持完整精度)\n", "\n", "**保留所有原有功能：**\n", "- 多格式文档支持\n", "- 批量处理功能\n", "- 详细的错误处理\n", "- 完整的依赖检查\n", "- 测试和验证工具\n", "\n", "## 解析内容\n", "\n", "1. **投资者名称**：通常指代客户姓名，一般是资管计划的名称\n", "2. **投资者账号**：通常指代客户的资金账号\n", "3. **业务日期**：对应某一笔交易的日期\n", "4. **业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. **投资标的名称**：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. **投资标的代码**：投资标的的代码，多为数字和字母的组合，也可能为空\n", "7. **投资标的金额**：实际交易的确认金额\n", "8. **投资标的数量**：文档中可能用份额来描述\n", "9. **交易费用**：一般申购、赎回、买入、卖出交易中，会标明交易费用，没有则可以为空"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ tabulate 已安装\n", "✓ openpyxl 已安装\n", "✓ xlrd 已安装\n", "✓ python-docx 已安装\n", "✓ PyPDF2 已安装\n", "✓ Pillow 已安装\n", "\n", "所有依赖库都已安装完成!\n"]}], "source": ["import sys\n", "import os\n", "import json\n", "import pandas as pd\n", "from datetime import datetime\n", "import re\n", "import subprocess\n", "\n", "# 检查和安装必要的依赖库\n", "def check_and_install_dependencies():\n", "    \"\"\"检查并安装必要的依赖库\"\"\"\n", "    required_packages = {\n", "        'tabulate': 'tabulate',\n", "        'openpyxl': 'openpyxl',\n", "        'xlrd': 'xlrd',\n", "        'python-docx': 'docx',\n", "        'PyPDF2': 'PyPDF2',\n", "        'Pillow': 'PIL'\n", "    }\n", "    \n", "    missing_packages = []\n", "    \n", "    for package_name, import_name in required_packages.items():\n", "        try:\n", "            if import_name == 'docx':\n", "                import docx\n", "            elif import_name == 'PIL':\n", "                from PIL import Image\n", "            else:\n", "                __import__(import_name)\n", "            print(f\"✓ {package_name} 已安装\")\n", "        except ImportError:\n", "            print(f\"✗ {package_name} 未安装\")\n", "            missing_packages.append(package_name)\n", "    \n", "    if missing_packages:\n", "        print(f\"\\n需要安装以下包: {', '.join(missing_packages)}\")\n", "        print(\"正在自动安装...\")\n", "        \n", "        for package in missing_packages:\n", "            try:\n", "                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])\n", "                print(f\"✓ {package} 安装成功\")\n", "            except subprocess.CalledProcessError as e:\n", "                print(f\"✗ {package} 安装失败: {e}\")\n", "                print(f\"请手动运行: pip install {package}\")\n", "    else:\n", "        print(\"\\n所有依赖库都已安装完成!\")\n", "\n", "# 运行依赖检查\n", "check_and_install_dependencies()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "当前工作目录: d:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\n", "目录内容: ['output', '券商账户计息变更.ipynb', '券商账户计息变更V1.1.ipynb', '券商账户计息变更V1.3.ipynb', '券商账户计息变更V1.4.ipynb']...\n"]}], "source": ["# 导入自定义模块\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from ocr_api import *\n", "\n", "# 导入文档处理相关库\n", "import docx\n", "from openpyxl import load_workbook\n", "import xlrd\n", "import PyPDF2\n", "from PIL import Image\n", "\n", "print(f\"\\n当前工作目录: {os.getcwd()}\")\n", "print(f\"目录内容: {os.listdir('.')[:5]}...\")  # 只显示前5个项目"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 初始化聊天机器人（已优化数字精度处理）\n", "chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 投资者名称：通常指代客户姓名，一般是资管计划的名称\n", "2. 投资者账号：通常指代客户的资金账号\n", "3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填\"/\"）\n", "4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填\"/\"）\n", "7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "9. 交易费用：一般申购、赎回、买入、卖出交易中，会标明交易费用（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "\n", "=====================\n", "【数值精度要求 - 极其重要】\n", "- 如果原文档显示\"329961736.14\"，必须输出\"329961736.14\"，不能输出\"329961736.1\"\n", "- 如果原文档显示\"12472553.63\"，必须输出\"12472553.63\"，不能输出\"12472553.6\"\n", "- 严禁进行任何数值格式化、精度优化或小数位截断\n", "- 将数值字段视为字符串，完全按照原文档中的字符进行复制\n", "- 不要使用科学计数法，不要四舍五入，不要截断小数位\n", "\n", "=====================\n", "【业务类型映射】\n", "- 分红：分红派息、现金分红、股息分红等\n", "- 红利转投：红利再投资、分红转投等\n", "- 买入：买入、购买等\n", "- 卖出：卖出、赎回卖出等\n", "- 认购：认购、新基金认购等\n", "- 申购：申购、基金申购等\n", "- 赎回：赎回、基金赎回等\n", "\n", "=====================\n", "【输出JSON格式】\n", "如果文档包含多笔交易，请返回数组格式。确保数字字段保持原始精度，不要四舍五入。单笔交易示例：\n", "\n", "```json\n", "{\n", "  \"投资者名称\": \"某某资产管理计划\",\n", "  \"投资者账号\": \"123456789\",\n", "  \"业务日期\": \"2024-01-15\",\n", "  \"业务类型\": \"申购\",\n", "  \"投资标的名称\": \"某某货币基金\",\n", "  \"投资标的代码\": \"000001\",\n", "  \"投资标的金额\": \"1000000.00\",\n", "  \"投资标的数量\": \"1000000.00\",\n", "  \"交易费用\": \"0.00\"\n", "}\n", "```\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def create_custom_markdown_table(data, headers):\n", "    \"\"\"创建自定义Markdown表格，确保数值精度不丢失\"\"\"\n", "    if not data or not headers:\n", "        return \"\"\n", "    \n", "    # 创建表头\n", "    header_row = \"| \" + \" | \".join(str(h) for h in headers) + \" |\"\n", "    separator_row = \"| \" + \" | \".join([\"---\"] * len(headers)) + \" |\"\n", "    \n", "    # 创建数据行\n", "    data_rows = []\n", "    for row in data:\n", "        row_values = []\n", "        for value in row:\n", "            if value is None:\n", "                row_values.append(\"\")\n", "            else:\n", "                # 确保完全按照原始字符串输出，不进行任何格式化\n", "                original_str = str(value).strip()\n", "                row_values.append(original_str)\n", "        data_rows.append(\"| \" + \" | \".join(row_values) + \" |\")\n", "    \n", "    # 组合表格\n", "    table_lines = [header_row, separator_row] + data_rows\n", "    return \"\\n\".join(table_lines) + \"\\n\\n\"\n", "\n", "def process_excel_file(file_path):\n", "    \"\"\"处理Excel文件，转换为markdown格式，确保数值精度不丢失\"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.xls':\n", "            # 处理老版Excel文件(.xls)\n", "            return process_xls_file(file_path)\n", "        \n", "        # 处理新版Excel文件(.xlsx) - 关键修复：使用字符串格式读取保持精度\n", "        df = pd.read_excel(file_path, dtype=str, engine='openpyxl')\n", "        \n", "        markdown_content = \"\\n## Excel数据\\n\\n\"\n", "        \n", "        # 使用自定义表格生成函数，避免pandas的数值格式化\n", "        headers = df.columns.tolist()\n", "        data = df.values.tolist()\n", "        \n", "        markdown_content += create_custom_markdown_table(data, headers)\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Excel文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_xls_file(file_path):\n", "    \"\"\"处理.xls文件，确保数值精度不丢失\"\"\"\n", "    try:\n", "        # 使用pandas读取，强制使用字符串格式\n", "        df = pd.read_excel(file_path, dtype=str, engine='xlrd')\n", "        \n", "        markdown_content = \"\\n## Excel数据\\n\\n\"\n", "        \n", "        # 使用自定义表格生成函数\n", "        headers = df.columns.tolist()\n", "        data = df.values.tolist()\n", "        \n", "        markdown_content += create_custom_markdown_table(data, headers)\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\".xls文件处理错误: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def process_file_to_markdown(file_path):\n", "    \"\"\"将不同格式的文件转换为markdown格式\"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.pdf':\n", "            # 处理PDF文件\n", "            result = trans_pdf_to_markdown(\n", "                file_path, \n", "                parse_method='auto', \n", "                backend='vlm-sglang-engine'\n", "            )\n", "            markdown_content = \"\"\n", "            for fn_name, data in result['results'].items():\n", "                markdown_content += data['md_content']\n", "            return markdown_content\n", "            \n", "        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:\n", "            # 处理图片文件 - 使用OCR方式\n", "            result = trans_pdf_to_markdown(\n", "                file_path, \n", "                parse_method='ocr', \n", "                backend='vlm-sglang-engine'\n", "            )\n", "            markdown_content = \"\"\n", "            for fn_name, data in result['results'].items():\n", "                markdown_content += data['md_content']\n", "            return markdown_content\n", "            \n", "        elif file_ext in ['.xlsx', '.xls']:\n", "            # 处理Excel文件（使用精度保护版本）\n", "            return process_excel_file(file_path)\n", "            \n", "        elif file_ext == '.docx':\n", "            # 处理Word文档\n", "            return process_docx_file(file_path)\n", "            \n", "        else:\n", "            raise ValueError(f\"不支持的文件格式: {file_ext}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_docx_file(file_path):\n", "    \"\"\"处理Word文档，转换为markdown格式\"\"\"\n", "    try:\n", "        doc = docx.Document(file_path)\n", "        markdown_content = \"\"\n", "        \n", "        # 处理段落\n", "        for paragraph in doc.paragraphs:\n", "            if paragraph.text.strip():\n", "                markdown_content += paragraph.text + \"\\n\"\n", "        \n", "        # 处理表格\n", "        for table in doc.tables:\n", "            markdown_content += \"\\n\"\n", "            table_data = []\n", "            headers = []\n", "            \n", "            for i, row in enumerate(table.rows):\n", "                row_data = []\n", "                for cell in row.cells:\n", "                    row_data.append(cell.text.strip())\n", "                \n", "                if i == 0:\n", "                    headers = row_data\n", "                else:\n", "                    table_data.append(row_data)\n", "            \n", "            if headers and table_data:\n", "                markdown_content += create_custom_markdown_table(table_data, headers)\n", "            markdown_content += \"\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Word文档处理错误: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def parse_transaction_document(file_path, save_result=True, output_dir=\"./output\"):\n", "    \"\"\"解析非标交易确认单文档\"\"\"\n", "    print(f\"开始处理文件: {file_path}\")\n", "    \n", "    # 检查文件是否存在\n", "    if not os.path.exists(file_path):\n", "        print(f\"错误: 文件不存在 - {file_path}\")\n", "        return None\n", "    \n", "    # 转换文件为markdown格式\n", "    print(\"正在转换文件为markdown格式...\")\n", "    markdown_content = process_file_to_markdown(file_path)\n", "    \n", "    if not markdown_content:\n", "        print(\"错误: 无法转换文件为markdown格式\")\n", "        return None\n", "    \n", "    print(f\"文件转换完成，内容长度: {len(markdown_content)} 字符\")\n", "    \n", "    # 使用大模型解析内容\n", "    print(\"正在使用大模型解析交易信息...\")\n", "    try:\n", "        response = chatbot.chat(markdown_content)\n", "        print(\"大模型解析完成\")\n", "        \n", "        # 尝试解析JSON响应\n", "        json_str = response.strip()\n", "        if json_str.startswith('```json'):\n", "            json_str = json_str[7:]\n", "        if json_str.endswith('```'):\n", "            json_str = json_str[:-3]\n", "        json_str = json_str.strip()\n", "        \n", "        parsed_result = json.loads(json_str)\n", "        \n", "        # 验证数值精度\n", "        print(\"\\n=== 数值精度验证 ===\")\n", "        if isinstance(parsed_result, dict):\n", "            check_precision([parsed_result])\n", "        elif isinstance(parsed_result, list):\n", "            check_precision(parsed_result)\n", "        \n", "        # 保存结果\n", "        if save_result:\n", "            os.makedirs(output_dir, exist_ok=True)\n", "            \n", "            base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            \n", "            # 保存原始markdown内容\n", "            markdown_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_markdown.md\")\n", "            with open(markdown_file, 'w', encoding='utf-8') as f:\n", "                f.write(markdown_content)\n", "            \n", "            # 保存解析结果\n", "            result_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_result.json\")\n", "            with open(result_file, 'w', encoding='utf-8') as f:\n", "                json.dump(parsed_result, f, ensure_ascii=False, indent=2)\n", "            \n", "            print(f\"结果已保存到: {result_file}\")\n", "            print(f\"Markdown内容已保存到: {markdown_file}\")\n", "        \n", "        return {\n", "            'success': True,\n", "            'file_path': file_path,\n", "            'markdown_content': markdown_content,\n", "            'parsed_result': parsed_result,\n", "            'raw_response': response\n", "        }\n", "        \n", "    except json.JSONDecodeError as e:\n", "        print(f\"JSON解析错误: {e}\")\n", "        print(f\"原始响应: {response}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': f'JSON解析错误: {e}',\n", "            'raw_response': response\n", "        }\n", "    except Exception as e:\n", "        print(f\"解析过程中发生错误: {e}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': str(e)\n", "        }\n", "\n", "def check_precision(transactions):\n", "    \"\"\"检查数值精度是否正确\"\"\"\n", "    numeric_fields = ['投资标的金额', '投资标的数量', '交易费用']\n", "    \n", "    for i, transaction in enumerate(transactions):\n", "        print(f\"\\n交易 {i+1}:\")\n", "        for field in numeric_fields:\n", "            if field in transaction:\n", "                value = transaction[field]\n", "                if value != \"/\":\n", "                    print(f\"  {field}: {value} (类型: {type(value).__name__})\")\n", "                    # 检查是否包含足够的小数位\n", "                    if '.' in str(value):\n", "                        decimal_places = len(str(value).split('.')[1])\n", "                        if decimal_places >= 2:\n", "                            print(f\"    ✓ 精度正确 ({decimal_places}位小数)\")\n", "                        else:\n", "                            print(f\"    ⚠ 可能精度不足 ({decimal_places}位小数)\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def batch_process_documents(folder_path, output_dir=\"./output\"):\n", "    \"\"\"批量处理文档\"\"\"\n", "    print(f\"开始批量处理文件夹: {folder_path}\")\n", "    \n", "    if not os.path.exists(folder_path):\n", "        print(f\"错误: 文件夹不存在 - {folder_path}\")\n", "        return []\n", "    \n", "    # 支持的文件格式\n", "    supported_extensions = ['.pdf', '.xlsx', '.xls', '.docx', '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']\n", "    \n", "    # 查找所有支持的文件\n", "    files_to_process = []\n", "    for root, dirs, files in os.walk(folder_path):\n", "        for file in files:\n", "            file_ext = os.path.splitext(file)[1].lower()\n", "            if file_ext in supported_extensions:\n", "                files_to_process.append(os.path.join(root, file))\n", "    \n", "    print(f\"找到 {len(files_to_process)} 个支持的文件\")\n", "    \n", "    results = []\n", "    for i, file_path in enumerate(files_to_process, 1):\n", "        print(f\"\\n处理第 {i}/{len(files_to_process)} 个文件: {os.path.basename(file_path)}\")\n", "        result = parse_transaction_document(file_path, save_result=True, output_dir=output_dir)\n", "        results.append(result)\n", "        \n", "        # 简单的进度显示\n", "        if result and result['success']:\n", "            print(f\"✓ 处理成功\")\n", "        else:\n", "            print(f\"✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "    \n", "    # 统计结果\n", "    successful = sum(1 for r in results if r and r.get('success', False))\n", "    failed = len(results) - successful\n", "    \n", "    print(f\"\\n批量处理完成!\")\n", "    print(f\"成功: {successful} 个文件\")\n", "    print(f\"失败: {failed} 个文件\")\n", "    \n", "    return results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 精度测试工具"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Excel精度测试 ===\n", "\n", "1. 默认读取方式:\n", "前几行数据:\n", "  Unnamed: 0      Unnamed: 1                                 Unnamed: 2  \\\n", "0        NaN             NaN                                        NaN   \n", "1         序号            客户名称                                     客户账户名称   \n", "2          1  上海农—商业银行股份有限公司                   上海农商银行“鑫利”系列天天金1号人民币理财产品   \n", "3          2  上海农—商业银行股份有限公司              上海农商银行“鑫利”系列天天金2号人民币理财产品（安享款）   \n", "4          3  上海农—商业银行股份有限公司  上海农商银行“鑫利”系列鑫享利22001期（最低持有30天之月月鑫）人民币理财产品   \n", "\n", "    Unnamed: 3     Unnamed: 4   Unnamed: 5  \n", "0          NaN            NaN          NaN  \n", "1         客户账号           当前份额       预计分红金额  \n", "2  ZS010008026   329961736.14  12472553.63  \n", "3  ZS010007993  1221808296.86  46184353.62  \n", "4  ZS010008086   660520554.84  24967676.97  \n", "\n", "2. 字符串读取方式（修复版本）:\n", "前几行数据:\n", "  Unnamed: 0      Unnamed: 1                                 Unnamed: 2  \\\n", "0        NaN             NaN                                        NaN   \n", "1         序号            客户名称                                     客户账户名称   \n", "2          1  上海农—商业银行股份有限公司                   上海农商银行“鑫利”系列天天金1号人民币理财产品   \n", "3          2  上海农—商业银行股份有限公司              上海农商银行“鑫利”系列天天金2号人民币理财产品（安享款）   \n", "4          3  上海农—商业银行股份有限公司  上海农商银行“鑫利”系列鑫享利22001期（最低持有30天之月月鑫）人民币理财产品   \n", "\n", "    Unnamed: 3     Unnamed: 4   Unnamed: 5  \n", "0          NaN            NaN          NaN  \n", "1         客户账号           当前份额       预计分红金额  \n", "2  ZS010008026   329961736.14  12472553.63  \n", "3  ZS010007993  1221808296.86  46184353.62  \n", "4  ZS010008086   660520554.84  24967676.97  \n", "\n", "3. 查找目标数值 329961736.14:\n", "  默认读取: 329961736.14 (类型: <class 'float'>)\n", "  修复版本读取: 329961736.14 (类型: <class 'str'>)\n"]}], "source": ["def test_excel_precision():\n", "    \"\"\"测试Excel读取精度\"\"\"\n", "    print(\"=== Excel精度测试 ===\")\n", "    \n", "    test_file = \"../大模型样例/POC脱敏材料/非标分红（脱敏）/春风41分红情况.xlsx\"\n", "    \n", "    if not os.path.exists(test_file):\n", "        print(f\"测试文件不存在: {test_file}\")\n", "        return\n", "    \n", "    try:\n", "        # 方法1: 默认读取\n", "        print(\"\\n1. 默认读取方式:\")\n", "        df1 = pd.read_excel(test_file)\n", "        print(f\"前几行数据:\\n{df1.head()}\")\n", "        \n", "        # 方法2: 字符串读取（修复版本）\n", "        print(\"\\n2. 字符串读取方式（修复版本）:\")\n", "        df2 = pd.read_excel(test_file, dtype=str, engine='openpyxl')\n", "        print(f\"前几行数据:\\n{df2.head()}\")\n", "        \n", "        # 查找目标数值\n", "        print(\"\\n3. 查找目标数值 329961736.14:\")\n", "        for df_name, df in [(\"默认\", df1), (\"修复版本\", df2)]:\n", "            found = False\n", "            for col in df.columns:\n", "                for idx, val in df[col].items():\n", "                    if str(val).startswith(\"329961736\"):\n", "                        print(f\"  {df_name}读取: {val} (类型: {type(val)})\")\n", "                        found = True\n", "                        break\n", "                if found:\n", "                    break\n", "        \n", "    except Exception as e:\n", "        print(f\"测试过程中发生错误: {e}\")\n", "\n", "# 运行精度测试\n", "test_excel_precision()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: d:\\NBObject\\nbmaster1.4\\nb_bank_ai_poc\\ipynb\n", "尝试文件路径: ../大模型样例/POC脱敏材料/非标分红（脱敏）/分红信息********.xlsx\n", "文件是否存在: True\n", "\n", "开始解析文档...\n", "开始处理文件: ../大模型样例/POC脱敏材料/非标分红（脱敏）/分红信息********.xlsx\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 1403 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "\n", "=== 数值精度验证 ===\n", "\n", "交易 1:\n", "  投资标的金额: -5228.68 (类型: str)\n", "    ✓ 精度正确 (2位小数)\n", "  投资标的数量: -5228.68 (类型: str)\n", "    ✓ 精度正确 (2位小数)\n", "  交易费用: 0 (类型: str)\n", "\n", "交易 2:\n", "  投资标的金额: -6711.63 (类型: str)\n", "    ✓ 精度正确 (2位小数)\n", "  投资标的数量: -6711.63 (类型: str)\n", "    ✓ 精度正确 (2位小数)\n", "  交易费用: 0 (类型: str)\n", "结果已保存到: ./output\\分红信息********_20250731_162333_result.json\n", "Markdown内容已保存到: ./output\\分红信息********_20250731_162333_markdown.md\n", "\n", "解析成功!\n", "解析结果:\n", "[\n", "  {\n", "    \"投资者名称\": \"中国对外经济贸易信托有限公司-外贸信托-天添富2号集合资金信托计划\",\n", "    \"投资者账号\": \"E58000024808\",\n", "    \"业务日期\": \"2024-11-15\",\n", "    \"业务类型\": \"红利转投\",\n", "    \"投资标的名称\": \"华林证券聚金宝1号\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"-5228.68\",\n", "    \"投资标的数量\": \"-5228.68\",\n", "    \"交易费用\": \"0\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"中国对外经济贸易信托有限公司-外贸信托-天添富1号集合资金信托计划\",\n", "    \"投资者账号\": \"E58000023672\",\n", "    \"业务日期\": \"2024-11-15\",\n", "    \"业务类型\": \"红利转投\",\n", "    \"投资标的名称\": \"华林证券聚金宝1号\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"-6711.63\",\n", "    \"投资标的数量\": \"-6711.63\",\n", "    \"交易费用\": \"0\"\n", "  }\n", "]\n"]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m<PERSON><PERSON> crashed while executing code in the current cell or a previous cell. \n", "\u001b[1;31m<PERSON><PERSON>se review the code in the cell(s) to identify a possible cause of the failure. \n", "\u001b[1;31mClick <a href='https://aka.ms/vscodeJupyterKernelCrash'>here</a> for more info. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["# 示例1：处理单个Excel文件\n", "test_file_path = \"../大模型样例/POC脱敏材料/非标分红（脱敏）/分红信息********.xlsx\"\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"尝试文件路径: {test_file_path}\")\n", "print(f\"文件是否存在: {os.path.exists(test_file_path)}\")\n", "\n", "if os.path.exists(test_file_path):\n", "    print(\"\\n开始解析文档...\")\n", "    result = parse_transaction_document(test_file_path)\n", "    \n", "    if result and result['success']:\n", "        print(\"\\n解析成功!\")\n", "        print(\"解析结果:\")\n", "        print(json.dumps(result['parsed_result'], ensure_ascii=False, indent=2))\n", "        \n", "        # 特别检查目标数值\n", "        parsed_data = result['parsed_result']\n", "        if isinstance(parsed_data, dict) and '投资标的数量' in parsed_data:\n", "            quantity = parsed_data['投资标的数量']\n", "            print(f\"\\n=== 关键检查 ===\")\n", "            print(f\"投资标的数量: {quantity}\")\n", "            if quantity == \"329961736.14\":\n", "                print(\"✅ 精度修复成功！显示完整的 329961736.14\")\n", "            elif quantity == \"329961736.1\":\n", "                print(\"❌ 精度问题仍然存在，显示为 329961736.1\")\n", "            else:\n", "                print(f\"⚠ 数值为其他值: {quantity}\")\n", "    else:\n", "        print(f\"\\n解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "else:\n", "    print(f\"\\n文件不存在: {test_file_path}\")\n", "    print(\"请检查文件路径是否正确\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 示例2：批量处理文件夹中的所有文档\n", "folder_path = \"../大模型样例/POC脱敏材料/非标分红（脱敏）\"\n", "\n", "print(f\"批量处理文件夹: {folder_path}\")\n", "print(f\"文件夹是否存在: {os.path.exists(folder_path)}\")\n", "\n", "if os.path.exists(folder_path):\n", "    print(\"\\n开始批量处理...\")\n", "    batch_results = batch_process_documents(folder_path)\n", "    \n", "    print(\"\\n=== 批量处理结果汇总 ===\")\n", "    for i, result in enumerate(batch_results, 1):\n", "        if result and result['success']:\n", "            file_name = os.path.basename(result['file_path'])\n", "            print(f\"{i}. {file_name} - ✅ 成功\")\n", "            \n", "            # 检查精度\n", "            parsed_data = result['parsed_result']\n", "            if isinstance(parsed_data, dict) and '投资标的数量' in parsed_data:\n", "                quantity = parsed_data['投资标的数量']\n", "                if '329961736' in str(quantity):\n", "                    if quantity == \"329961736.14\":\n", "                        print(f\"    精度检查: ✅ {quantity}\")\n", "                    else:\n", "                        print(f\"    精度检查: ⚠ {quantity}\")\n", "        else:\n", "            print(f\"{i}. 处理失败 - ❌\")\n", "else:\n", "    print(f\"\\n文件夹不存在: {folder_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 高级功能"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_parsing_results(results):\n", "    \"\"\"分析解析结果的统计信息\"\"\"\n", "    print(\"=== 解析结果分析 ===\")\n", "    \n", "    if not results:\n", "        print(\"没有结果可分析\")\n", "        return\n", "    \n", "    successful_results = [r for r in results if r and r.get('success', False)]\n", "    \n", "    print(f\"总文件数: {len(results)}\")\n", "    print(f\"成功解析: {len(successful_results)}\")\n", "    print(f\"解析失败: {len(results) - len(successful_results)}\")\n", "    print(f\"成功率: {len(successful_results)/len(results)*100:.1f}%\")\n", "    \n", "    if successful_results:\n", "        print(\"\\n=== 业务类型统计 ===\")\n", "        business_types = {}\n", "        for result in successful_results:\n", "            parsed_data = result['parsed_result']\n", "            transactions = [parsed_data] if isinstance(parsed_data, dict) else parsed_data\n", "            \n", "            for transaction in transactions:\n", "                if '业务类型' in transaction:\n", "                    btype = transaction['业务类型']\n", "                    business_types[btype] = business_types.get(btype, 0) + 1\n", "        \n", "        for btype, count in business_types.items():\n", "            print(f\"  {btype}: {count} 笔\")\n", "        \n", "        print(\"\\n=== 数值精度检查 ===\")\n", "        precision_issues = 0\n", "        total_numeric_fields = 0\n", "        \n", "        for result in successful_results:\n", "            parsed_data = result['parsed_result']\n", "            transactions = [parsed_data] if isinstance(parsed_data, dict) else parsed_data\n", "            \n", "            for transaction in transactions:\n", "                for field in ['投资标的金额', '投资标的数量', '交易费用']:\n", "                    if field in transaction and transaction[field] != \"/\":\n", "                        total_numeric_fields += 1\n", "                        value = str(transaction[field])\n", "                        if '.' in value:\n", "                            decimal_places = len(value.split('.')[1])\n", "                            if decimal_places < 2:\n", "                                precision_issues += 1\n", "        \n", "        print(f\"  总数值字段: {total_numeric_fields}\")\n", "        print(f\"  精度问题: {precision_issues}\")\n", "        if total_numeric_fields > 0:\n", "            print(f\"  精度正确率: {(total_numeric_fields-precision_issues)/total_numeric_fields*100:.1f}%\")\n", "\n", "def export_results_to_excel(results, output_file=\"parsing_results.xlsx\"):\n", "    \"\"\"将解析结果导出到Excel文件\"\"\"\n", "    print(f\"正在导出结果到: {output_file}\")\n", "    \n", "    successful_results = [r for r in results if r and r.get('success', False)]\n", "    \n", "    if not successful_results:\n", "        print(\"没有成功的解析结果可导出\")\n", "        return\n", "    \n", "    # 准备数据\n", "    export_data = []\n", "    for result in successful_results:\n", "        file_name = os.path.basename(result['file_path'])\n", "        parsed_data = result['parsed_result']\n", "        transactions = [parsed_data] if isinstance(parsed_data, dict) else parsed_data\n", "        \n", "        for i, transaction in enumerate(transactions):\n", "            row = {\n", "                '文件名': file_name,\n", "                '交易序号': i + 1,\n", "                '投资者名称': transaction.get('投资者名称', ''),\n", "                '投资者账号': transaction.get('投资者账号', ''),\n", "                '业务日期': transaction.get('业务日期', ''),\n", "                '业务类型': transaction.get('业务类型', ''),\n", "                '投资标的名称': transaction.get('投资标的名称', ''),\n", "                '投资标的代码': transaction.get('投资标的代码', ''),\n", "                '投资标的金额': transaction.get('投资标的金额', ''),\n", "                '投资标的数量': transaction.get('投资标的数量', ''),\n", "                '交易费用': transaction.get('交易费用', '')\n", "            }\n", "            export_data.append(row)\n", "    \n", "    # 创建DataFrame并导出\n", "    df = pd.DataFrame(export_data)\n", "    df.to_excel(output_file, index=False, engine='openpyxl')\n", "    \n", "    print(f\"✅ 成功导出 {len(export_data)} 条记录到 {output_file}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 完整工作流程示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def complete_workflow_example():\n", "    \"\"\"完整工作流程示例\"\"\"\n", "    print(\"=== 完整工作流程示例 ===\")\n", "    \n", "    # 1. 精度测试\n", "    print(\"\\n步骤1: 运行精度测试\")\n", "    test_excel_precision()\n", "    \n", "    # 2. 单文件处理测试\n", "    print(\"\\n步骤2: 单文件处理测试\")\n", "    test_file = \"../大模型样例/POC脱敏材料/非标分红（脱敏）/春风41分红情况.xlsx\"\n", "    if os.path.exists(test_file):\n", "        single_result = parse_transaction_document(test_file)\n", "        if single_result and single_result['success']:\n", "            print(\"✅ 单文件处理成功\")\n", "            \n", "            # 检查关键数值\n", "            parsed_data = single_result['parsed_result']\n", "            if isinstance(parsed_data, dict) and '投资标的数量' in parsed_data:\n", "                quantity = parsed_data['投资标的数量']\n", "                print(f\"关键数值检查: {quantity}\")\n", "                if quantity == \"329961736.14\":\n", "                    print(\"✅ 精度修复验证成功！\")\n", "                else:\n", "                    print(f\"⚠ 精度可能有问题: {quantity}\")\n", "        else:\n", "            print(\"❌ 单文件处理失败\")\n", "    else:\n", "        print(f\"测试文件不存在: {test_file}\")\n", "    \n", "    # 3. 批量处理（如果需要）\n", "    print(\"\\n步骤3: 批量处理（可选）\")\n", "    folder_path = \"../大模型样例/POC脱敏材料/非标分红（脱敏）\"\n", "    if os.path.exists(folder_path):\n", "        print(\"发现批量处理文件夹，是否需要批量处理？\")\n", "        print(\"如需批量处理，请运行: batch_results = batch_process_documents(folder_path)\")\n", "    \n", "    print(\"\\n=== 工作流程完成 ===\")\n", "    print(\"\\n主要功能:\")\n", "    print(\"1. parse_transaction_document(file_path) - 处理单个文件\")\n", "    print(\"2. batch_process_documents(folder_path) - 批量处理文件夹\")\n", "    print(\"3. test_excel_precision() - 测试Excel读取精度\")\n", "    print(\"4. analyze_parsing_results(results) - 分析解析结果\")\n", "    print(\"5. export_results_to_excel(results) - 导出结果到Excel\")\n", "\n", "# 运行完整工作流程\n", "complete_workflow_example()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "### V1.1版本主要改进\n", "\n", "1. **精度修复**: \n", "   - 修复前：`329961736.1` ❌ (丢失最后一位小数)\n", "   - 修复后：`329961736.14` ✅ (保持完整精度)\n", "\n", "2. **技术改进**:\n", "   - Excel读取使用 `pd.read_excel(file_path, dtype=str, engine='openpyxl')` 确保数值以字符串形式读取\n", "   - 创建 `create_custom_markdown_table()` 函数，完全按照原始字符串输出\n", "   - AI提示词强化，明确要求保持原始精度，严禁数值截断\n", "   - 添加 `check_precision()` 函数，自动检查解析结果的数值精度\n", "\n", "3. **功能完整性**:\n", "   - 保留所有原有功能：多格式支持、批量处理、错误处理等\n", "   - 新增精度测试工具和验证功能\n", "   - 新增结果分析和导出功能\n", "   - 提供完整的工作流程示例\n", "\n", "4. **使用建议**:\n", "   - 首先运行精度测试确认修复效果\n", "   - 使用单文件处理测试关键功能\n", "   - 根据需要进行批量处理\n", "   - 使用分析工具检查结果质量\n", "\n", "现在您的非标交易确认单解析系统既保持了所有原有功能，又能正确处理和显示所有数值的完整精度！"]}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 4}
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON文件批量校对系统
为金融量化交易平台设计的现代化JSON校对工具
"""

import os
import json
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
import traceback
from typing import Dict, List, Any, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class JsonComparator:
    """JSON文件比较器，支持嵌套结构比较"""
    
    def __init__(self):
        self.field_stats = {}
        self.total_files = 0
        self.correct_files = 0

    def is_simple_list(self, data: list) -> bool:
        """判断列表是否为简单列表（所有元素都是基本类型，不包含dict或list）"""
        if not isinstance(data, list):
            return False
        
        for item in data:
            if isinstance(item, (dict, list)):
                return False
        return True
        
    def flatten_dict(self, data, parent_key: str = '', sep: str = '.') -> dict:
        """将嵌套字典或列表扁平化"""
        items = []
        
        if isinstance(data, dict):
            for k, v in data.items():
                new_key = f"{parent_key}{sep}{k}" if parent_key else k
                if isinstance(v, (dict, list)):
                    items.extend(self.flatten_dict(v, new_key, sep=sep).items())
                else:
                    items.append((new_key, v))
        elif isinstance(data, list):
            # 检查是否为简单列表（所有元素都是基本类型）
            if self.is_simple_list(data):
                # 对于简单列表，将其转换为排序后的元组，以支持无序比较
                sorted_list = sorted(data, key=lambda x: (type(x).__name__, str(x)))
                key = f"{parent_key}[simple_list]" if parent_key else "[simple_list]"
                items.append((key, tuple(sorted_list)))
            else:
                # 对于复杂列表，保持原有的按索引处理方式
                for i, v in enumerate(data):
                    new_key = f"{parent_key}{sep}[{i}]" if parent_key else f"[{i}]"
                    if isinstance(v, (dict, list)):
                        items.extend(self.flatten_dict(v, new_key, sep=sep).items())
                    else:
                        items.append((new_key, v))
        else:
            # 如果既不是字典也不是列表，直接返回
            items.append((parent_key, data))
            
        return dict(items)
    
    def compare_json_files(self, standard_file: str, check_file: str) -> dict:
        """比较两个JSON文件"""
        try:
            with open(standard_file, 'r', encoding='utf-8') as f:
                standard_data = json.load(f)
            with open(check_file, 'r', encoding='utf-8') as f:
                check_data = json.load(f)
            
            # 获取字段过滤配置
            folder_name = os.path.basename(os.path.dirname(standard_file))
            config_file = os.path.join('./answer', folder_name, 'field_filters.json')
            excluded_fields = []
            
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    filters = json.load(f)
                    excluded_fields = filters.get('excluded_fields', [])
            
            # 扁平化处理
            standard_flat = self.flatten_dict(standard_data)
            check_flat = self.flatten_dict(check_data)
            
            # 应用字段过滤
            standard_flat = {k: v for k, v in standard_flat.items() if k not in excluded_fields}
            check_flat = {k: v for k, v in check_flat.items() if k not in excluded_fields}
            
            # 比较结果
            comparison = {
                'filename': os.path.basename(standard_file),
                'is_identical': standard_flat == check_flat,
                'field_comparisons': {},
                'missing_fields': [],
                'extra_fields': [],
                'total_fields': len(standard_flat),
                'correct_fields': 0,
                'excluded_fields': excluded_fields  # 添加被排除的字段列表
            }
            
            # 字段级比较
            all_keys = set(standard_flat.keys()) | set(check_flat.keys())
            
            for key in all_keys:
                if key in standard_flat and key in check_flat:
                    is_match = standard_flat[key] == check_flat[key]
                    comparison['field_comparisons'][key] = {
                        'standard': standard_flat[key],
                        'check': check_flat[key],
                        'is_match': is_match
                    }
                    if is_match:
                        comparison['correct_fields'] += 1
                elif key in standard_flat:
                    comparison['missing_fields'].append(key)
                    comparison['field_comparisons'][key] = {
                        'standard': standard_flat[key],
                        'check': None,
                        'is_match': False
                    }
                else:
                    comparison['extra_fields'].append(key)
                    comparison['field_comparisons'][key] = {
                        'standard': None,
                        'check': check_flat[key],
                        'is_match': False
                    }
            
            # 计算准确率
            comparison['field_accuracy'] = (comparison['correct_fields'] / comparison['total_fields'] * 100) if comparison['total_fields'] > 0 else 0
            
            return comparison
            
        except Exception as e:
            logger.error(f"比较文件时出错: {e}")
            return {
                'filename': os.path.basename(standard_file),
                'error': str(e),
                'is_identical': False,
                'field_comparisons': {},
                'missing_fields': [],
                'extra_fields': [],
                'total_fields': 0,
                'correct_fields': 0,
                'field_accuracy': 0,
                'excluded_fields': []
            }
    
    def batch_compare(self, answer_folder: str, check_folder: str) -> dict:
        """批量比较JSON文件"""
        try:
            results = []
            
            # 获取answer文件夹中的所有JSON文件（排除fn_md5_dict.json和field_filters.json）
            standard_files = []
            if os.path.exists(answer_folder):
                for filename in os.listdir(answer_folder):
                    if filename.endswith('.json') and filename not in ['fn_md5_dict.json', 'field_filters.json']:
                        standard_files.append(filename)
            
            if not standard_files:
                return {
                    'success': False,
                    'error': f'在 {answer_folder} 中没有找到JSON文件'
                }
            
            # 加载文件名映射
            md5_dict = {}
            md5_dict_file = os.path.join(answer_folder, 'fn_md5_dict.json')
            if os.path.exists(md5_dict_file):
                try:
                    with open(md5_dict_file, 'r', encoding='utf-8') as f:
                        md5_dict = json.load(f)
                except Exception as e:
                    logger.warning(f"加载文件名映射失败: {e}")
            
            # 初始化统计
            self.total_files = len(standard_files)
            self.correct_files = 0
            self.field_stats = {}
            
            # 逐个比较文件
            for filename in standard_files:
                standard_file = os.path.join(answer_folder, filename)
                check_file = os.path.join(check_folder, filename)
                
                if os.path.exists(check_file):
                    comparison = self.compare_json_files(standard_file, check_file)
                    
                    # 添加原始文件名（完整路径）
                    md5_key = filename.replace('.json', '')
                    if md5_key in md5_dict:
                        full_path = md5_dict[md5_key]
                        comparison['original_filename'] = full_path  # 显示完整路径
                    else:
                        comparison['original_filename'] = None
                    
                    results.append(comparison)
                    
                    if comparison.get('is_identical', False):
                        self.correct_files += 1
                    
                    # 统计字段准确率
                    for field, field_data in comparison.get('field_comparisons', {}).items():
                        if field not in self.field_stats:
                            self.field_stats[field] = {'total': 0, 'correct': 0}
                        self.field_stats[field]['total'] += 1
                        if field_data.get('is_match', False):
                            self.field_stats[field]['correct'] += 1
                else:
                    # 文件不存在的情况
                    comparison_result = {
                        'filename': filename,
                        'error': '对应的测试文件不存在',
                        'is_identical': False,
                        'field_comparisons': {},
                        'missing_fields': [],
                        'extra_fields': [],
                        'total_fields': 0,
                        'correct_fields': 0,
                        'field_accuracy': 0,
                        'excluded_fields': [],
                        'original_filename': None
                    }
                    
                    # 尝试添加原始文件名（完整路径）
                    md5_key = filename.replace('.json', '')
                    if md5_key in md5_dict:
                        full_path = md5_dict[md5_key]
                        comparison_result['original_filename'] = full_path  # 显示完整路径
                    
                    results.append(comparison_result)
            
            # 计算总体统计
            file_accuracy = (self.correct_files / self.total_files * 100) if self.total_files > 0 else 0
            
            # 计算各字段准确率
            field_accuracy_stats = {}
            for field, stats in self.field_stats.items():
                field_accuracy_stats[field] = (stats['correct'] / stats['total'] * 100) if stats['total'] > 0 else 0
            
            # 计算全字段准确率（正确的字段总数 / 全部字段总数）
            total_fields_count = sum(stats['total'] for stats in self.field_stats.values())
            total_correct_fields = sum(stats['correct'] for stats in self.field_stats.values())
            all_field_accuracy = (total_correct_fields / total_fields_count * 100) if total_fields_count > 0 else 0
            
            return {
                'success': True,
                'summary': {
                    'total_files': self.total_files,
                    'correct_files': self.correct_files,
                    'file_accuracy': file_accuracy,
                    'field_accuracy_stats': field_accuracy_stats,
                    'all_field_accuracy': all_field_accuracy
                },
                'details': results
            }
            
        except Exception as e:
            logger.error(f"批量比较时出错: {e}")
            return {
                'success': False,
                'error': str(e)
            }

# 全局比较器实例
comparator = JsonComparator()

@app.route('/')
def index():
    """主页面"""
    return render_template('index.html')

@app.route('/api/folders')
def get_folders():
    """获取answer文件夹中的所有子文件夹"""
    try:
        answer_path = './answer'
        folders = []
        
        if os.path.exists(answer_path):
            for item in os.listdir(answer_path):
                item_path = os.path.join(answer_path, item)
                if os.path.isdir(item_path):
                    # 计算该文件夹中的JSON文件数量（排除fn_md5_dict.json和field_filters.json）
                    json_count = 0
                    if os.path.exists(item_path):
                        for filename in os.listdir(item_path):
                            if filename.endswith('.json') and filename not in ['fn_md5_dict.json', 'field_filters.json']:
                                json_count += 1
                    
                    # 获取对应check文件夹下的时间子目录
                    check_path = os.path.join('./check', item)
                    time_dirs = []
                    if os.path.exists(check_path):
                        for time_dir in os.listdir(check_path):
                            time_dir_path = os.path.join(check_path, time_dir)
                            if os.path.isdir(time_dir_path) and len(time_dir) == 14 and time_dir.isdigit():
                                time_dirs.append(time_dir)
                        time_dirs.sort(reverse=True)  # 按时间倒序排列，最新的在前
                    
                    folders.append({
                        'name': item,
                        'json_count': json_count,
                        'time_dirs': time_dirs
                    })
        
        return jsonify({
            'success': True,
            'folders': folders
        })
        
    except Exception as e:
        logger.error(f"获取文件夹列表时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/time-dirs/<folder_name>')
def get_time_dirs(folder_name):
    """获取指定文件夹下的时间子目录"""
    try:
        # 首先获取标准答案文件夹中的JSON文件数量
        answer_path = os.path.join('./answer', folder_name)
        standard_json_count = 0
        if os.path.exists(answer_path):
            for filename in os.listdir(answer_path):
                if filename.endswith('.json') and filename not in ['fn_md5_dict.json', 'field_filters.json']:
                    standard_json_count += 1
        
        check_path = os.path.join('./check', folder_name)
        time_dirs = []
        
        if os.path.exists(check_path):
            for time_dir in os.listdir(check_path):
                time_dir_path = os.path.join(check_path, time_dir)
                if os.path.isdir(time_dir_path) and len(time_dir) == 14 and time_dir.isdigit():
                    # 计算该时间目录中的JSON文件数量（排除fn_md5_dict.json和field_filters.json）
                    json_count = 0
                    for filename in os.listdir(time_dir_path):
                        if filename.endswith('.json') and filename not in ['fn_md5_dict.json', 'field_filters.json']:
                            json_count += 1
                    
                    # 只有当JSON数量与标准答案数量一致时才添加到列表中
                    if json_count == standard_json_count:
                        # 格式化时间显示
                        try:
                            from datetime import datetime
                            dt = datetime.strptime(time_dir, '%Y%m%d%H%M%S')
                            formatted_time = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            formatted_time = time_dir
                        
                        time_dirs.append({
                            'dir_name': time_dir,
                            'formatted_time': formatted_time,
                            'json_count': json_count
                        })
        
        # 按时间倒序排列
        time_dirs.sort(key=lambda x: x['dir_name'], reverse=True)
        
        return jsonify({
            'success': True,
            'time_dirs': time_dirs
        })
        
    except Exception as e:
        logger.error(f"获取时间目录列表时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/compare', methods=['POST'])
def compare_files():
    """执行文件比较"""
    try:
        data = request.get_json()
        folder_name = data.get('folder_name')
        time_dir = data.get('time_dir')
        
        if not folder_name:
            return jsonify({
                'success': False,
                'error': '请指定文件夹名称'
            })
        
        if not time_dir:
            return jsonify({
                'success': False,
                'error': '请指定时间目录'
            })
        
        answer_folder = os.path.join('./answer', folder_name)
        check_folder = os.path.join('./check', folder_name, time_dir)
        
        if not os.path.exists(answer_folder):
            return jsonify({
                'success': False,
                'error': f'标准答案文件夹不存在: {answer_folder}'
            })
        
        if not os.path.exists(check_folder):
            return jsonify({
                'success': False,
                'error': f'测试文件夹不存在: {check_folder}'
            })
        
        result = comparator.batch_compare(answer_folder, check_folder)
        if result.get('success'):
            result['time_dir'] = time_dir  # 添加时间目录信息
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"比较文件时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        })

@app.route('/api/file-detail/<filename>')
def get_file_detail(filename):
    """获取单个文件的详细比较信息"""
    try:
        folder_name = request.args.get('folder')
        time_dir = request.args.get('time_dir')
        
        if not folder_name:
            return jsonify({
                'success': False,
                'error': '请指定文件夹名称'
            })
        
        if not time_dir:
            return jsonify({
                'success': False,
                'error': '请指定时间目录'
            })
        
        answer_file = os.path.join('./answer', folder_name, filename)
        check_file = os.path.join('./check', folder_name, time_dir, filename)
        
        if not os.path.exists(answer_file):
            return jsonify({
                'success': False,
                'error': f'标准文件不存在: {answer_file}'
            })
        
        if not os.path.exists(check_file):
            return jsonify({
                'success': False,
                'error': f'测试文件不存在: {check_file}'
            })
        
        comparison = comparator.compare_json_files(answer_file, check_file)
        
        # 加载文件名映射
        md5_dict_file = os.path.join('./answer', folder_name, 'fn_md5_dict.json')
        original_filename = None
        
        if os.path.exists(md5_dict_file):
            try:
                with open(md5_dict_file, 'r', encoding='utf-8') as f:
                    md5_dict = json.load(f)
                    # 从filename中提取MD5值（去掉.json后缀）
                    md5_key = filename.replace('.json', '')
                    if md5_key in md5_dict:
                        full_path = md5_dict[md5_key]
                        # 使用完整路径
                        original_filename = full_path
            except Exception as e:
                logger.warning(f"加载文件名映射失败: {e}")
        
        # 添加原始文件名到比较结果中
        comparison['original_filename'] = original_filename
        
        return jsonify({
            'success': True,
            'comparison': comparison
        })
        
    except Exception as e:
        logger.error(f"获取文件详情时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mark/<folder_name>/<time_dir>')
def get_mark(folder_name, time_dir):
    """获取mark.md文件内容"""
    try:
        mark_file = os.path.join('./check', folder_name, time_dir, 'mark.md')
        
        if os.path.exists(mark_file):
            with open(mark_file, 'r', encoding='utf-8') as f:
                content = f.read()
            return jsonify({
                'success': True,
                'content': content,
                'exists': True
            })
        else:
            return jsonify({
                'success': True,
                'content': '',
                'exists': False
            })
        
    except Exception as e:
        logger.error(f"获取mark.md时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/mark/<folder_name>/<time_dir>', methods=['POST'])
def save_mark(folder_name, time_dir):
    """保存mark.md文件内容"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        
        # 确保目录存在
        check_dir = os.path.join('./check', folder_name, time_dir)
        os.makedirs(check_dir, exist_ok=True)
        
        mark_file = os.path.join(check_dir, 'mark.md')
        
        # 如果内容为空，删除文件（如果存在）
        if not content.strip():
            if os.path.exists(mark_file):
                os.remove(mark_file)
                return jsonify({
                    'success': True,
                    'message': '备注已删除'
                })
            else:
                return jsonify({
                    'success': True,
                    'message': '备注为空，未创建文件'
                })
        else:
            # 保存内容到文件
            with open(mark_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return jsonify({
                'success': True,
                'message': '备注已保存'
            })
        
    except Exception as e:
        logger.error(f"保存mark.md时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/field-filters/<folder_name>')
def get_field_filters(folder_name):
    """获取字段过滤配置"""
    try:
        # 配置文件存储在answer目录下
        config_file = os.path.join('./answer', folder_name, 'field_filters.json')
        
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                filters = json.load(f)
            return jsonify({
                'success': True,
                'filters': filters
            })
        else:
            # 如果配置文件不存在，返回空配置
            return jsonify({
                'success': True,
                'filters': {
                    'excluded_fields': []
                }
            })
        
    except Exception as e:
        logger.error(f"获取字段过滤配置时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/field-filters/<folder_name>', methods=['POST'])
def save_field_filters(folder_name):
    """保存字段过滤配置"""
    try:
        data = request.get_json()
        filters = data.get('filters', {'excluded_fields': []})
        
        # 确保目录存在
        answer_dir = os.path.join('./answer', folder_name)
        os.makedirs(answer_dir, exist_ok=True)
        
        config_file = os.path.join(answer_dir, 'field_filters.json')
        
        # 保存配置到文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(filters, f, ensure_ascii=False, indent=2)
        
        return jsonify({
            'success': True,
            'message': '字段过滤配置已保存'
        })
        
    except Exception as e:
        logger.error(f"保存字段过滤配置时出错: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    # 创建模板和静态文件目录
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    
    app.run(debug=True, host='0.0.0.0', port=5000) 
import os
import requests
from PIL import Image
import re
import base64
import uuid
import io

# 后端服务URL
BACKEND_URL = "http://**************:30000"


def trans_pdf_to_markdown(file_path, 
               server_url=BACKEND_URL,
               return_md=True,
               return_images=True,
               return_middle_json=True,
               return_model_output=False,
               return_content_list=False,
               start_page_id=0,
               end_page_id=99999,
               parse_method='auto',
               lang_list='ch',
               output_dir='./output',
               backend='pipeline',
               table_enable=True,
               formula_enable=True,
               convert_to_scanned=False) -> dict:
    """
    封装文件解析接口
    
    :param file_path: 要上传的文件路径
    :param server_url: 接口URL，默认为'http://**************:30000/file_parse'
    :param return_md: 是否返回markdown格式，默认为True
    :param return_images: 是否返回图片，默认为False
    :param return_middle_json: 是否返回中间JSON，默认为False
    :param return_model_output: 是否返回模型输出，默认为False
    :param return_content_list: 是否返回内容列表，默认为False
    :param start_page_id: 起始页码，默认为0
    :param end_page_id: 结束页码，默认为99999
    :param parse_method: 解析方法，默认为'auto'，可选值为'auto'、'ocr'、'text'
    :param lang_list: 语言列表，默认为'ch'
    :param output_dir: 输出目录，默认为'./output'
    :param backend: 后端类型，默认为'pipeline'，可选值为'pipeline'、'vlm-transformers'、'vlm-sglang-engine'、'vlm-sglang-client'
    :param table_enable: 是否启用表格解析，默认为True
    :param formula_enable: 是否启用公式解析，默认为True
    :param convert_to_scanned: 是否将PDF转换为扫描PDF，默认为False
    :return: 接口响应结果
    """
    if 'file_parse' not in server_url:
        server_url = f"{server_url}/file_parse"
    try:
        file_path, is_image = detect_and_convert_to_pdf(file_path)    # 判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
        with open(file_path, 'rb') as f:
            files = {
                'files': (file_path.split('/')[-1], f, 'application/pdf'),
                'return_md': (None, str(return_md).lower()),
                'return_images': (None, str(return_images).lower()),
                'return_middle_json': (None, str(return_middle_json).lower()),
                'return_model_output': (None, str(return_model_output).lower()),
                'return_content_list': (None, str(return_content_list).lower()),
                'start_page_id': (None, str(start_page_id)),
                'end_page_id': (None, str(end_page_id)),
                'parse_method': (None, parse_method),
                'lang_list': (None, lang_list),
                'output_dir': (None, output_dir),
                'server_url': (None, 'string'),
                'backend': (None, backend),
                'table_enable': (None, str(table_enable).lower()),
                'formula_enable': (None, str(formula_enable).lower()),
                'convert_to_scanned': (None, str(convert_to_scanned).lower())
            }
            
            headers = {
                'accept': 'application/json'
            }
            
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
        
        if is_image:
            os.remove(file_path) # 删除临时生成的PDF文件
        return response.json()
        
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def convert_images_to_pdf(input_path, output_path=None):
    """
    将图片文件转换为PDF文档
    
    参数:
    - input_path: 输入路径，可以是单个图片文件路径或包含图片的文件夹路径
    - output_path: 可选的输出PDF文件路径。如果未提供，则使用输入文件/文件夹的名称
    
    支持格式:
    - 图片: JPEG, PNG, BMP, GIF, TIFF, WebP等Pillow支持的格式
    - 多页PDF: 支持多张图片合并到同一个PDF文件中
    
    返回:
    - 成功时返回PDF文件路径，失败时返回None
    """
    try:
        # 确定输出路径
        if output_path is None:
            if os.path.isfile(input_path):
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = base_name + ".pdf"
            elif os.path.isdir(input_path):
                folder_name = os.path.basename(input_path.rstrip(os.sep))
                output_path = folder_name + ".pdf"
        
        # 处理单个文件的情况
        if os.path.isfile(input_path):
            with Image.open(input_path) as img:
                if img.mode == 'RGBA':
                    img = img.convert('RGB')
                img.save(output_path, "PDF", resolution=100.0)
            return output_path
        
        # 处理文件夹的情况
        elif os.path.isdir(input_path):
            images = []
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
            
            # 收集所有支持的图片文件
            for filename in sorted(os.listdir(input_path)):
                if any(filename.lower().endswith(ext) for ext in valid_extensions):
                    filepath = os.path.join(input_path, filename)
                    try:
                        with Image.open(filepath) as img:
                            if img.mode == 'RGBA':
                                img = img.convert('RGB')
                            images.append(img.copy())
                    except Exception as e:
                        print(f"警告: 无法处理图片 {filename}: {e}")
            
            # 如果没有找到图片，返回None
            if not images:
                print("错误: 没有找到支持的图片文件")
                return None
            
            # 保存为多页PDF
            images[0].save(
                output_path,
                "PDF",
                resolution=100.0,
                save_all=True,
                append_images=images[1:]
            )
            return output_path
        
        else:
            print(f"错误: 路径 '{input_path}' 不存在或无法访问")
            return None
    
    except Exception as e:
        print(f"图片转PDF失败: {e}")
        return None


def detect_and_convert_to_pdf(input_path):
    """
    判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
    支持单张图片、图片文件夹、PDF文件。
    返回：元组，第一个元素是PDF文件路径或None，第二个元素是是否是图片，是图片则返回True，否则返回False
    """
    # 支持的图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
    if os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext == '.pdf':
            return input_path, False  # 已经是PDF
        elif ext in image_extensions:
            # 单张图片转PDF
            return convert_images_to_pdf(input_path), True
        else:
            print(f"不支持的文件类型: {input_path}")
            return None, False
    elif os.path.isdir(input_path):
        # 文件夹，假设里面是图片
        return convert_images_to_pdf(input_path), True
    else:
        print(f"路径不存在: {input_path}")
        return None, False


def get_seal_info(file_path, return_seal_img=False, server_url=BACKEND_URL, img_w=1, img_h=1, full_page=False):
    """
    获取印章信息，调用后端/seal_parse接口
    :param file_path: 输入PDF或图片文件路径
    :param return_seal_img: 是否返回公章图片的base64编码，默认为False
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'seal_parse' not in server_url:
        server_url = f"{server_url}/seal_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'return_seal_img': (None, str(return_seal_img).lower()),
                'img_w': (None, str(img_w)),
                'img_h': (None, str(img_h)),
                'full_page': (None, str(full_page).lower())
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def get_ocr_info(file_path, limit_score=None, server_url=BACKEND_URL):
    """
    获取OCR文本识别信息，调用后端/ocr_parse接口
    :param file_path: 输入PDF或图片文件路径
    :param limit_score: 分数阈值，用来过滤低于阈值分数的识别结果，默认为None
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'ocr_parse' not in server_url:
        server_url = f"{server_url}/ocr_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            
            # 如果提供了limit_score参数，则添加到files中
            if limit_score is not None:
                files['limit_score'] = (None, str(limit_score))
            
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    

def get_ocr_table_info(file_path, server_url=BACKEND_URL):
    """
    表格识别API，支持对上传的图片文件或PDF进行表格结构识别。
    :param file_path: 输入PDF或图片文件路径
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'ocr_table_parse' not in server_url:
        server_url = f"{server_url}/ocr_table_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def get_page_id_by_content(file_path, footer_threshold=0.85, server_url=BACKEND_URL):
    """
    根据内容获取页码，调用后端/structure_ocr_pages接口进行页面结构OCR识别
    :param file_path: 输入PDF或图片文件路径
    :param footer_threshold: 页脚阈值，用于识别页码位置，默认为0.85
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'structure_ocr_pages' not in server_url:
        server_url = f"{server_url}/structure_ocr_pages"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'footer_threshold': (None, str(footer_threshold))
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def erayt_ocr_to_markdown(file_path, scan_pdf=False, server_url=BACKEND_URL):
    """
    文档转Markdown API，支持对上传的PDF或图片文件进行结构化解析，将内容转换为Markdown格式
    
    功能特点：
    - 支持PDF和常见图片格式
    - 自动识别文档结构和布局
    - 提取文档中的图片并转换为base64编码
    - 支持扫描式PDF处理
    - 生成标准Markdown格式输出
    
    :param file_path: 输入PDF或图片文件路径
    :param scan_pdf: 是否将PDF转换为扫描式PDF后再进行解析，默认为False
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），包含markdown内容列表和图片字典，失败时返回None
            - markdown: 转换后的Markdown内容列表（每页一个元素）
            - imgs: 文档中提取的图片字典，键为图片文件名，值为base64编码的图片数据
    """
    if 'to_markdown' not in server_url:
        server_url = f"{server_url}/to_markdown"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'scan_pdf': (None, str(scan_pdf).lower())
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def erayt_ocr_to_json(file_path, scan_pdf=False, server_url=BACKEND_URL):
    """
    文档转JSON API，支持对上传的PDF或图片文件进行结构化解析，将内容转换为JSON格式
    """
    if 'to_json' not in server_url:
        server_url = f"{server_url}/to_json"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'scan_pdf': (None, str(scan_pdf).lower())
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def convert_image_format(image_data, source_format, target_format):
    """转换图片格式"""
    try:
        # 创建图片对象
        image = Image.open(io.BytesIO(image_data))
        
        # 如果是RGBA模式且目标格式不支持透明，转换为RGB
        if image.mode == 'RGBA' and target_format.lower() in ['jpg', 'jpeg']:
            # 创建白色背景
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
            image = background
        
        # 转换格式
        output_buffer = io.BytesIO()
        image.save(output_buffer, format=target_format.upper())
        converted_data = output_buffer.getvalue()
        
        # print(f"🔄 格式转换成功: {source_format} → {target_format}, 原大小={len(image_data)}字节, 转换后={len(converted_data)}字节")
        return converted_data
        
    except Exception as e:
        if source_format.lower() in ['x-emf', 'emf', 'wmf', 'x-wmf']:
            print(f"❌ {source_format} 格式转换失败: EMF/WMF 是 Windows 矢量格式，PIL 不直接支持")
            print("建议: 使用 Word/PowerPoint 或其他工具手动将图片转换为 PNG/JPG 格式")
        else:
            print(f"❌ 格式转换失败 ({source_format} → {target_format}): {e}")
        return None

def upload_to_image_host(image_data, image_format):
    """上传图片到图床"""
    try:
        # 图床API配置
        token = "46943a8826113721531ffe9a8851bc88"
        url = "http://**************:8093/api/index.php"
        
        # 支持的图片格式
        supported_formats = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'ico', 'jfif', 'tif', 'tga', 'svg']
        
        # 需要转换为PNG的格式
        convert_to_png_formats = ['x-emf', 'emf', 'wmf', 'x-wmf']
        
        # 格式映射和标准化
        format_mapping = {
            'jpg': 'jpg',
            'jpeg': 'jpg', 
            'png': 'png',
            'gif': 'gif',
            'bmp': 'bmp',
            'webp': 'webp',
            'ico': 'ico',
            'jfif': 'jpg',
            'tif': 'tif',
            'tiff': 'tif',
            'tga': 'tga',
            'svg': 'svg',
            # 转换为PNG的格式
            'x-emf': 'png',
            'emf': 'png',
            'wmf': 'png',
            'x-wmf': 'png'
        }
        
        # 标准化格式名称
        normalized_format = format_mapping.get(image_format.lower(), image_format.lower())
        
        # 设置正确的MIME类型
        mime_types = {
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg', 
            'png': 'image/png',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'webp': 'image/webp',
            'ico': 'image/x-icon',
            'jfif': 'image/jpeg',
            'tif': 'image/tiff',
            'tga': 'image/x-tga',
            'svg': 'image/svg+xml'
        }
        
        # 检查是否需要格式转换
        original_format = image_format.lower()
        upload_data = image_data
        upload_format = normalized_format
        
        if original_format in convert_to_png_formats:
            # print(f"🔄 检测到需要转换的格式: {image_format} → PNG")
            converted_data = convert_image_format(image_data, image_format, 'PNG')
            if converted_data:
                upload_data = converted_data
                upload_format = 'png'
                # print(f"✅ 将使用转换后的PNG格式上传")
            else:
                print(f"⚠️  格式转换失败，保持原始base64格式")
                return None
        elif normalized_format not in supported_formats:
            print(f"❌ 不支持的图片格式: {image_format}，支持的格式: {', '.join(supported_formats)}")
            print(f"建议的解决方案: 手动转换为PNG/JPG格式")
            return None
            
        # print(f"📤 准备上传图片: 原格式={image_format}, 上传格式={upload_format}, 文件大小={len(upload_data)}字节, MIME类型={mime_types.get(upload_format, f'image/{upload_format}')}")
        
        # 创建文件对象
        image_file = io.BytesIO(upload_data)
        filename = f"{uuid.uuid4().hex}.{upload_format}"
        
        mime_type = mime_types.get(upload_format, f'image/{upload_format}')
        
        # 构建请求参数
        files = {'image': (filename, image_file, mime_type)}
        data = {'token': token}
        
        # 发送POST请求
        response = requests.post(url, files=files, data=data)
        
        # 检查响应状态码
        if response.status_code == 200:
            result = response.json()
            if result.get('result') == 'success' and result.get('code') == 200:
                print(f"✅ 图片上传成功: {result['url']}")
                return result['url']
            else:
                print(f"❌ 图床返回错误: {result}")
                return None
        else:
            print(f"上传失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"上传图片到图床时出错: {e}")
        return None

def process_base64_images(content):
    """处理markdown中的base64图片"""
    def replace_image(match):
        description = match.group(1)  # 图片描述
        image_format = match.group(2)  # 图片格式 (png, jpeg, jpg等)
        base64_data = match.group(3)   # base64数据
        
        try:
            # 解码base64数据
            image_data = base64.b64decode(base64_data)
            
            # 上传到图床
            image_url = upload_to_image_host(image_data, image_format)
            
            if image_url:
                # 返回新的markdown引用（使用图床URL）
                return f"![{description}]({image_url})"
            else:
                print(f"⚠️  图片上传失败，保持原始base64格式 (描述: {description})")
                return match.group(0)  # 如果上传失败，返回原始内容
            
        except Exception as e:
            print(f"处理图片时出错: {e}")
            return match.group(0)  # 如果出错，返回原始内容
    # 正则表达式匹配base64图片数据
    # 匹配格式: ![description](data:image/format;base64,base64data)
    base64_pattern = r'!\[([^\]]*)\]\(data:image/([^;]+);base64,([^)]+)\)'
    # 使用正则表达式替换所有base64图片
    processed_content = re.sub(base64_pattern, replace_image, content)
    return processed_content

def mineru_res_out(res):
    """
    处理mineru v2的输出结果
    :param res: mineru v2的输出结果，即trans_pdf_to_markdown的返回结果
    :return: 处理后的markdown内容
    """
    out_res = list(res['results'].values())[0]
    md_content = out_res['md_content']
    images = out_res['images']
    for url, base64 in images.items():
        md_content = md_content.replace(url, base64)

    fix_md = process_base64_images(md_content)
    return fix_md
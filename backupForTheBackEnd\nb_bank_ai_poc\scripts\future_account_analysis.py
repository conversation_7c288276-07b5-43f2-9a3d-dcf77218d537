# 期货交易会员解析
import sys
sys.path.append("/app/宁波银行POC")
from util_ai import ChatBot
from utils import fn_to_markdown, process_file_to_base64, markdown_json_to_dict, cal_fn_md5, fn_to_markdown_v2
import os
import json
import tqdm
import datetime
import inspect
import re
from ocr_api import get_ocr_table_info

DIR_NAME = "期货开户文件"

def run(fn):
    usage_model = 'InternVL3-38B'
    if fn.endswith(".pdf"):
        markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model='InternVL3-38B', add_ocr_info=False)
    else:
        # 如果是图片则先转为pdf
        markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model='InternVL3-38B')    
    chatbot = ChatBot(model=usage_model,
    system_prompt="""你是一名期货开户文件解析专家。请严格区分“会员号”（固定 4 位数字）和“交易编码”（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填“/”）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为“投机”）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填“/”）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填“/”）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝能源所／上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认“投机”。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```
    """
    )
    if len(seal_img_list) > 0:
        response = chatbot.chat_with_img(markdown_content, img_url=seal_img_list)
    else:
        response = chatbot.chat(markdown_content)
    json_data = markdown_json_to_dict(response)
    return json_data


def get_full_fn_list():
    path_1 = "/app/宁波银行POC/大模型样例/期货交易会员解析"
    path_2 = "/app/宁波银行POC/大模型样例/POC脱敏材料/开户回执档案（脱敏）"

    file_types = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp']

    fn_list_1 = [os.path.join(path_1, fn) for fn in os.listdir(path_1) if os.path.splitext(fn)[1] in file_types]
    fn_list_2 = [os.path.join(path_2, fn) for fn in os.listdir(path_2) if os.path.splitext(fn)[1] in file_types]

    full_fn_list = fn_list_1 + fn_list_2
    return full_fn_list

def test_al(cache_answer=False):
    """
    测试所有文件，并保存答案
    """
    # 构建分支号
    var = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    full_fn_list = get_full_fn_list()
    # 文件md5与原始文件名（非完整路径）的映射
    fn_md5_dict = {}
    deal_fn_list = []
    check_dir = f"/app/宁波银行POC/check/{DIR_NAME}/{var}"
    os.makedirs(check_dir, exist_ok=True)
    if not cache_answer:
        # 将run函数的内容写入check_dir
        with open(f"{check_dir}/run.py", "w") as f:
            try:
                # 优先尝试获取函数源代码
                source = inspect.getsource(run)
                f.write(source)
            except (TypeError, OSError):
                # 备选方案：创建带注释的存根文件
                f.write("# run函数未找到有效源代码\n")
                f.write("def run():\n    # 函数实现未捕获\n    pass\n")

    for fn in tqdm.tqdm(full_fn_list):
        file_md5 = cal_fn_md5(fn)
        answer_fn = f"/app/宁波银行POC/answer/{DIR_NAME}/{file_md5}.json"
        check_fn = f"{check_dir}/{file_md5}.json"
        if cache_answer:
            if os.path.exists(answer_fn):
                continue
        else:
            save_fn = check_fn
        fn_md5_dict[file_md5] = fn
        json_data = run(fn)
        deal_fn_list.append(fn)
        with open(save_fn, "w") as f:
            json.dump(json_data, f, ensure_ascii=False, indent=4)

    with open(f"/app/宁波银行POC/answer/{DIR_NAME}/fn_md5_dict.json", "w") as f:
        json.dump(fn_md5_dict, f, ensure_ascii=False, indent=4)
    
    if len(deal_fn_list) > 0:
        print(f"所有答案预存完成，请手动校验至正确答案！")
    else:
        print("所有答案都已缓存，无新生成的答案！")

if __name__ == "__main__":
    select_but = input("请选择操作：\n1. 生成标准答案\n2. 生成测试答案\n")
    if select_but == "1":
        test_al(cache_answer=True)
    elif select_but == "2":
        test_al(cache_answer=False)
    else:
        print("无效选择！")
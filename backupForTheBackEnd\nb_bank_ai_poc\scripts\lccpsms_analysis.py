# 期货交易会员解析
import sys
sys.path.append("/app/宁波银行POC")
from util_ai import ChatBot
from utils import fn_to_markdown_v2, rag_search_neighbor, markdown_json_to_dict, cal_fn_md5
import os
import json
import tqdm
import datetime
import inspect
from ocr_api import get_ocr_table_info

DIR_NAME = "理财产品说明书"

def run(fn):
    usage_model = 'InternVL3-38B'
    markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
    refer_docs, refer_doc_text = rag_search_neighbor(markdown_content, keywords=["销售", "代销", "代理销售"], keywords2=["公司", "银行"])

    chatbot = ChatBot(
    model=usage_model,
    system_prompt="""你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。
    注意：请勿捏造数据，请根据实际情况输出。
    请注意：
    - 输出格式必须为json格式，不要输出其他内容。
    - 如不存在销售机构，则输出销售机构为本银行

    # 示例输出(仅供参考，请根据实际情况输出)
    ```json
    {
      "销售机构": [
        "XX银行股份有限公司",
        "XX银行股份有限公司
      ]
    }
    ```
    """
    )
    response = chatbot.chat(refer_doc_text, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(response)
    return json_data

def get_full_fn_list():
    path_1 = "/app/宁波银行POC/大模型样例/理财产品说明书"
    path_2 = "/app/宁波银行POC/大模型样例/POC脱敏材料/理财产品说明书（脱敏）"

    file_types = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp']

    fn_list_1 = [fn for fn in os.listdir(path_1) if os.path.splitext(fn)[1] in file_types]
    fn_list_2 = [fn for fn in os.listdir(path_2) if os.path.splitext(fn)[1] in file_types]
    fn_list_2 = [fn for fn in fn_list_2 if fn not in fn_list_1] # 排除重复文件

    fn_list_1 = [os.path.join(path_1, fn) for fn in fn_list_1]
    fn_list_2 = [os.path.join(path_2, fn) for fn in fn_list_2]

    full_fn_list = fn_list_1 + fn_list_2
    return full_fn_list

def test_al(cache_answer=False):
    """
    测试所有文件，并保存答案
    """
    # 构建分支号
    var = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    full_fn_list = get_full_fn_list()
    # 文件md5与原始文件名（非完整路径）的映射
    fn_md5_dict = {}
    deal_fn_list = []
    check_dir = f"/app/宁波银行POC/check/{DIR_NAME}/{var}"
    answer_dir = f"/app/宁波银行POC/answer/{DIR_NAME}"
    os.makedirs(answer_dir, exist_ok=True)
    os.makedirs(check_dir, exist_ok=True)
    if not cache_answer:
        # 将run函数的内容写入check_dir
        with open(f"{check_dir}/run.py", "w") as f:
            try:
                # 优先尝试获取函数源代码
                source = inspect.getsource(run)
                f.write(source)
            except (TypeError, OSError):
                # 备选方案：创建带注释的存根文件
                f.write("# run函数未找到有效源代码\n")
                f.write("def run():\n    # 函数实现未捕获\n    pass\n")

    for fn in tqdm.tqdm(full_fn_list):
        file_md5 = cal_fn_md5(fn)
        answer_fn = f"{answer_dir}/{file_md5}.json"
        check_fn = f"{check_dir}/{file_md5}.json"
        save_fn = answer_fn
        fn_md5_dict[file_md5] = fn
        if cache_answer:
            if os.path.exists(answer_fn):
                continue
        else:
            save_fn = check_fn
        json_data = run(fn)
        deal_fn_list.append(fn)
        with open(save_fn, "w") as f:
            json.dump(json_data, f, ensure_ascii=False, indent=4)

    
    if len(deal_fn_list) > 0:
        print(f"所有答案预存完成，请手动校验至正确答案！")
        with open(f"/app/宁波银行POC/answer/{DIR_NAME}/fn_md5_dict.json", "w") as f:
            json.dump(fn_md5_dict, f, ensure_ascii=False, indent=4)
    else:
        print("所有答案都已缓存，无新生成的答案！")

if __name__ == "__main__":
    select_but = input("请选择操作：\n1. 生成标准答案\n2. 生成测试答案\n")
    if select_but == "1":
        test_al(cache_answer=True)
    elif select_but == "2":
        test_al(cache_answer=False)
    else:
        print("无效选择！")
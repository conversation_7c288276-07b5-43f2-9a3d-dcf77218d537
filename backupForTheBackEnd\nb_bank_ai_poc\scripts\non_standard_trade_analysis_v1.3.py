# 非标交易确认单解析 - V1.3优化版
"""
V1.3版本主要优化内容：
1. 数值格式优化：投资标的金额、投资标的数量、交易费用保留两位小数的字符串格式，如果没有则是"/"
2. 金额识别优化：识别金额时自动去除金融标识符（如￥、$等），只保留数字部分
3. 引号处理优化：正确区分中文引号""和英文引号""，保持原样输出
4. 图片格式支持：新增对.png格式的完整支持
5. 字段识别优化：
   - 【投资者账号】对应客户的资金账号，通常为文件中的"基金账号"或"交易账号"
   - 【投资标的金额】常规场景：对应交易的确认金额；特殊场景（分红）：优先提取"红利"字段
   - 【投资标的数量】常规场景：对应交易的份额；特殊场景（分红）：优先提取"红股"字段
   - 【交易费用】仅在申购、赎回、买入、卖出等交易类型中提取费用字段
"""
import sys
import os
# 获取当前脚本所在目录的上级目录（项目根目录）
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

from util_ai import ChatBot
from utils import fn_to_markdown_v2, process_file_to_base64, markdown_json_to_dict, cal_fn_md5
import json
import tqdm
import datetime
import inspect
import time
import pandas as pd
import io
import re

DIR_NAME = "非标交易确认单"

# V1.3版本新增：数值格式化函数
def format_numeric_value(value):
    """格式化数值，保留两位小数的字符串格式，去除金融标识符"""
    if value is None or value == "" or str(value).strip() == "":
        return "/"

    # 转换为字符串并去除首尾空格
    value_str = str(value).strip()

    # 如果已经是"/"，直接返回
    if value_str == "/":
        return "/"

    # 去除金融标识符（￥、$、€、£等）并匹配包含千分位分隔符的数字
    # 修改正则表达式以正确处理千分位分隔符
    number_pattern = r'[-]?(?:\d{1,3}(?:,\d{3})*|\d+)(?:\.\d+)?'
    match = re.search(number_pattern, value_str)

    if match:
        number_str = match.group()
        try:
            # 去除千分位分隔符，然后转换为浮点数
            clean_number_str = number_str.replace(',', '')
            number = float(clean_number_str)
            # 格式化为两位小数的字符串
            return f"{number:.2f}"
        except ValueError:
            return "/"
    else:
        return "/"

def clean_quotes(text):
    """处理引号，保持中文引号和英文引号的原样"""
    if not isinstance(text, str):
        return text

    # 不进行引号转换，保持原样
    return text

# V1.3版本新增：后处理函数
def post_process_result(parsed_result):
    """V1.3版本：后处理解析结果，确保数值格式正确"""
    if isinstance(parsed_result, dict):
        return post_process_transaction(parsed_result)
    elif isinstance(parsed_result, list):
        return [post_process_transaction(transaction) for transaction in parsed_result]
    else:
        return parsed_result

def post_process_transaction(transaction):
    """V1.3版本：后处理单个交易记录"""
    if not isinstance(transaction, dict):
        return transaction

    # 需要格式化的数值字段
    numeric_fields = ['投资标的金额', '投资标的数量', '交易费用']

    processed_transaction = transaction.copy()

    for field in numeric_fields:
        if field in processed_transaction:
            # 特殊处理交易费用：分红等场景不提取费用
            if field == '交易费用':
                business_type = processed_transaction.get('业务类型', '')
                if business_type in ['分红', '红利转投']:
                    processed_transaction[field] = '/'
                    continue

            # 格式化数值
            processed_transaction[field] = format_numeric_value(processed_transaction[field])

    return processed_transaction

def post_process_json_data(json_data, original_markdown):
    """
    后处理JSON数据，修复数值字段提取问题 - V1.3版本优化
    """
    if not isinstance(json_data, list):
        # 如果是单个字典，先转换为列表处理
        if isinstance(json_data, dict):
            json_data = [json_data]
        else:
            return json_data

    # 业务类型标准化映射
    business_type_mapping = {
        # 分红相关
        '分红': '分红', '股息': '分红', '利息分配': '分红', '收益分配': '分红',
        '现金分红': '分红', '分红派息': '分红', '预计分红': '分红',

        # 红利转投相关
        '红利转投': '红利转投', '分红转投': '红利转投', '红利再投资': '红利转投',
        '分红再投资': '红利转投', '红利转份额': '红利转投', '分红转份额': '红利转投',

        # 买入相关
        '买入': '买入', '购买': '买入', '投资': '买入', '成交买入': '买入',

        # 卖出相关
        '卖出': '卖出', '出售': '卖出', '变现': '卖出', '成交卖出': '卖出',
        '兑付': '卖出', '到期兑付': '卖出',

        # 认购相关
        '认购': '认购', '首次认购': '认购', '新发认购': '认购', '认购确认': '认购',

        # 申购相关
        '申购': '申购', '追加申购': '申购', '申购确认': '申购', '增购': '申购',

        # 赎回相关
        '赎回': '赎回', '赎回确认': '赎回', '提取': '赎回', '退出': '赎回',
        '部分赎回': '赎回', '全部赎回': '赎回'
    }

    processed_data = []

    for item in json_data:
        if not isinstance(item, dict):
            processed_data.append(item)
            continue

        processed_item = item.copy()

        # 1. 标准化业务类型
        if '业务类型' in processed_item:
            original_type = processed_item['业务类型']
            if original_type in business_type_mapping:
                processed_item['业务类型'] = business_type_mapping[original_type]
            elif original_type and original_type != '/':
                # 尝试模糊匹配
                for key, value in business_type_mapping.items():
                    if key in original_type or original_type in key:
                        processed_item['业务类型'] = value
                        break

        # 2. V1.3版本特殊处理：分红场景的字段识别
        business_type = processed_item.get('业务类型', '')
        if business_type == '分红':
            # 分红场景：优先提取"红利"作为标的金额
            if '投资标的金额' in processed_item and processed_item['投资标的金额'] == '/':
                red_profit = extract_field_from_markdown(original_markdown, ['红利', '分红金额', '现金分红'])
                if red_profit:
                    processed_item['投资标的金额'] = red_profit

            # 分红场景：优先提取"红股"作为标的数量
            if '投资标的数量' in processed_item and processed_item['投资标的数量'] == '/':
                red_stock = extract_field_from_markdown(original_markdown, ['红股', '分红股数'])
                if red_stock:
                    processed_item['投资标的数量'] = red_stock

        # 3. V1.3版本：投资者账号字段优化
        if '投资者账号' in processed_item and processed_item['投资者账号'] == '/':
            account_value = extract_field_from_markdown(original_markdown, ['基金账号', '交易账号', '客户账号', '资金账号'])
            if account_value:
                processed_item['投资者账号'] = account_value

        # 4. 修复数值字段
        numeric_fields = ['投资标的金额', '投资标的数量', '交易费用']

        for field in numeric_fields:
            if field in processed_item and processed_item[field] == '/':
                # 尝试从markdown中重新提取数值
                extracted_value = extract_numeric_value_from_markdown(
                    original_markdown, field, processed_item
                )
                if extracted_value:
                    processed_item[field] = extracted_value

        # 5. 确保所有字段都存在
        required_fields = [
            '投资者名称', '投资者账号', '业务日期', '业务类型',
            '投资标的名称', '投资标的代码', '投资标的金额',
            '投资标的数量', '交易费用'
        ]

        for field in required_fields:
            if field not in processed_item:
                processed_item[field] = '/'

        processed_data.append(processed_item)

    # V1.3版本：应用后处理格式化
    processed_data = post_process_result(processed_data)

    return processed_data

# V1.3版本新增：PNG文件优化处理函数
def process_png_file_optimized(fn, usage_model):
    """
    PNG文件优化处理函数，提高识别准确率
    """
    print(f"开始优化处理PNG文件: {fn}")

    # 尝试多种OCR策略
    strategies = [
        {"convert_to_scanned": False, "ai_seal": True, "description": "直接处理模式"},
        {"convert_to_scanned": True, "ai_seal": True, "description": "扫描模式"},
    ]

    best_result = None
    best_length = 0

    for i, strategy in enumerate(strategies):
        try:
            print(f"尝试策略 {i+1}: {strategy['description']}")
            markdown_content, seal_img_list = fn_to_markdown_v2(
                fn,
                convert_to_scanned=strategy["convert_to_scanned"],
                ai_seal=strategy["ai_seal"],
                ai_model=usage_model
            )

            # 评估结果质量（基于内容长度和关键词）
            content_length = len(markdown_content.strip())
            key_indicators = ['金额', '数量', '费用', '账号', '日期', '名称', '代码']
            key_score = sum(1 for key in key_indicators if key in markdown_content)

            quality_score = content_length + key_score * 100
            print(f"策略 {i+1} 质量评分: {quality_score} (长度: {content_length}, 关键词: {key_score})")

            if quality_score > best_length:
                best_result = (markdown_content, seal_img_list)
                best_length = quality_score
                print(f"策略 {i+1} 效果更好，已更新最佳结果")

        except Exception as e:
            print(f"策略 {i+1} 失败: {str(e)}")
            continue

    if best_result:
        print(f"PNG文件处理完成，最佳质量评分: {best_length}")
        return best_result
    else:
        print("所有策略都失败，使用默认处理")
        return fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model=usage_model)

# V1.3版本新增：字段提取函数
def extract_field_from_markdown(markdown_content, keywords):
    """从markdown内容中根据关键词提取字段值"""
    try:
        lines = markdown_content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否包含关键词
            for keyword in keywords:
                if keyword in line:
                    # 尝试提取冒号后的值
                    if ':' in line or '：' in line:
                        parts = re.split('[：:]', line)
                        if len(parts) >= 2:
                            value = parts[1].strip()
                            # 去除金融标识符并格式化
                            formatted_value = format_numeric_value(value)
                            if formatted_value != "/":
                                return formatted_value

                    # 尝试提取数值（包含千分位分隔符）
                    number_pattern = r'[-]?(?:\d{1,3}(?:,\d{3})*|\d+)(?:\.\d+)?'
                    matches = re.findall(number_pattern, line)
                    if matches:
                        return format_numeric_value(matches[0])

        return None

    except Exception as e:
        print(f"提取字段时出错: {str(e)}")
        return None

def extract_numeric_value_from_markdown(markdown_content, field_name, item_context):
    """
    从markdown内容中提取数值
    """
    try:
        # 根据字段类型定义搜索关键词
        field_keywords = {
            '投资标的金额': ['金额', '确认金额', '交易金额', '成交金额', '申购金额', '赎回金额', '分红金额'],
            '投资标的数量': ['数量', '份额', '确认份额', '交易份额', '申购份额', '赎回份额'],
            '交易费用': ['费用', '手续费', '管理费', '申购费', '赎回费', '交易费用']
        }
        
        keywords = field_keywords.get(field_name, [])
        
        # 在markdown中搜索相关数值
        lines = markdown_content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否包含相关关键词
            has_keyword = any(keyword in line for keyword in keywords)
            
            if has_keyword:
                # 提取数值（支持千分位分隔符）
                patterns = [
                    r'-?(?:\d{1,3}(?:,\d{3})*|\d+)\.\d+',  # 带小数点和千分位的数值
                    r'-?(?:\d{1,3}(?:,\d{3})*|\d+)',       # 整数（含千分位）
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, line)
                    if matches:
                        # 返回第一个匹配的数值，去除千分位分隔符
                        raw_value = matches[0]
                        clean_value = raw_value.replace(',', '')
                        return clean_value
        
        return None
        
    except Exception as e:
        print(f"提取数值时出错: {str(e)}")
        return None

# V1.3版本新增：自定义markdown表格生成函数
def create_custom_markdown_table(data, headers):
    """创建自定义Markdown表格，确保数值精度不丢失，V1.3版本优化"""
    if not data or not headers:
        return ""

    # 创建表头
    header_row = "| " + " | ".join(str(h) for h in headers) + " |"
    separator_row = "| " + " | ".join(["---"] * len(headers)) + " |"

    # 创建数据行
    data_rows = []
    for row in data:
        row_values = []
        for value in row:
            if value is None:
                row_values.append("")
            else:
                # 确保完全按照原始字符串输出，保持引号原样
                original_str = clean_quotes(str(value).strip())
                row_values.append(original_str)
        data_rows.append("| " + " | ".join(row_values) + " |")

    # 组合表格
    table_lines = [header_row, separator_row] + data_rows
    return "\n".join(table_lines) + "\n\n"

def run(fn):
    usage_model = 'InternVL3-38B'

    # 判断文件类型
    file_ext = os.path.splitext(fn)[1].lower()

    # 对Excel文件特殊处理 - V1.3版本优化
    if file_ext in ['.xlsx', '.xls']:
        try:
            # 根据文件扩展名选择合适的引擎
            if file_ext == '.xls':
                # .xls文件使用xlrd引擎
                df = pd.read_excel(fn, dtype=str, engine='xlrd')
            else:
                # .xlsx文件使用openpyxl引擎
                df = pd.read_excel(fn, dtype=str, engine='openpyxl')

            # 处理NaN值，但保持其他值的原始格式
            df = df.fillna('')  # 将NaN替换为空字符串

            # V1.3版本：使用优化的markdown生成
            markdown_content = "# Excel文件内容\n\n**V1.3版本重要提示：以下表格中的所有数值必须严格按照显示的格式进行提取，包括所有小数位数和负号，不得进行任何精度优化！**\n\n"

            # 使用自定义表格生成函数，避免pandas的数值格式化
            headers = df.columns.tolist()
            data = df.values.tolist()

            markdown_content += create_custom_markdown_table(data, headers)

            # V1.3版本：添加特殊提示
            markdown_content += "\n\n**【V1.3数据精度检查】**：请严格按照上表中显示的数值进行提取，特别注意小数位数！去除金融标识符（如￥、$等），保持中英文引号原样！\n"

        except Exception as e:
            print(f"Excel文件读取错误: {str(e)}")
            raise Exception(f"Excel文件读取错误: {str(e)}")

    # 对Word文件特殊处理 - V1.3版本优化
    elif file_ext in ['.docx', '.doc']:
        try:
            import docx2txt
            # 提取文本内容
            text = docx2txt.process(fn)
            # V1.3版本：保持引号原样
            text = clean_quotes(text)
            markdown_content = f"# Word文档内容\n\n{text}"
        except ImportError:
            print("缺少docx2txt库，请安装: pip install docx2txt")
            raise Exception("缺少docx2txt库，请安装: pip install docx2txt")
        except Exception as e:
            print(f"Word文件读取错误: {str(e)}")
            raise Exception(f"Word文件读取错误: {str(e)}")

    # PDF和图片文件使用原有逻辑 - V1.3版本完整支持.png
    else:
        if fn.endswith(".pdf"):
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=True, ai_model=usage_model)
        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:
            # V1.3版本：PNG文件优化处理
            print(f"处理图片文件: {file_ext}")

            # 对于PNG文件，使用优化的处理函数
            if file_ext == '.png':
                markdown_content, seal_img_list = process_png_file_optimized(fn, usage_model)
            else:
                # 其他图片格式使用原有参数
                markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
        else:
            # 其他格式的图片文件
            markdown_content, seal_img_list = fn_to_markdown_v2(fn, convert_to_scanned=True, ai_seal=True, ai_model=usage_model)
    
    # 如果是Excel或Word文件，跳过VLM OCR处理
    if file_ext not in ['.xlsx', '.xls', '.docx', '.doc']:
        # V1.3版本：PNG文件OCR优化
        print("开始补充OCR处理...")

        # 对于PNG文件，使用更详细的OCR提示词
        if file_ext == '.png':
            ocr_prompt = """你是一名专业的OCR专家，特别擅长处理PNG格式的图片。请仔细分析图片内容，重点提取：
1. 所有数值信息（特别注意千分位分隔符，如2,123.57应识别为2123.57）
2. 表格数据和结构化信息
3. 印章、落款日期等关键信息
4. 投资者名称、账号等身份信息
5. 业务类型、交易金额等交易要素
请确保数值提取的准确性，不要遗漏任何重要信息。"""
            max_tokens = 300
        else:
            ocr_prompt = "你是一名OCR专家。请从用户提供的图片中提取有关印章信息、落款日期的全部有效信息"
            max_tokens = 100

        # VL2OCR
        ocr_bot = ChatBot(model='InternVL3-38B')
        img_list = process_file_to_base64(fn)
        vlm_info = ocr_bot.chat_with_img(ocr_prompt, img_url=img_list, max_tokens=max_tokens)
        markdown_content += f"\n# 补充信息：\n{vlm_info}"
        print("补充OCR处理完成")
    
    sys_prompt = """你是一位资深的银行托管部经理，负责从客户提供的非标交易确认单中**抽取交易要素**，并按以下要求结构化输出 JSON 数组。

**【V1.3版本关键提醒】**：在处理数值时，你必须像复制粘贴一样，完全按照原文档中显示的字符进行提取，不得进行任何数值处理！

=====================【V1.3版本字段定义】=====================
1. 【投资者名称】：通常指代客户姓名，一般是资管计划的名称
2. 【投资者账号】：通常指代客户的资金账号，对应文档中的"基金账号"、"交易账号"、"客户账号"等字段
3. 【业务日期】：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 【业务类型】：需要提取文件中代表当前类型的文字，并映射到下面的选项中：分红、红利转投、买入、卖出、认购、申购、赎回
5. 【投资标的名称】：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 【投资标的代码】：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 【投资标的金额】：实际交易的确认金额（缺失填"/"）
8. 【投资标的数量】：文档中可能用份额来描述（缺失填"/"）
9. 【交易费用】：一般申购、赎回、买入、卖出交易中，会标明交易费用（缺失填"/"）

=====================【V1.3版本特殊处理规则】=====================

**金额识别规则：**
- 识别金额时，自动去除金融标识符（如￥、$、€、£等），只保留数字部分
- 例如：￥5010317.83 → 5010317.83

**数值格式要求：**
- 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
- 例如：1000000 → "1000000.00"，1000000.5 → "1000000.50"
- 如果字段不存在或为空，填写"/"

**引号处理：**
- 中文引号""保持原样，不要转换为英文引号
- 英文引号""保持原样，不要转换为中文引号

**特殊场景处理（分红）：**
- 【投资标的金额】：若文档中"红利"字段存在且非空，优先提取"红利"作为标的金额
- 【投资标的数量】：若文档中"红股"字段存在且非空，提取"红股"作为标的数量
- 【交易费用】：仅在申购、赎回、买入、卖出等交易类型中提取费用字段，分红等场景填写"/"

=====================【V1.3版本业务类型识别规则】=====================
**重要：必须严格按照以下规则识别业务类型，并映射到标准术语**

**标准业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回

**识别关键词映射表**：
• **分红** ← 关键词：分红、股息、利息分配、收益分配、现金分红、分红派息
• **红利转投** ← 关键词：红利转投、分红转投、红利再投资、分红再投资、红利转份额、分红转份额
• **买入** ← 关键词：买入、购买、投资、成交买入
• **卖出** ← 关键词：卖出、出售、变现、成交卖出、兑付、到期兑付
• **认购** ← 关键词：认购、首次认购、新发认购、认购确认
• **申购** ← 关键词：申购、追加申购、申购确认、增购
• **赎回** ← 关键词：赎回、赎回确认、提取、退出、部分赎回、全部赎回

**V1.3版本识别步骤**：
1. 首先在文档中寻找明确的业务类型标识（如"业务类型"、"交易类型"、"操作类型"等字段）
2. 如果没有明确字段，则根据文档标题、内容描述中的关键词进行识别
3. 将识别到的原始词汇映射到上述7个标准业务类型之一
4. 如果无法确定，优先根据文档的主要内容和金额流向判断
5. **特别注意**：分红场景要优先查找"红利"和"红股"字段

=====================【数值提取增强规则】=====================
**针对投资标的金额、投资标的数量、交易费用字段的特殊处理**：

1. **多重查找策略**：
   - 优先查找明确标注的字段（如"确认金额"、"交易金额"、"份额"、"数量"等）
   - 查找表格中的数值列
   - 查找文本中的货币符号后的数值（¥、￥、RMB等）
   - 查找带有千分位分隔符的数值（如1,234,567.89）

2. **数值识别模式**：
   - 正数：123456.78、1,234,567.89、￥123,456.78
   - 负数：-123456.78、-1,234,567.89、-￥123,456.78
   - 零值：0、0.00、0.000等

3. **容错处理**：
   - 如果某个数值字段在文档中确实存在但难以精确定位，不要填写"/"
   - 仔细查看表格的每一行每一列
   - 注意数值可能出现在不同的位置（表头、表尾、备注等）

4. **精度保持**：
   - 完全按照原文档显示的格式输出，包括所有小数位
   - 保留千分位分隔符（如果原文档有的话）
   - 保留负号和货币符号前缀

=====================【V1.3版本格式要求】=====================
• 日期全部转为 `YYYY-MM-DD` 格式
• **V1.3版本数值格式要求**：投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
• 去除金融标识符（￥、$等），只保留数字部分
• 保持中英文引号原样，不进行转换
• 业务类型必须是以下7个之一：分红、红利转投、买入、卖出、认购、申购、赎回
• 输出 **JSON 数组**，字段顺序固定，V1.3版本示例如下：

```json
[
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-15",
    "业务类型": "申购",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "100000.00",
    "投资标的数量": "10000.00",
    "交易费用": "150.00"
  },
  {
    "投资者名称": "XX资产管理计划",
    "投资者账号": "123456789",
    "业务日期": "2025-01-16",
    "业务类型": "分红",
    "投资标的名称": "XX基金",
    "投资标的代码": "000001",
    "投资标的金额": "5000.00",
    "投资标的数量": "/",
    "交易费用": "/"
  }
]
```

**【V1.3版本严格精度要求 - 必须遵守】**
1. 如果文档中确实没有相关信息，对应字段填写"/"
2. **V1.3版本数值格式要求（绝对不可违反）**：
   - 投资标的金额、投资标的数量、交易费用必须保留两位小数的字符串格式
   - 例如：原文档显示"329961736.14"，输出"329961736.14"
   - 例如：原文档显示"100"，输出"100.00"
   - 例如：原文档显示"￥5010317.83"，输出"5010317.83"
   - 去除金融标识符，但保留数值精度
3. **V1.3版本引号处理（绝对不可违反）**：
   - 中文引号""保持原样，不要转换为英文引号
   - 英文引号""保持原样，不要转换为中文引号
   - **千分位分隔符处理**：原文档显示"2,123.57"，输出"2123.57"（不是"2.12"）
   - **千分位分隔符处理**：原文档显示"1,234,567.89"，输出"1234567.89"（不是"1.23"）
   - 去除金融标识符和千分位分隔符，但保留数值精度
4. **V1.3版本特殊场景处理**：
   - 分红场景：优先查找"红利"字段作为投资标的金额
   - 分红场景：优先查找"红股"字段作为投资标的数量
   - 分红场景：交易费用字段填写"/"
   - 投资者账号：优先查找"基金账号"或"交易账号"字段

**【V1.3版本特别注意】**：
- 仔细检查文档中的每个表格、每行数据
- 数值字段绝对不能轻易填写"/"，除非确实在文档中找不到任何相关数值
- 业务类型必须严格按照映射规则转换为标准术语
- 所有数值必须格式化为两位小数的字符串格式
"""

    chat_bot = ChatBot(system_prompt=sys_prompt, model='InternVL3-38B')

    res = chat_bot.chat(markdown_content, top_p=0.75, temperature=0.3)
    json_data = markdown_json_to_dict(res)

    # V1.3版本：后处理优化
    json_data = post_process_json_data(json_data, markdown_content)

    # V1.3版本：最终格式验证
    print("=== V1.3版本数值格式验证 ===")
    if isinstance(json_data, list):
        for i, item in enumerate(json_data):
            if isinstance(item, dict):
                print(f"交易 {i+1}:")
                for field in ['投资标的金额', '投资标的数量', '交易费用']:
                    if field in item:
                        value = item[field]
                        print(f"  {field}: {value} (类型: {type(value).__name__})")
                        if value == "/":
                            print(f"    ✓ 空值格式正确")
                        elif isinstance(value, str) and re.match(r'^-?\d+\.\d{2}$', value):
                            print(f"    ✓ 两位小数格式正确")
                        else:
                            print(f"    ⚠ 格式可能不符合V1.3要求")

    return json_data

def get_full_fn_list():
    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    
    # 只读取POC脱敏材料下的文件
    poc_path = os.path.join(project_root, "大模型样例", "POC脱敏材料")
    
    # V1.3版本：完整支持.png格式和其他图片格式
    file_types = ['.pdf', '.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.tif', '.webp', '.xlsx', '.xls', '.docx']

    full_fn_list = []
    
    print(f"=== 开始扫描文件 ===")
    print(f"项目根目录: {project_root}")
    print(f"POC脱敏材料路径: {poc_path}")
    print(f"支持的文件类型: {file_types}")
    print()
    
    # 检查并添加POC脱敏材料下的相关文件
    print("=== 扫描POC脱敏材料文件夹 ===")
    poc_subdirs = ['非标红利转投（脱敏）', '非标卖出（脱敏）', '非标分红（脱敏）', 
                   '非标买入（脱敏）', '赎回确认（脱敏）', '认购确认（脱敏）']
    
    total_files = 0
    for subdir in poc_subdirs:
        subdir_path = os.path.join(poc_path, subdir)
        print(f"检查文件夹: {subdir_path}")
        if os.path.exists(subdir_path):
            print(f"  文件夹存在，开始读取文件...")
            file_count = 0
            for fn in os.listdir(subdir_path):
                if os.path.splitext(fn)[1].lower() in file_types:
                    full_fn_list.append(os.path.join(subdir_path, fn))
                    file_count += 1
                    print(f"    ✓ 添加文件: {fn}")
            print(f"  文件夹 {subdir} 共添加 {file_count} 个文件")
            total_files += file_count
        else:
            print(f"  文件夹不存在: {subdir_path}")
    
    print()
    print(f"=== 扫描完成 ===")
    print(f"总共找到 {len(full_fn_list)} 个文件 (应为 {total_files} 个)")
    print()

    return full_fn_list

def test_al(cache_answer=False):
    """
    测试所有文件，并保存答案
    """
    # 构建分支号
    var = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    full_fn_list = get_full_fn_list()
    
    if not full_fn_list:
        print("未找到任何可处理的文件！")
        return
    
    print(f"找到 {len(full_fn_list)} 个文件待处理")
    
    # 文件md5与原始文件名（非完整路径）的映射
    fn_md5_dict = {}
    deal_fn_list = []
    
    # 获取当前脚本所在目录的上级目录（项目根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)
    
    check_dir = os.path.join(project_root, "check", DIR_NAME, var)
    os.makedirs(check_dir, exist_ok=True)
    
    # 确保answer目录存在
    answer_dir = os.path.join(project_root, "answer", DIR_NAME)
    os.makedirs(answer_dir, exist_ok=True)
    
    if not cache_answer:
        # 将run函数的内容写入check_dir
        with open(os.path.join(check_dir, "run.py"), "w", encoding='utf-8') as f:
            try:
                # 优先尝试获取函数源代码
                source = inspect.getsource(run)
                f.write(source)
            except (TypeError, OSError):
                # 备选方案：创建带注释的存根文件
                f.write("# run函数未找到有效源代码\n")
                f.write("def run():\n    # 函数实现未捕获\n    pass\n")

    for fn in tqdm.tqdm(full_fn_list):
        try:
            file_md5 = cal_fn_md5(fn)
            answer_fn = os.path.join(project_root, "answer", DIR_NAME, f"{file_md5}.json")
            check_fn = os.path.join(check_dir, f"{file_md5}.json")
            
            if cache_answer:
                if os.path.exists(answer_fn):
                    continue
                else:
                    save_fn = answer_fn
            else:
                save_fn = check_fn
                
            fn_md5_dict[file_md5] = fn
            
            # 添加重试机制
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    json_data = run(fn)
                    break
                except Exception as e:
                    if attempt < max_retries - 1:
                        print(f"处理文件 {fn} 时出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                        time.sleep(2)  # 等待2秒后重试
                    else:
                        print(f"处理文件 {fn} 失败，已达到最大重试次数: {str(e)}")
                        # 创建错误响应
                        json_data = {
                            "error": f"处理失败: {str(e)}",
                            "file": fn,
                            "timestamp": datetime.datetime.now().isoformat()
                        }
            
            deal_fn_list.append(fn)
            
            with open(save_fn, "w", encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=4)
                
        except Exception as e:
            print(f"处理文件 {fn} 时发生严重错误: {str(e)}")
            # 继续处理下一个文件，不中断整个流程
            continue

    # 根据cache_answer参数决定fn_md5_dict.json的保存位置
    if cache_answer:
        # 选项1：保存到answer目录
        dict_save_dir = os.path.join(project_root, "answer", DIR_NAME)
        os.makedirs(dict_save_dir, exist_ok=True)
    else:
        # 选项2：保存到check目录（带时间戳）
        dict_save_dir = check_dir
    
    with open(os.path.join(dict_save_dir, "fn_md5_dict.json"), "w", encoding='utf-8') as f:
        json.dump(fn_md5_dict, f, ensure_ascii=False, indent=4)
    
    if len(deal_fn_list) > 0:
        print(f"所有答案预存完成，共处理 {len(deal_fn_list)} 个文件！")
        print(f"结果保存在: {check_dir}")
    else:
        print("所有答案都已缓存，无新生成的答案！")

if __name__ == "__main__":
    select_but = input("请选择操作：\n1. 生成标准答案\n2. 生成测试答案\n")
    if select_but == "1":
        test_al(cache_answer=True)
    elif select_but == "2":
        test_al(cache_answer=False)
    else:
        print("无效选择！")

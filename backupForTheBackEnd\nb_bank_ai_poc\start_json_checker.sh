#!/bin/bash

# JSON文件校对系统启动脚本
# 智能体门户系统 v1.1.0

echo "=========================================="
echo "        JSON文件校对系统 v1.1.0"
echo "        智能体门户系统"
echo "=========================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python3"
    exit 1
fi

# 安装依赖
echo "检查并安装依赖包..."
pip install -r requirements.txt \
  --trusted-host pypi.tuna.tsinghua.edu.cn \
  --trusted-host pypi.org \
  --trusted-host files.pythonhosted.org

# 检查必要的文件夹
echo "检查必要的文件夹..."
mkdir -p answer
mkdir -p check
mkdir -p templates
mkdir -p static/css
mkdir -p static/js

echo ""
echo "🚀 功能特性："
echo "   ✅ 支持嵌套JSON结构比较"
echo "   ✅ 时间目录管理"
echo "   ✅ 联动滚动查看"
echo "   ✅ 智能错误定位"
echo "   ✅ 统计分析报告"
echo ""

# 启动应用
echo "启动JSON校对系统..."
echo "访问地址: http://localhost:5000"
echo "按 Ctrl+C 停止服务"
echo "=========================================="

python3 json_checker_app.py 
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 - 深邃钢铁蓝与午夜灰 */
    --primary-dark: #0A1F44;
    --secondary-dark: #121925;
    --accent-silver: #C0C0C0;
    --accent-gold: #FFD700;
    
    /* 扩展色彩 */
    --dark-blue: #1a2332;
    --medium-blue: #2a3441;
    --light-blue: #3a4651;
    --text-primary: #F5F5F5;
    --text-secondary: #A0A0A0;
    --text-accent: #4FC3F7;
    
    /* 状态色彩 */
    --success-color: #00C851;
    --warning-color: #FF8800;
    --error-color: #CC0000;
    --info-color: #33B5E5;
    
    /* 阴影与特效 */
    --glass-shadow: 0 8px 32px rgba(10, 31, 68, 0.37);
    --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    --hover-shadow: 0 8px 24px rgba(79, 195, 247, 0.2);
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, #0A1F44 0%, #121925 100%);
    --gradient-card: linear-gradient(145deg, rgba(42, 52, 65, 0.8) 0%, rgba(26, 35, 50, 0.9) 100%);
    --gradient-accent: linear-gradient(90deg, #4FC3F7 0%, #FFD700 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: var(--gradient-primary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    font-weight: 400;
}

/* 应用容器 */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部导航 */
.top-nav {
    background: rgba(26, 35, 50, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
    padding: 0 2rem;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--card-shadow);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--accent-gold);
}

.logo i {
    font-size: 1.5rem;
}

.nav-title {
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-primary);
    padding-left: 2rem;
    border-left: 1px solid rgba(79, 195, 247, 0.3);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.user-avatar {
    font-size: 1.8rem;
    color: var(--accent-silver);
    cursor: pointer;
    transition: color 0.3s ease;
}

.user-avatar:hover {
    color: var(--accent-gold);
}

/* 主内容区 */
.main-content {
    flex: 1;
    display: flex;
    min-height: calc(100vh - 70px);
}

/* 侧边栏 */
.sidebar {
    width: 320px;
    background: rgba(26, 35, 50, 0.8);
    backdrop-filter: blur(10px);
    border-right: 1px solid rgba(79, 195, 247, 0.2);
    padding: 2rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2.5rem;
}

.sidebar-section h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-accent);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.folder-selector {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    align-items: center;
}

.folder-selector .folder-dropdown {
    flex: 1;
    min-width: 0; /* 确保下拉框可以收缩 */
}

.folder-selector .btn-icon {
    flex-shrink: 0; /* 确保按钮不会被压缩 */
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.time-selector {
    margin-bottom: 1rem;
}

.selector-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.folder-dropdown {
    flex: 1;
    background: var(--medium-blue);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    padding: 0.8rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.folder-dropdown:focus {
    outline: none;
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.btn-icon {
    background: var(--medium-blue);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    color: var(--text-accent);
    padding: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: var(--light-blue);
    transform: translateY(-1px);
}

.folder-info {
    background: rgba(42, 52, 65, 0.6);
    border-radius: 8px;
    padding: 1rem;
    border-left: 3px solid var(--text-accent);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.label {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.value {
    font-weight: 500;
    color: var(--text-primary);
}

.status-ready {
    color: var(--success-color);
}

/* 按钮样式 */
.btn-primary {
    width: 100%;
    background: var(--gradient-accent);
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    color: var(--primary-dark);
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-primary:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: var(--medium-blue);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    padding: 0.7rem 1.2rem;
    color: var(--text-primary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background: var(--light-blue);
    border-color: var(--text-accent);
    transform: translateY(-1px);
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

/* 欢迎界面 */
.welcome-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
}

.welcome-content {
    text-align: center;
    max-width: 800px;
}

.welcome-icon {
    font-size: 4rem;
    color: var(--text-accent);
    margin-bottom: 1.5rem;
}

.welcome-content h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-content p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
    border-color: var(--text-accent);
}

.feature-card i {
    font-size: 2.5rem;
    color: var(--text-accent);
    margin-bottom: 1rem;
}

.feature-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 加载界面 */
.loading-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
}

.loading-content {
    text-align: center;
    max-width: 400px;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(79, 195, 247, 0.2);
    border-top: 4px solid var(--text-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.loading-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-accent);
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* 统计概览界面 */
.summary-screen {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.summary-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.summary-actions {
    display: flex;
    gap: 1rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.metric-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 16px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-accent);
}

.metric-card.primary::before {
    background: linear-gradient(90deg, #4FC3F7 0%, #2196F3 100%);
}

.metric-card.secondary::before {
    background: linear-gradient(90deg, #00C851 0%, #4CAF50 100%);
}

.metric-card.accent::before {
    background: linear-gradient(90deg, #FFD700 0%, #FFC107 100%);
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.metric-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.metric-header i {
    font-size: 1.5rem;
    color: var(--text-accent);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-accent);
    margin-bottom: 0.5rem;
}

.metric-detail {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* 图表容器 */
.charts-container {
    margin-top: 2rem;
}

.chart-card {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 16px;
    padding: 2rem;
}

.chart-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.field-accuracy-chart {
    max-height: 400px;
    overflow-y: auto;
}

.field-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.8rem;
    background: rgba(42, 52, 65, 0.6);
    border-radius: 8px;
    border-left: 4px solid var(--text-accent);
    transition: all 0.3s ease;
}

.field-item:hover {
    background: rgba(58, 70, 81, 0.8);
    transform: translateX(5px);
}

.field-name {
    font-weight: 500;
    color: var(--text-primary);
    flex: 1;
}

.field-accuracy {
    font-weight: 600;
    font-size: 1.1rem;
    margin-right: 1rem;
}

.accuracy-bar {
    width: 120px;
    height: 8px;
    background: rgba(79, 195, 247, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.accuracy-fill {
    height: 100%;
    background: var(--gradient-accent);
    border-radius: 4px;
    transition: width 0.8s ease-in-out;
}

/* 详细对比界面 */
.detail-screen {
    animation: fadeIn 0.5s ease-in-out;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.detail-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.detail-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
}

.search-box input {
    background: var(--medium-blue);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    padding: 0.8rem 1rem 0.8rem 2.5rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 250px;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--text-accent);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
}

.files-container {
    display: grid;
    grid-template-columns: 400px 1fr;
    gap: 2rem;
    height: calc(100vh - 200px);
}

.files-list {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    overflow-y: auto;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    margin-bottom: 0.8rem;
    background: rgba(42, 52, 65, 0.6);
    border-radius: 8px;
    border-left: 4px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-item:hover {
    background: rgba(58, 70, 81, 0.8);
    transform: translateX(3px);
}

.file-item.active {
    background: rgba(79, 195, 247, 0.1);
    border-left-color: var(--text-accent);
}

.file-item.correct {
    border-left-color: var(--success-color);
}

.file-item.incorrect {
    border-left-color: var(--error-color);
}

.file-info {
    flex: 1;
}

.file-name {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.file-stats {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.file-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-icon {
    font-size: 1.2rem;
}

.status-icon.correct {
    color: var(--success-color);
}

.status-icon.incorrect {
    color: var(--error-color);
}

.file-comparison {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    overflow-y: auto;
}

.comparison-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    text-align: center;
}

.comparison-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-accent);
}

.comparison-content {
    height: 100%;
}

.comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.comparison-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.original-filename {
    font-size: 0.9rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    padding: 0.4rem 0.8rem;
    background: rgba(79, 195, 247, 0.1);
    border-left: 3px solid var(--text-accent);
    border-radius: 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.file-original-name {
    font-size: 0.8rem;
    font-weight: 400;
    color: var(--text-secondary);
    margin-top: 0.3rem;
    padding: 0.2rem 0.5rem;
    background: rgba(79, 195, 247, 0.08);
    border-left: 2px solid var(--text-accent);
    border-radius: 3px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    line-height: 1.3;
}

.comparison-body {
    height: calc(100% - 80px);
    overflow-y: auto;
}

.json-comparison {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    height: 100%;
}

.json-panel {
    background: rgba(26, 35, 50, 0.8);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 8px;
    overflow: hidden;
}

.panel-header {
    background: var(--medium-blue);
    padding: 0.8rem 1rem;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 1px solid rgba(79, 195, 247, 0.2);
}

.panel-header.standard {
    color: var(--success-color);
}

.panel-header.check {
    color: var(--info-color);
}

.panel-content {
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    height: calc(100% - 50px);
    overflow-y: auto;
    scroll-behavior: smooth;
    will-change: scroll-position;
    transform: translateZ(0); /* 启用硬件加速 */
}

.json-field {
    margin-bottom: 0.8rem;
    padding: 0.8rem;
    border-radius: 6px;
    border-left: 3px solid transparent;
}

.json-field.match {
    background: rgba(0, 200, 81, 0.1);
    border-left-color: var(--success-color);
}

.json-field.mismatch {
    background: rgba(204, 0, 0, 0.1);
    border-left-color: var(--error-color);
}

.json-field.missing {
    background: rgba(255, 136, 0, 0.1);
    border-left-color: var(--warning-color);
}

.field-path {
    font-weight: 600;
    color: var(--text-accent);
    font-size: 0.8rem;
    margin-bottom: 0.3rem;
}

.field-value {
    color: var(--text-primary);
    word-break: break-all;
}

.field-value.null {
    color: var(--text-secondary);
    font-style: italic;
}

/* Toast 提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    background: var(--gradient-card);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(79, 195, 247, 0.2);
    border-radius: 8px;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    box-shadow: var(--card-shadow);
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.success .toast-icon {
    color: var(--success-color);
}

.toast.error .toast-icon {
    color: var(--error-color);
}

.toast.info .toast-icon {
    color: var(--info-color);
}

.toast-message {
    color: var(--text-primary);
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .files-container {
        grid-template-columns: 350px 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 280px;
        position: fixed;
        left: -280px;
        top: 70px;
        height: calc(100vh - 70px);
        z-index: 99;
        transition: left 0.3s ease;
    }
    
    .sidebar.open {
        left: 0;
    }
    
    .content-area {
        margin-left: 0;
        padding: 1rem;
    }
    
    .files-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .detail-actions {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .search-box input {
        width: 100%;
    }
    
    .summary-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .json-comparison {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .top-nav {
        padding: 0 1rem;
        height: 60px;
    }
    
    .nav-left {
        gap: 1rem;
    }
    
    .nav-title {
        display: none;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(26, 35, 50, 0.5);
}

::-webkit-scrollbar-thumb {
    background: rgba(79, 195, 247, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(79, 195, 247, 0.5);
}

/* 选择文本样式 */
::selection {
    background: rgba(79, 195, 247, 0.3);
    color: var(--text-primary);
}

/* 焦点样式 */
*:focus {
    outline: 2px solid rgba(79, 195, 247, 0.5);
    outline-offset: 2px;
}

button:focus,
select:focus,
input:focus {
    outline: none;
}

/* 动画增强 */
.metric-card,
.feature-card,
.file-item,
.json-field {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 额外的视觉效果 */
.app-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 50%, rgba(79, 195, 247, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(79, 195, 247, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* 高亮动画效果 */
@keyframes highlight {
    0% {
        background-color: transparent;
        box-shadow: none;
    }
    50% {
        background-color: rgba(255, 215, 0, 0.3);
        box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
    }
    100% {
        background-color: transparent;
        box-shadow: none;
    }
}

/* 错误字段脉冲效果 */
.json-field.mismatch {
    position: relative;
}

.json-field.mismatch::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-left: 3px solid var(--error-color);
    opacity: 0.8;
    animation: pulse-error 2s infinite;
}

@keyframes pulse-error {
    0%, 100% {
        opacity: 0.8;
    }
    50% {
        opacity: 0.4;
    }
}

/* 滚动条样式增强 */
.panel-content::-webkit-scrollbar {
    width: 12px;
}

.panel-content::-webkit-scrollbar-track {
    background: rgba(26, 35, 50, 0.5);
    border-radius: 6px;
}

.panel-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgba(79, 195, 247, 0.6) 0%, rgba(79, 195, 247, 0.3) 100%);
    border-radius: 6px;
    border: 2px solid rgba(26, 35, 50, 0.5);
}

.panel-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgba(79, 195, 247, 0.8) 0%, rgba(79, 195, 247, 0.5) 100%);
}



/* 滚动时的平滑过渡 */
.json-comparison {
    contain: layout style paint;
}

/* 优化长列表渲染性能 */
.json-field {
    contain: layout style;
}

/* 字段过滤样式 */
.field-filters {
    margin-top: 15px;
}

.field-list {
    max-height: 300px;
    overflow-y: auto;
    background: rgba(26, 35, 50, 0.8);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    padding: 8px;
    margin-bottom: 10px;
}

.field-item {
    display: flex;
    align-items: center;
    padding: 6px 8px;
    border-radius: 6px;
    margin-bottom: 4px;
    background: rgba(26, 35, 50, 0.6);
    transition: all 0.3s ease;
}

.field-item:hover {
    background: rgba(26, 35, 50, 0.9);
}

.field-item:last-child {
    margin-bottom: 0;
}

.field-checkbox {
    margin-right: 8px;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(79, 195, 247, 0.5);
    border-radius: 4px;
    background: transparent;
    position: relative;
    transition: all 0.3s ease;
}

.field-checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.field-checkbox:checked::after {
    content: '\2713';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}

.field-checkbox:hover {
    border-color: var(--primary-color);
}

.field-label {
    flex: 1;
    font-size: 13px;
    color: var(--text-light);
    cursor: pointer;
    user-select: none;
}

.filter-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    justify-content: flex-end;
}

.filter-status {
    margin-top: 8px;
    font-size: 12px;
    min-height: 16px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.filter-status.success {
    color: #4CAF50;
}

.filter-status.error {
    color: var(--error-color);
}

.filter-status.info {
    color: var(--info-color);
}

/* 字段过滤在小屏幕上的优化 */
@media (max-width: 768px) {
    .field-list {
        max-height: 200px;
    }

    .field-item {
        padding: 8px;
    }

    .field-label {
        font-size: 14px;
    }

    .filter-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-small {
        justify-content: center;
        padding: 8px 12px;
    }
}

/* 备注编辑器样式 */
.mark-editor {
    margin-top: 15px;
}

.mark-textarea {
    width: 100%;
    min-height: 120px;
    background: rgba(26, 35, 50, 0.8);
    border: 1px solid rgba(79, 195, 247, 0.3);
    border-radius: 8px;
    color: var(--text-light);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 13px;
    line-height: 1.5;
    padding: 12px;
    resize: vertical;
    transition: all 0.3s ease;
    outline: none;
}

.mark-textarea:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(79, 195, 247, 0.1);
    background: rgba(26, 35, 50, 0.9);
}

.mark-textarea::placeholder {
    color: rgba(160, 160, 160, 0.6);
    font-style: italic;
}

.mark-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    justify-content: flex-end;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    transition: all 0.3s ease;
    font-family: 'Inter', sans-serif;
    font-weight: 500;
    text-decoration: none;
    outline: none;
}

.btn-success {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2);
}

.btn-success:hover {
    background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.2);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
    transform: translateY(-1px);
}

.mark-status {
    margin-top: 8px;
    font-size: 12px;
    min-height: 16px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.mark-status.success {
    color: #4CAF50;
}

.mark-status.error {
    color: var(--error-color);
}

.mark-status.info {
    color: var(--info-color);
}

/* 备注编辑器在小屏幕上的优化 */
@media (max-width: 768px) {
    .mark-textarea {
        min-height: 100px;
        font-size: 14px;
    }

    .mark-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-small {
        justify-content: center;
        padding: 8px 12px;
    }
} 
/**
 * JSON文件校对系统前端应用
 * 金融量化交易平台风格的现代化前端应用
 */

class JsonCheckerApp {
    constructor() {
        this.currentScreen = 'welcome';
        this.comparisonResults = null;
        this.currentFolder = null;
        this.currentTimeDir = null;
        this.selectedFile = null;
        this.syncScrolling = true;
        this.allFields = new Set();  // 所有可用的字段
        this.excludedFields = new Set();  // 被排除的字段
        
        this.initializeApp();
        this.bindEvents();
        this.loadFolders();
    }
    
    /**
     * 初始化应用
     */
    initializeApp() {
        console.log('JSON校对系统启动中...');
        this.showToast('系统初始化完成', 'success');
    }
    
    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 文件夹选择
        const folderSelect = document.getElementById('folderSelect');
        if (folderSelect) {
            folderSelect.addEventListener('change', (e) => {
                this.onFolderChange(e.target.value);
            });
        }
        
        // 时间目录选择
        const timeDirSelect = document.getElementById('timeDirSelect');
        if (timeDirSelect) {
            timeDirSelect.addEventListener('change', (e) => {
                this.onTimeDirChange(e.target.value);
            });
        }
        
        // 刷新文件夹按钮
        const refreshBtn = document.getElementById('refreshFolders');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.loadFolders();
            });
        }
        
        // 开始校对按钮
        const startBtn = document.getElementById('startComparison');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startComparison();
            });
        }
        
        // 查看详情按钮
        const viewDetailsBtn = document.getElementById('viewDetails');
        if (viewDetailsBtn) {
            viewDetailsBtn.addEventListener('click', () => {
                this.showDetailScreen();
            });
        }
        
        // 返回统计按钮
        const backBtn = document.getElementById('backToSummary');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                this.showSummaryScreen();
            });
        }
        
        // 文件搜索
        const searchInput = document.getElementById('fileSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterFiles(e.target.value);
            });
        }
        
        // 导出报告按钮
        const exportBtn = document.getElementById('exportResults');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                this.exportReport();
            });
        }
        
        // 备注相关事件
        const saveMarkBtn = document.getElementById('saveMarkBtn');
        if (saveMarkBtn) {
            saveMarkBtn.addEventListener('click', () => {
                this.saveMark();
            });
        }
        
        const clearMarkBtn = document.getElementById('clearMarkBtn');
        if (clearMarkBtn) {
            clearMarkBtn.addEventListener('click', () => {
                this.clearMark();
            });
        }

        // 字段过滤相关事件
        const saveFiltersBtn = document.getElementById('saveFilters');
        if (saveFiltersBtn) {
            saveFiltersBtn.addEventListener('click', () => {
                this.saveFieldFilters();
            });
        }
        
        const clearFiltersBtn = document.getElementById('clearFilters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFieldFilters();
            });
        }
    }
    
    /**
     * 加载文件夹列表
     */
    async loadFolders() {
        try {
            this.showToast('正在加载文件夹...', 'info');
            
            const response = await fetch('/api/folders');
            const data = await response.json();
            
            if (data.success) {
                this.populateFolderSelect(data.folders);
                this.showToast('文件夹加载完成', 'success');
            } else {
                this.showToast(`加载失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('加载文件夹失败:', error);
            this.showToast('网络错误，请检查连接', 'error');
        }
    }
    
    /**
     * 填充文件夹选择器
     */
    populateFolderSelect(folders) {
        const folderSelect = document.getElementById('folderSelect');
        if (!folderSelect) return;
        
        // 清空现有选项（保留默认选项）
        folderSelect.innerHTML = '<option value="">选择文件夹...</option>';
        
        folders.forEach(folder => {
            const option = document.createElement('option');
            option.value = folder.name;
            option.textContent = `${folder.name} (${folder.json_count} 个JSON文件)`;
            folderSelect.appendChild(option);
        });
    }
    
    /**
     * 文件夹选择变化处理
     */
    onFolderChange(folderName) {
        this.currentFolder = folderName;
        this.currentTimeDir = null;
        
        const timeSelector = document.getElementById('timeSelector');
        const folderInfo = document.getElementById('folderInfo');
        const startBtn = document.getElementById('startComparison');
        const jsonCount = document.getElementById('jsonCount');
        
        if (folderName) {
            // 获取选中文件夹的JSON文件数量
            const folderSelect = document.getElementById('folderSelect');
            const selectedOption = folderSelect.options[folderSelect.selectedIndex];
            const match = selectedOption.textContent.match(/\((\d+) 个JSON文件\)/);
            const count = match ? match[1] : '0';
            
            jsonCount.textContent = count;
            
            // 显示时间目录选择器
            timeSelector.style.display = 'block';
            this.loadTimeDirs(folderName);
            
            // 加载字段过滤配置
            this.loadFieldFilters(folderName);
            
            startBtn.disabled = true; // 需要选择时间目录后才能启用
            this.showToast(`已选择文件夹: ${folderName}`, 'info');
        } else {
            timeSelector.style.display = 'none';
            folderInfo.style.display = 'none';
            startBtn.disabled = true;
            this.hideMarkEditor();
            this.hideFieldFiltersSection();
            
            // 清空时间目录选择器
            const timeDirSelect = document.getElementById('timeDirSelect');
            if (timeDirSelect) {
                timeDirSelect.innerHTML = '<option value="">选择时间目录...</option>';
            }
        }
    }
    
    /**
     * 时间目录选择变化处理
     */
    onTimeDirChange(timeDir) {
        this.currentTimeDir = timeDir;
        
        const folderInfo = document.getElementById('folderInfo');
        const startBtn = document.getElementById('startComparison');
        const testJsonCount = document.getElementById('testJsonCount');
        const testTime = document.getElementById('testTime');
        
        if (timeDir && this.currentFolder) {
            // 获取选中时间目录的信息
            const timeDirSelect = document.getElementById('timeDirSelect');
            const selectedOption = timeDirSelect.options[timeDirSelect.selectedIndex];
            const match = selectedOption.textContent.match(/\((\d+) 个文件\)/);
            const count = match ? match[1] : '0';
            
            // 格式化时间显示
            const formattedTime = this.formatTimeDir(timeDir);
            
            testJsonCount.textContent = count;
            testTime.textContent = formattedTime;
            folderInfo.style.display = 'block';
            startBtn.disabled = false;
            
            // 显示备注编辑器并加载内容
            this.showMarkEditor();
            this.loadMark();
            
            this.showToast(`已选择测试时间: ${formattedTime}`, 'info');
        } else {
            folderInfo.style.display = 'none';
            startBtn.disabled = true;
            this.hideMarkEditor();
        }
    }
    
    /**
     * 加载时间目录列表
     */
    async loadTimeDirs(folderName) {
        try {
            const response = await fetch(`/api/time-dirs/${folderName}`);
            const data = await response.json();
            
            if (data.success) {
                this.populateTimeDirSelect(data.time_dirs);
                
                // 自动选择最新的时间目录
                if (data.time_dirs.length > 0) {
                    const timeDirSelect = document.getElementById('timeDirSelect');
                    timeDirSelect.value = data.time_dirs[0].dir_name;
                    this.onTimeDirChange(data.time_dirs[0].dir_name);
                }
            } else {
                this.showToast(`加载时间目录失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('加载时间目录失败:', error);
            this.showToast('网络错误，请检查连接', 'error');
        }
    }
    
    /**
     * 填充时间目录选择器
     */
    populateTimeDirSelect(timeDirs) {
        const timeDirSelect = document.getElementById('timeDirSelect');
        if (!timeDirSelect) return;
        
        // 清空现有选项
        timeDirSelect.innerHTML = '<option value="">选择时间目录...</option>';
        
        timeDirs.forEach(timeDir => {
            const option = document.createElement('option');
            option.value = timeDir.dir_name;
            option.textContent = `${timeDir.formatted_time} (${timeDir.json_count} 个文件)`;
            timeDirSelect.appendChild(option);
        });
    }
    
    /**
     * 格式化时间目录名称
     */
    formatTimeDir(timeDir) {
        try {
            if (timeDir.length === 14) {
                const year = timeDir.substr(0, 4);
                const month = timeDir.substr(4, 2);
                const day = timeDir.substr(6, 2);
                const hour = timeDir.substr(8, 2);
                const minute = timeDir.substr(10, 2);
                const second = timeDir.substr(12, 2);
                return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            }
        } catch (error) {
            console.error('时间格式化失败:', error);
        }
        return timeDir;
    }
    
    /**
     * 开始比较
     */
    async startComparison() {
        if (!this.currentFolder) {
            this.showToast('请先选择文件夹', 'error');
            return;
        }
        
        if (!this.currentTimeDir) {
            this.showToast('请先选择时间目录', 'error');
            return;
        }
        
        this.showLoadingScreen();
        
        try {
            const response = await fetch('/api/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_name: this.currentFolder,
                    time_dir: this.currentTimeDir
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.comparisonResults = data;
                
                // 更新可用字段列表
                if (data.details && data.details.length > 0) {
                    data.details.forEach(comparison => {
                        this.updateAvailableFields(comparison);
                    });
                }
                
                this.showSummaryScreen();
                this.showToast('校对完成！', 'success');
            } else {
                this.showToast(`校对失败: ${data.error}`, 'error');
                this.showWelcomeScreen();
            }
        } catch (error) {
            console.error('校对过程出错:', error);
            this.showToast('校对过程中发生错误', 'error');
            this.showWelcomeScreen();
        }
    }
    
    /**
     * 显示欢迎界面
     */
    showWelcomeScreen() {
        this.hideAllScreens();
        document.getElementById('welcomeScreen').style.display = 'block';
        this.currentScreen = 'welcome';
    }
    
    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        this.hideAllScreens();
        document.getElementById('loadingScreen').style.display = 'block';
        this.currentScreen = 'loading';
    }
    
    /**
     * 显示统计概览界面
     */
    showSummaryScreen() {
        if (!this.comparisonResults) return;
        
        this.hideAllScreens();
        document.getElementById('summaryScreen').style.display = 'block';
        this.currentScreen = 'summary';
        
        this.renderSummaryData();
    }
    
    /**
     * 显示详细对比界面
     */
    showDetailScreen() {
        if (!this.comparisonResults) return;
        
        this.hideAllScreens();
        document.getElementById('detailScreen').style.display = 'block';
        this.currentScreen = 'detail';
        
        this.renderDetailData();
    }
    
    /**
     * 隐藏所有界面
     */
    hideAllScreens() {
        const screens = ['welcomeScreen', 'loadingScreen', 'summaryScreen', 'detailScreen'];
        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) {
                screen.style.display = 'none';
            }
        });
    }
    
    /**
     * 渲染统计数据
     */
    renderSummaryData() {
        const summary = this.comparisonResults.summary;
        
        // 更新指标卡片
        document.getElementById('fileAccuracy').textContent = `${summary.file_accuracy.toFixed(1)}%`;
        document.getElementById('correctFiles').textContent = summary.correct_files;
        document.getElementById('totalFiles').textContent = summary.total_files;
        document.getElementById('allFieldAccuracy').textContent = `${summary.all_field_accuracy.toFixed(1)}%`;
        document.getElementById('fieldCount').textContent = Object.keys(summary.field_accuracy_stats).length;
        
        // 渲染字段准确率图表
        this.renderFieldChart(summary.field_accuracy_stats);
    }
    
    /**
     * 渲染字段准确率图表
     */
    renderFieldChart(fieldStats) {
        const chartContainer = document.getElementById('fieldChart');
        if (!chartContainer) return;
        
        chartContainer.innerHTML = '';
        
        // 按准确率排序
        const sortedFields = Object.entries(fieldStats)
            .sort(([,a], [,b]) => b - a);
        
        sortedFields.forEach(([fieldName, accuracy]) => {
            const fieldItem = document.createElement('div');
            fieldItem.className = 'field-item';
            
            const accuracyColor = this.getAccuracyColor(accuracy);
            
            fieldItem.innerHTML = `
                <div class="field-name">${fieldName}</div>
                <div class="field-accuracy" style="color: ${accuracyColor}">${accuracy.toFixed(1)}%</div>
                <div class="accuracy-bar">
                    <div class="accuracy-fill" style="width: ${accuracy}%; background: ${accuracyColor}"></div>
                </div>
            `;
            
            chartContainer.appendChild(fieldItem);
        });
    }
    
    /**
     * 获取准确率对应的颜色
     */
    getAccuracyColor(accuracy) {
        if (accuracy >= 90) return '#00C851';
        if (accuracy >= 70) return '#FF8800';
        return '#CC0000';
    }
    
    /**
     * 渲染详细数据
     */
    renderDetailData() {
        const filesList = document.getElementById('filesList');
        if (!filesList) return;
        
        filesList.innerHTML = '';
        
        const details = this.comparisonResults.details;
        
        details.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = `file-item ${file.is_identical ? 'correct' : 'incorrect'}`;
            fileItem.dataset.filename = file.filename;
            
            const statusIcon = file.is_identical ? 'fas fa-check-circle correct' : 'fas fa-times-circle incorrect';
            const accuracy = file.field_accuracy || 0;
            
            // 构建文件信息显示
            let fileInfoHtml = `<div class="file-name">${file.filename}</div>`;
            if (file.original_filename) {
                fileInfoHtml += `<div class="file-original-name">原始文件: ${file.original_filename}</div>`;
            }
            fileInfoHtml += `<div class="file-stats">准确率: ${accuracy.toFixed(1)}% | 字段: ${file.correct_fields}/${file.total_fields}</div>`;
            
            fileItem.innerHTML = `
                <div class="file-info">
                    ${fileInfoHtml}
                </div>
                <div class="file-status">
                    <div class="status-icon ${statusIcon}"></div>
                </div>
            `;
            
            fileItem.addEventListener('click', () => {
                this.showFileComparison(file);
            });
            
            filesList.appendChild(fileItem);
        });
    }
    
    /**
     * 显示文件对比详情
     */
    async showFileComparison(file) {
        // 更新选中状态
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
        
        const fileItem = document.querySelector(`[data-filename="${file.filename}"]`);
        if (fileItem) {
            fileItem.classList.add('active');
        }
        
        this.selectedFile = file.filename;
        
        // 清理之前的滚动处理器
        this.cleanupScrollHandlers();
        
        // 获取详细的比较数据
        try {
            const response = await fetch(`/api/file-detail/${file.filename}?folder=${this.currentFolder}&time_dir=${this.currentTimeDir}`);
            const data = await response.json();
            
            if (data.success) {
                this.renderFileComparison(data.comparison);
                this.showToast(`已加载文件: ${file.filename}`, 'info');
            } else {
                this.showToast(`加载文件详情失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('加载文件详情失败:', error);
            this.showToast('网络错误', 'error');
        }
    }
    
    /**
     * 渲染文件对比内容
     */
    renderFileComparison(comparison) {
        const comparisonContainer = document.getElementById('fileComparison');
        if (!comparisonContainer) return;
        
        // 构建文件名显示
        let filenameDisplay = comparison.filename;
        if (comparison.original_filename) {
            filenameDisplay += `<div class="original-filename">原始文件: ${comparison.original_filename}</div>`;
        }
        
        comparisonContainer.innerHTML = `
            <div class="comparison-content">
                <div class="comparison-header">
                    <div class="comparison-title">${filenameDisplay}</div>
                    <div class="comparison-stats">
                        准确率: ${comparison.field_accuracy.toFixed(1)}% | 
                        匹配字段: ${comparison.correct_fields}/${comparison.total_fields}
                    </div>
                </div>
                <div class="comparison-body">
                    <div class="json-comparison">
                        <div class="json-panel">
                            <div class="panel-header standard">标准答案</div>
                            <div class="panel-content" id="standardContent"></div>
                        </div>
                        <div class="json-panel">
                            <div class="panel-header check">测试结果</div>
                            <div class="panel-content" id="checkContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.renderJsonComparison(comparison.field_comparisons);
    }
    
    /**
     * 渲染JSON对比内容
     */
    renderJsonComparison(fieldComparisons) {
        const standardContent = document.getElementById('standardContent');
        const checkContent = document.getElementById('checkContent');
        
        if (!standardContent || !checkContent) return;
        
        standardContent.innerHTML = '';
        checkContent.innerHTML = '';
        
        let firstErrorElement = null;
        
        Object.entries(fieldComparisons).forEach(([fieldPath, fieldData]) => {
            const isMatch = fieldData.is_match;
            const matchClass = isMatch ? 'match' : 'mismatch';
            
            // 标准答案字段
            const standardField = document.createElement('div');
            standardField.className = `json-field ${matchClass}`;
            standardField.id = `standard-${fieldPath.replace(/\./g, '-')}`;
            standardField.innerHTML = `
                <div class="field-path">${fieldPath}</div>
                <div class="field-value ${fieldData.standard === null ? 'null' : ''}">${
                    fieldData.standard === null ? '(缺失)' : JSON.stringify(fieldData.standard, null, 2)
                }</div>
            `;
            standardContent.appendChild(standardField);
            
            // 测试结果字段
            const checkField = document.createElement('div');
            checkField.className = `json-field ${matchClass}`;
            checkField.id = `check-${fieldPath.replace(/\./g, '-')}`;
            checkField.innerHTML = `
                <div class="field-path">${fieldPath}</div>
                <div class="field-value ${fieldData.check === null ? 'null' : ''}">${
                    fieldData.check === null ? '(缺失)' : JSON.stringify(fieldData.check, null, 2)
                }</div>
            `;
            checkContent.appendChild(checkField);
            
            // 记录第一个错误元素
            if (!isMatch && !firstErrorElement) {
                firstErrorElement = standardField;
            }
        });
        
        // 设置联动滚动
        this.setupSyncScroll(standardContent, checkContent);
        
        // 自动滚动到第一个错误位置
        if (firstErrorElement) {
            setTimeout(() => {
                firstErrorElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                // 添加高亮效果
                firstErrorElement.style.animation = 'highlight 2s ease-in-out';
                const correspondingCheckField = document.getElementById(firstErrorElement.id.replace('standard-', 'check-'));
                if (correspondingCheckField) {
                    correspondingCheckField.style.animation = 'highlight 2s ease-in-out';
                }
            }, 300);
        }
    }
    
    /**
     * 设置联动滚动
     */
    setupSyncScroll(element1, element2) {
        // 使用更精确的同步控制
        let syncInProgress = false;
        let lastScrollSource = null;
        let scrollTimeout = null;
        
        const syncScroll = (source, target, sourceId) => {
            // 如果正在同步中，或者这次滚动是由同步触发的，则跳过
            if (syncInProgress) return;
            
            // 清除之前的超时
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            
            // 标记同步开始
            syncInProgress = true;
            lastScrollSource = sourceId;
            
            // 计算滚动比例，避免除零错误
            const sourceMaxScroll = Math.max(1, source.scrollHeight - source.clientHeight);
            const targetMaxScroll = Math.max(1, target.scrollHeight - target.clientHeight);
            
            if (sourceMaxScroll > 1 && targetMaxScroll > 1) {
                const scrollRatio = source.scrollTop / sourceMaxScroll;
                const newScrollTop = scrollRatio * targetMaxScroll;
                
                // 使用requestAnimationFrame确保平滑滚动
                requestAnimationFrame(() => {
                    target.scrollTop = newScrollTop;
                    
                    // 设置较短的延迟来重置同步状态
                    scrollTimeout = setTimeout(() => {
                        syncInProgress = false;
                        lastScrollSource = null;
                    }, 16); // 约一帧的时间
                });
            } else {
                // 如果没有可滚动内容，立即重置状态
                setTimeout(() => {
                    syncInProgress = false;
                    lastScrollSource = null;
                }, 16);
            }
        };
        
        // 移除之前的事件监听器
        if (element1._syncScrollHandler) {
            element1.removeEventListener('scroll', element1._syncScrollHandler);
            delete element1._syncScrollHandler;
        }
        if (element2._syncScrollHandler) {
            element2.removeEventListener('scroll', element2._syncScrollHandler);
            delete element2._syncScrollHandler;
        }
        
        // 创建新的滚动处理器，使用不同的ID来区分滚动源
        element1._syncScrollHandler = (e) => {
            if (!syncInProgress || lastScrollSource !== 'element1') {
                syncScroll(element1, element2, 'element1');
            }
        };
        
        element2._syncScrollHandler = (e) => {
            if (!syncInProgress || lastScrollSource !== 'element2') {
                syncScroll(element2, element1, 'element2');
            }
        };
        
        // 添加事件监听器，使用passive模式提高性能
        element1.addEventListener('scroll', element1._syncScrollHandler, { passive: true });
        element2.addEventListener('scroll', element2._syncScrollHandler, { passive: true });
        
        // 添加鼠标进入事件，用于更好的交互体验
        element1.addEventListener('mouseenter', () => {
            element1.style.scrollBehavior = 'auto';
        });
        
        element2.addEventListener('mouseenter', () => {
            element2.style.scrollBehavior = 'auto';
        });
        
        // 存储清理函数，用于组件销毁时清理
        if (!this._scrollCleanupFunctions) {
            this._scrollCleanupFunctions = [];
        }
        
        const cleanup = () => {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            element1.removeEventListener('scroll', element1._syncScrollHandler);
            element2.removeEventListener('scroll', element2._syncScrollHandler);
            delete element1._syncScrollHandler;
            delete element2._syncScrollHandler;
        };
        
        this._scrollCleanupFunctions.push(cleanup);
    }
    

    
    /**
     * 清理滚动相关的事件监听器
     */
    cleanupScrollHandlers() {
        if (this._scrollCleanupFunctions) {
            this._scrollCleanupFunctions.forEach(cleanup => cleanup());
            this._scrollCleanupFunctions = [];
        }
    }
    
    /**
     * 文件过滤
     */
    filterFiles(searchTerm) {
        const fileItems = document.querySelectorAll('.file-item');
        const term = searchTerm.toLowerCase();
        
        fileItems.forEach(item => {
            const filename = item.dataset.filename.toLowerCase();
            const shouldShow = filename.includes(term);
            item.style.display = shouldShow ? 'flex' : 'none';
        });
        
        if (searchTerm) {
            this.showToast(`搜索到 ${document.querySelectorAll('.file-item[style*="flex"]').length} 个文件`, 'info');
        }
    }
    
    /**
     * 导出报告
     */
    exportReport() {
        if (!this.comparisonResults) {
            this.showToast('没有可导出的数据', 'error');
            return;
        }
        
        try {
            const reportData = {
                summary: this.comparisonResults.summary,
                details: this.comparisonResults.details,
                exportTime: new Date().toISOString(),
                folder: this.currentFolder
            };
            
            const dataStr = JSON.stringify(reportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `json_comparison_report_${this.currentFolder}_${new Date().toISOString().slice(0, 10)}.json`;
            link.click();
            
            this.showToast('报告导出成功', 'success');
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showToast('导出失败', 'error');
        }
    }
    
    /**
     * 显示提示信息
     */
    showToast(message, type = 'info') {
        const toast = document.getElementById('toast');
        const icon = document.querySelector('.toast-icon');
        const messageEl = document.querySelector('.toast-message');
        
        if (!toast || !icon || !messageEl) return;
        
        // 设置图标
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle',
            warning: 'fas fa-exclamation-triangle'
        };
        
        icon.className = `toast-icon ${icons[type] || icons.info}`;
        messageEl.textContent = message;
        
        // 设置样式类
        toast.className = `toast ${type}`;
        
        // 显示Toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // 3秒后隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
        
        console.log(`[${type.toUpperCase()}] ${message}`);
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN');
    }
    
    /**
     * 显示备注编辑器
     */
    showMarkEditor() {
        const markSection = document.getElementById('markSection');
        if (markSection) {
            markSection.style.display = 'block';
        }
    }
    
    /**
     * 隐藏备注编辑器
     */
    hideMarkEditor() {
        const markSection = document.getElementById('markSection');
        if (markSection) {
            markSection.style.display = 'none';
        }
    }
    
    /**
     * 加载备注内容
     */
    async loadMark() {
        if (!this.currentFolder || !this.currentTimeDir) {
            return;
        }
        
        try {
            const response = await fetch(`/api/mark/${this.currentFolder}/${this.currentTimeDir}`);
            const data = await response.json();
            
            const markContent = document.getElementById('markContent');
            const markStatus = document.getElementById('markStatus');
            
            if (data.success) {
                if (markContent) {
                    markContent.value = data.content || '';
                }
                
                if (markStatus) {
                    if (data.exists && data.content) {
                        markStatus.innerHTML = '<i class="fas fa-check-circle"></i> 已有备注';
                        markStatus.className = 'mark-status success';
                    } else {
                        markStatus.innerHTML = '<i class="fas fa-info-circle"></i> 暂无备注';
                        markStatus.className = 'mark-status info';
                    }
                }
            } else {
                if (markStatus) {
                    markStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 加载失败: ${data.error}`;
                    markStatus.className = 'mark-status error';
                }
            }
        } catch (error) {
            console.error('加载备注失败:', error);
            const markStatus = document.getElementById('markStatus');
            if (markStatus) {
                markStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 网络错误';
                markStatus.className = 'mark-status error';
            }
        }
    }
    
    /**
     * 保存备注
     */
    async saveMark() {
        if (!this.currentFolder || !this.currentTimeDir) {
            this.showToast('请先选择文件夹和时间目录', 'error');
            return;
        }
        
        const markContent = document.getElementById('markContent');
        const markStatus = document.getElementById('markStatus');
        
        if (!markContent) {
            this.showToast('备注编辑器未找到', 'error');
            return;
        }
        
        const content = markContent.value;
        
        try {
            // 显示保存中状态
            if (markStatus) {
                markStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
                markStatus.className = 'mark-status info';
            }
            
            const response = await fetch(`/api/mark/${this.currentFolder}/${this.currentTimeDir}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ content })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message, 'success');
                
                if (markStatus) {
                    if (content.trim()) {
                        markStatus.innerHTML = '<i class="fas fa-check-circle"></i> 备注已保存';
                        markStatus.className = 'mark-status success';
                    } else {
                        markStatus.innerHTML = '<i class="fas fa-info-circle"></i> 暂无备注';
                        markStatus.className = 'mark-status info';
                    }
                }
            } else {
                this.showToast(`保存失败: ${data.error}`, 'error');
                
                if (markStatus) {
                    markStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 保存失败: ${data.error}`;
                    markStatus.className = 'mark-status error';
                }
            }
        } catch (error) {
            console.error('保存备注失败:', error);
            this.showToast('网络错误，保存失败', 'error');
            
            if (markStatus) {
                markStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 网络错误';
                markStatus.className = 'mark-status error';
            }
        }
    }
    
    /**
     * 清空备注
     */
    clearMark() {
        const markContent = document.getElementById('markContent');
        const markStatus = document.getElementById('markStatus');
        
        if (markContent) {
            markContent.value = '';
            markContent.focus();
        }
        
        if (markStatus) {
            markStatus.innerHTML = '<i class="fas fa-info-circle"></i> 备注已清空，点击保存生效';
            markStatus.className = 'mark-status info';
        }
        
        this.showToast('备注内容已清空', 'info');
    }

    /**
     * 加载字段过滤配置
     */
    async loadFieldFilters(folderName) {
        if (!folderName) return;
        
        try {
            const response = await fetch(`/api/field-filters/${folderName}`);
            const data = await response.json();
            
            if (data.success) {
                this.excludedFields = new Set(data.filters.excluded_fields || []);
                this.showFieldFiltersSection();
            } else {
                this.showToast(`加载字段过滤配置失败: ${data.error}`, 'error');
            }
        } catch (error) {
            console.error('加载字段过滤配置失败:', error);
            this.showToast('网络错误，无法加载字段过滤配置', 'error');
        }
    }

    /**
     * 显示字段过滤区域
     */
    showFieldFiltersSection() {
        const section = document.getElementById('fieldFiltersSection');
        if (!section) return;

        section.style.display = 'block';
        this.renderFieldList();
    }

    /**
     * 隐藏字段过滤区域
     */
    hideFieldFiltersSection() {
        const section = document.getElementById('fieldFiltersSection');
        if (section) {
            section.style.display = 'none';
        }
    }

    /**
     * 渲染字段列表
     */
    renderFieldList() {
        const fieldList = document.getElementById('fieldList');
        if (!fieldList) return;

        // 清空现有列表
        fieldList.innerHTML = '';

        // 如果没有字段，显示提示信息
        if (this.allFields.size === 0) {
            fieldList.innerHTML = '<div class="field-item"><span class="field-label">暂无可用字段，请先执行一次校对</span></div>';
            return;
        }

        // 按字母顺序排序字段
        const sortedFields = Array.from(this.allFields).sort();

        // 创建字段列表
        sortedFields.forEach(field => {
            const item = document.createElement('div');
            item.className = 'field-item';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'field-checkbox';
            checkbox.checked = !this.excludedFields.has(field);
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    this.excludedFields.delete(field);
                } else {
                    this.excludedFields.add(field);
                }
            });

            const label = document.createElement('label');
            label.className = 'field-label';
            label.textContent = field;
            label.addEventListener('click', () => {
                checkbox.checked = !checkbox.checked;
                checkbox.dispatchEvent(new Event('change'));
            });

            item.appendChild(checkbox);
            item.appendChild(label);
            fieldList.appendChild(item);
        });
    }

    /**
     * 保存字段过滤配置
     */
    async saveFieldFilters() {
        if (!this.currentFolder) {
            this.showToast('请先选择文件夹', 'error');
            return;
        }

        const filterStatus = document.getElementById('filterStatus');
        if (filterStatus) {
            filterStatus.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
            filterStatus.className = 'filter-status info';
        }

        try {
            const response = await fetch(`/api/field-filters/${this.currentFolder}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filters: {
                        excluded_fields: Array.from(this.excludedFields)
                    }
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showToast('字段过滤配置已保存', 'success');
                if (filterStatus) {
                    filterStatus.innerHTML = '<i class="fas fa-check-circle"></i> 配置已保存';
                    filterStatus.className = 'filter-status success';
                }
            } else {
                this.showToast(`保存失败: ${data.error}`, 'error');
                if (filterStatus) {
                    filterStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> 保存失败: ${data.error}`;
                    filterStatus.className = 'filter-status error';
                }
            }
        } catch (error) {
            console.error('保存字段过滤配置失败:', error);
            this.showToast('网络错误，保存失败', 'error');
            if (filterStatus) {
                filterStatus.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 网络错误';
                filterStatus.className = 'filter-status error';
            }
        }
    }

    /**
     * 清空字段过滤
     */
    clearFieldFilters() {
        this.excludedFields.clear();
        this.renderFieldList();

        const filterStatus = document.getElementById('filterStatus');
        if (filterStatus) {
            filterStatus.innerHTML = '<i class="fas fa-info-circle"></i> 过滤已清空，点击保存生效';
            filterStatus.className = 'filter-status info';
        }

        this.showToast('字段过滤已清空', 'info');
    }

    /**
     * 更新可用字段列表
     */
    updateAvailableFields(comparison) {
        if (!comparison || !comparison.field_comparisons) return;

        // 添加所有字段到集合中
        Object.keys(comparison.field_comparisons).forEach(field => {
            this.allFields.add(field);
        });

        // 重新渲染字段列表
        this.renderFieldList();
    }
}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    console.log('JSON校对系统界面加载完成');
    window.jsonCheckerApp = new JsonCheckerApp();
});

// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('页面错误:', event.error);
    if (window.jsonCheckerApp) {
        window.jsonCheckerApp.showToast('系统发生错误，请刷新页面', 'error');
    }
});

// 网络状态监听
window.addEventListener('online', () => {
    if (window.jsonCheckerApp) {
        window.jsonCheckerApp.showToast('网络连接已恢复', 'success');
    }
});

window.addEventListener('offline', () => {
    if (window.jsonCheckerApp) {
        window.jsonCheckerApp.showToast('网络连接已断开', 'error');
    }
}); 
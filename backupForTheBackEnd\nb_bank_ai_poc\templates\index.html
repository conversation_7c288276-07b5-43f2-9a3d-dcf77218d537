<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSON文件校对系统 - 波行POC调试平台</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航 -->
        <header class="top-nav">
            <div class="nav-left">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <span>波行POC调试平台</span>
                </div>
                <div class="nav-title">JSON文件校对平台</div>
            </div>
            <div class="nav-right">
                <div class="status-indicator">
                    <span class="status-dot"></span>
                    <span>系统运行中</span>
                </div>
                <div class="user-avatar">
                    <i class="fas fa-user-circle"></i>
                </div>
            </div>
        </header>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <div class="sidebar-section">
                    <h3><i class="fas fa-folder-open"></i> 文件夹选择</h3>
                    <div class="folder-selector">
                        <select id="folderSelect" class="folder-dropdown">
                            <option value="">选择文件夹...</option>
                        </select>
                        <button id="refreshFolders" class="btn-icon" title="刷新文件夹列表">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <div class="time-selector" id="timeSelector" style="display: none;">
                        <label class="selector-label">测试时间目录:</label>
                        <select id="timeDirSelect" class="folder-dropdown">
                            <option value="">选择时间目录...</option>
                        </select>
                    </div>
                    <div class="folder-info" id="folderInfo" style="display: none;">
                        <div class="info-item">
                            <span class="label">标准文件数:</span>
                            <span class="value" id="jsonCount">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">测试文件数:</span>
                            <span class="value" id="testJsonCount">0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">测试时间:</span>
                            <span class="value" id="testTime">--</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-play-circle"></i> 操作控制</h3>
                    <button id="startComparison" class="btn-primary" disabled>
                        <i class="fas fa-check-double"></i>
                        开始校对
                    </button>
                </div>

                <div class="sidebar-section" id="fieldFiltersSection" style="display: none;">
                    <h3><i class="fas fa-filter"></i> 字段过滤</h3>
                    <div class="field-filters">
                        <div class="field-list" id="fieldList">
                            <!-- 字段列表将动态生成 -->
                        </div>
                        <div class="filter-actions">
                            <button id="saveFilters" class="btn-small btn-success">
                                <i class="fas fa-save"></i>
                                保存过滤
                            </button>
                            <button id="clearFilters" class="btn-small btn-danger">
                                <i class="fas fa-trash"></i>
                                清空过滤
                            </button>
                        </div>
                        <div class="filter-status" id="filterStatus"></div>
                    </div>
                </div>

                <div class="sidebar-section">
                    <h3><i class="fas fa-info-circle"></i> 系统信息</h3>
                    <div class="system-info">
                        <div class="info-item">
                            <span class="label">版本:</span>
                            <span class="value">v1.0.0</span>
                        </div>
                        <div class="info-item">
                            <span class="label">状态:</span>
                            <span class="value status-ready">就绪</span>
                        </div>
                    </div>
                </div>

                <div class="sidebar-section" id="markSection" style="display: none;">
                    <h3><i class="fas fa-sticky-note"></i> 测试备注</h3>
                    <div class="mark-editor">
                        <textarea id="markContent" class="mark-textarea" placeholder="在此添加测试备注..."></textarea>
                        <div class="mark-actions">
                            <button id="saveMarkBtn" class="btn-small btn-success">
                                <i class="fas fa-save"></i>
                                保存备注
                            </button>
                            <button id="clearMarkBtn" class="btn-small btn-danger">
                                <i class="fas fa-trash"></i>
                                清空
                            </button>
                        </div>
                        <div class="mark-status" id="markStatus"></div>
                    </div>
                </div>
            </aside>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 欢迎界面 -->
                <div id="welcomeScreen" class="welcome-screen">
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <h2>JSON文件批量校对系统</h2>
                        <p>高效、精确的JSON文件比较与分析平台</p>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <i class="fas fa-search"></i>
                                <h4>智能比较</h4>
                                <p>支持嵌套JSON结构的深度比较</p>
                            </div>
                            <div class="feature-card">
                                <i class="fas fa-chart-bar"></i>
                                <h4>统计分析</h4>
                                <p>详细的准确率和字段级统计</p>
                            </div>
                            <div class="feature-card">
                                <i class="fas fa-eye"></i>
                                <h4>可视化对比</h4>
                                <p>直观的差异展示和高亮显示</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div id="loadingScreen" class="loading-screen" style="display: none;">
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <h3>正在处理文件...</h3>
                        <p>请稍候，系统正在执行批量校对</p>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                    </div>
                </div>

                <!-- 统计概览界面 -->
                <div id="summaryScreen" class="summary-screen" style="display: none;">
                    <div class="summary-header">
                        <h2><i class="fas fa-chart-pie"></i> 校对结果统计</h2>
                        <div class="summary-actions">
                            <button id="viewDetails" class="btn-secondary">
                                <i class="fas fa-list-alt"></i>
                                查看详情
                            </button>
                            <button id="exportResults" class="btn-secondary">
                                <i class="fas fa-download"></i>
                                导出报告
                            </button>
                        </div>
                    </div>

                    <div class="metrics-grid">
                        <div class="metric-card primary">
                            <div class="metric-header">
                                <h3>文件正确率</h3>
                                <i class="fas fa-file-check"></i>
                            </div>
                            <div class="metric-value" id="fileAccuracy">0%</div>
                            <div class="metric-detail">
                                <span id="correctFiles">0</span> / <span id="totalFiles">0</span> 文件完全匹配
                            </div>
                        </div>

                        <div class="metric-card secondary">
                            <div class="metric-header">
                                <h3>全字段正确率</h3>
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-value" id="allFieldAccuracy">0%</div>
                            <div class="metric-detail">正确字段总数 / 全部字段总数</div>
                        </div>

                        <div class="metric-card accent">
                            <div class="metric-header">
                                <h3>字段统计</h3>
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="metric-value" id="fieldCount">0</div>
                            <div class="metric-detail">检测到的字段类型数量</div>
                        </div>
                    </div>

                    <div class="charts-container">
                        <div class="chart-card">
                            <h3><i class="fas fa-chart-bar"></i> 各字段准确率分析</h3>
                            <div class="field-accuracy-chart" id="fieldChart">
                                <!-- 字段准确率图表将在这里动态生成 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细对比界面 -->
                <div id="detailScreen" class="detail-screen" style="display: none;">
                    <div class="detail-header">
                        <h2><i class="fas fa-microscope"></i> 详细对比分析</h2>
                        <div class="detail-actions">
                            <button id="backToSummary" class="btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                返回统计
                            </button>
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="fileSearch" placeholder="搜索文件名...">
                            </div>
                        </div>
                    </div>

                    <div class="files-container">
                        <div class="files-list" id="filesList">
                            <!-- 文件列表将动态生成 -->
                        </div>
                        <div class="file-comparison" id="fileComparison">
                            <div class="comparison-placeholder">
                                <i class="fas fa-mouse-pointer"></i>
                                <p>点击左侧文件查看详细对比</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Toast 提示 -->
    <div id="toast" class="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html> 
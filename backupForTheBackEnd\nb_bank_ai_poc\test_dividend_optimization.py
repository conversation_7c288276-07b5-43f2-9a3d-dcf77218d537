#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试红利转投文档识别优化效果
"""

import sys
import os
import json

# 添加脚本路径
sys.path.append("scripts")

def test_single_dividend_file():
    """测试单个红利转投文件"""
    
    # 导入优化后的处理函数
    try:
        from non_standard_trade_analysis_v1_5 import run
        print("✓ 成功导入优化后的处理函数")
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return
    
    # 查找红利转投测试文件
    project_root = os.path.dirname(os.path.abspath(__file__))
    dividend_dir = os.path.join(project_root, "大模型样例", "POC脱敏材料", "非标红利转投（脱敏）")
    
    if not os.path.exists(dividend_dir):
        print(f"✗ 红利转投文件夹不存在: {dividend_dir}")
        return
    
    # 查找PDF文件
    test_files = []
    for file in os.listdir(dividend_dir):
        if file.endswith('.pdf'):
            test_files.append(os.path.join(dividend_dir, file))
    
    if not test_files:
        print("✗ 未找到红利转投测试文件")
        return
    
    # 测试第一个文件
    test_file = test_files[0]
    print(f"测试文件: {os.path.basename(test_file)}")
    
    try:
        # 运行优化后的处理函数
        result = run(test_file)
        
        print("\n=== 处理结果 ===")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
        # 分析结果
        if isinstance(result, list) and len(result) > 0:
            print(f"\n✓ 成功提取 {len(result)} 笔交易")
            
            for i, transaction in enumerate(result, 1):
                print(f"\n交易 {i}:")
                print(f"  投资者名称: {transaction.get('投资者名称', 'N/A')}")
                print(f"  业务类型: {transaction.get('业务类型', 'N/A')}")
                print(f"  投资标的名称: {transaction.get('投资标的名称', 'N/A')}")
                print(f"  投资标的金额: {transaction.get('投资标的金额', 'N/A')}")
                
                # 检查关键字段
                issues = []
                if transaction.get('投资者名称') == '/':
                    issues.append("投资者名称缺失")
                elif '永诚保险' in str(transaction.get('投资者名称', '')):
                    issues.append("投资者名称错误（识别成发行方）")
                
                if transaction.get('业务类型') == '/':
                    issues.append("业务类型缺失")
                elif transaction.get('业务类型') != '红利转投':
                    issues.append("业务类型错误")
                
                if transaction.get('投资标的金额') == '/':
                    issues.append("投资标的金额缺失")
                
                if issues:
                    print(f"  ❌ 问题: {', '.join(issues)}")
                else:
                    print(f"  ✅ 字段完整")
        else:
            print("✗ 未提取到交易信息")
            
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_optimization_effect():
    """分析优化效果"""
    
    print("\n=== 优化效果分析 ===")
    
    optimizations = [
        {
            "优化项": "OCR提示词增强",
            "具体改进": [
                "专门针对红利转投文档的OCR提示词",
                "明确区分投资方和发行方",
                "强调表格结构识别",
                "增加token数量到800"
            ]
        },
        {
            "优化项": "系统提示词优化",
            "具体改进": [
                "强调投资者名称是投资方而非发行方",
                "增强红利转投业务类型识别",
                "明确字段映射规则",
                "增加多笔交易处理逻辑"
            ]
        },
        {
            "优化项": "字段识别增强",
            "具体改进": [
                "投资者名称格式明确化",
                "投资标的代码与账号区分",
                "金额和份额相等性验证",
                "多行数据逐一提取"
            ]
        }
    ]
    
    for opt in optimizations:
        print(f"\n📈 {opt['优化项']}:")
        for improvement in opt['具体改进']:
            print(f"  • {improvement}")
    
    print("\n🎯 预期改进效果:")
    print("  • 投资者名称识别准确率: 0% → 90%+")
    print("  • 业务类型识别准确率: 0% → 95%+")
    print("  • 投资标的信息完整性: 0% → 85%+")
    print("  • 数值字段提取准确率: 0% → 80%+")
    print("  • 整体文档识别准确率: <10% → 80%+")

if __name__ == "__main__":
    print("=== 红利转投文档识别优化测试 ===\n")
    
    test_single_dividend_file()
    analyze_optimization_effect()
    
    print("\n=== 测试完成 ===")
    print("如果结果仍不理想，可能需要进一步优化OCR识别或调整系统提示词")

from util_ai import ChatBot
from ocr_api import *
from util_img_process import base64_to_img, chat_bot_img, pdf_to_images, enhance_file_quality, is_scanned_pdf
import base64
import os
from PIL import Image
from io import BytesIO
import hashlib
import re
import json
    
def fn_to_markdown(fn, convert_to_scanned=False, ai_seal=False, add_seal_info=True, add_ocr_info=True, enhance_quality=False, ai_model='qwen2.5vl:3b'):
    """
    将文件转换为markdown格式，并添加印章信息和OCR识别内容
    :param fn: 文件路径
    :param convert_to_scanned: 是否将pdf转换为扫描式pdf
    :param ai_seal: 是否使用AI识别印章附近的信息
    :param ai_model: 使用哪个模型进行AI识别，默认为InternVL3-38B
    :param add_seal_info: 是否添加印章信息
    :param add_ocr_info: 是否添加OCR识别内容
    :param enhance_quality: 是否启用图片清晰度提升，默认为False
    :return: markdown格式内容
    """
    original_fn = fn  # 保存原始文件路径
    enhanced_fn = fn  # 用于存储可能增强后的文件路径
    
    # 判断是否需要提升清晰度
    should_enhance = False
    if enhance_quality:
        file_ext = os.path.splitext(fn)[1].lower()
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']
        
        if file_ext in image_extensions:
            # 图片文件需要提升清晰度
            should_enhance = True
            print("检测到图片文件，将提升清晰度")
        elif file_ext == '.pdf':
            if convert_to_scanned:
                # 设置了转换为扫描式PDF
                should_enhance = True
                print("设置了convert_to_scanned=True，将提升PDF清晰度")
            else:
                # 检测是否为扫描式PDF
                if is_scanned_pdf(fn):
                    should_enhance = True
                    print("检测到扫描式PDF，将提升清晰度")
    
    # 如果需要提升清晰度，先进行处理
    if should_enhance:
        try:
            print(f"开始提升文件清晰度：{fn}")
            enhanced_fn = enhance_file_quality(fn, scale_factor=2.0, sharpen_factor=1.5)
            print(f"清晰度提升完成：{enhanced_fn}")
        except Exception as e:
            print(f"清晰度提升失败，使用原文件：{e}")
            enhanced_fn = fn
    
    # 使用增强后的文件进行markdown转换
    markdown_data = trans_pdf_to_markdown(enhanced_fn, parse_method='auto', convert_to_scanned=convert_to_scanned)
    markdown_content = ""
    for fn_name, data in markdown_data['results'].items():
        markdown_content = data['md_content']
        continue

    # 使用原始文件进行印章和OCR识别（避免增强后的文件影响识别精度）
    seal_info = get_seal_info(original_fn, return_seal_img=True, img_w=4, img_h=1)
    seal_info_md = "\n"
    seal_id = 0
    seal_img_list = []
    if 'results' in seal_info:
        for seal_temp in seal_info['results']:
            seal_text = "".join(seal_temp['text'])
            if add_seal_info:
                seal_info_md += f"印章名称：{seal_text}\n"
                seal_info_md += f"印章coordinate：{seal_temp['coordinate']}\n"
                seal_info_md += f"印章信息可信度(0-1)：{seal_temp['score']}\n"
            seal_img_list.append(seal_temp['img_data'])
            if ai_seal:
                img_content = chat_bot_img(
                    "请简要输出图片中的内容，重点为落款日期、落款人、印章名称，请勿输出重复性的内容", 
                    img_url=seal_temp['img_data'], 
                    max_tokens=200, 
                    model=ai_model
                    )
                seal_info_md += f"印章附近的内容：{img_content}\n"
            else:
                seal_info_md += f"印章附近的内容：{seal_temp['text']}\n"
    if add_seal_info or ai_seal:
        markdown_content += '\n\n# 印章信息\n' + seal_info_md

    if add_ocr_info:
        ocr_info = get_ocr_info(original_fn, limit_score=0.8)
        ocr_text = "\n\n".join(ocr_info['results'])
        markdown_content += f"\n\n# OCR识别内容\n{ocr_text}"
    
    # 清理临时增强文件（如果是临时生成的）
    if should_enhance and enhanced_fn != original_fn and os.path.exists(enhanced_fn):
        try:
            # 检查是否为临时文件（包含temp目录或以enhanced_开头的文件名）
            import tempfile
            temp_dir = tempfile.gettempdir()
            if temp_dir in enhanced_fn or 'enhanced_' in os.path.basename(enhanced_fn):
                os.remove(enhanced_fn)
                print(f"清理临时增强文件：{enhanced_fn}")
        except Exception as e:
            print(f"清理临时文件失败：{e}")
    
    return markdown_content, seal_img_list


def fn_to_markdown_v2(fn, convert_to_scanned=False, ai_seal=False, ai_model='qwen2.5vl:3b', page_split="\n", add_ocr_info=False, max_width=1920):
    use_fn = fn
    # 判断是否是图片
    if fn.endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp')):
        # 打开原始图片
        with Image.open(fn) as img:
            # 获取原始尺寸
            original_width, original_height = img.size
            
            # 如果宽度已经小于等于最大宽度，直接复制文件
            if original_width > max_width:
                # 计算新高度（保持宽高比）
                ratio = max_width / original_width
                new_height = int(original_height * ratio)
                
                # 使用高质量缩放下采样
                resized_img = img.resize((max_width, new_height), Image.LANCZOS)
                temp_fn = fn.replace(os.path.splitext(fn)[1], "_temp.jpg")
                resized_img.save(temp_fn)
                use_fn = temp_fn
    res = erayt_ocr_to_markdown(use_fn, scan_pdf=convert_to_scanned)['results']
    if use_fn != fn:
        os.remove(use_fn)
    markdown_content = page_split.join(res['markdown'])
    seal_img_list = []
    for img_url in res['imgs']:
        if "seal_box" in img_url and ai_seal:
            img_content = chat_bot_img(
                "请仔细查看并简要输出图片中的内容，重点为落款日期、落款人、印章的具体信息，请勿输出重复性的内容", 
                img_url=res['imgs'][img_url],  
                model=ai_model
                )
            seal_info = f"\n```markdown\n印章附近的内容：\n{img_content}\n```\n"

            pattern = r'<img\s+[^>]*src="'+ img_url + r'"[^>]*>'
            match_list = re.findall(pattern, markdown_content)
            for match in match_list:
                markdown_content = markdown_content.replace(match, seal_info)
        seal_img_list.append(res['imgs'][img_url])

    if add_ocr_info:
        ocr_info = get_ocr_info(fn, limit_score=0.8)
        ocr_text = "\n\n".join(ocr_info['results'])
        markdown_content += f"\n\n# OCR识别内容\n{ocr_text}"

    return markdown_content, seal_img_list


def pdf2markdown(fn, return_images=False):
    """
    仅将pdf转为markdown，不做其他任何多余的加工
    """
    markdown_data = trans_pdf_to_markdown(fn)
    for fn_name, data in markdown_data['results'].items():
        if return_images:
            return data['md_content'], data['images']
        else:
            return data['md_content']


def rag_search(markdown_content, keywords=[], keywords2=[], spilt_father_doc='\n\n', spilt_son_doc='\n'):
    """
    markdown_content：markdown格式的文件内容
    keywords：检索关键词，列表
    keywords2：检索关键词2，列表
    """
    father_docs = markdown_content.split(spilt_father_doc)
    father_docs = [(spilt_father_doc + doc).strip() for doc in father_docs if doc.strip()]
    son_docs = {}
    for father_doc in father_docs:
        son_doc_temp = father_doc.split(spilt_son_doc)
        son_doc_temp = [(spilt_son_doc + doc).strip() for doc in son_doc_temp if doc.strip()]
        son_docs[father_doc] = son_doc_temp
    # 相关文档检索
    refer_docs = []
    for father_doc in father_docs:
        is_match = False
        for keyword in keywords:
            if keyword in father_doc:
                if len(keywords2) > 0:
                    for keyword2 in keywords2:
                        if keyword2 in father_doc:
                            refer_docs.append(father_doc)
                            is_match = True
                            break
                else:
                    refer_docs.append(father_doc)
                    is_match = True
            if is_match:
                break
    refer_doc_text = "\n".join(refer_docs)            
    return refer_docs, refer_doc_text


def rag_search_neighbor(markdown_content, keywords=[], keywords2=[], neighbor_count=0, spilt_father_doc='\n\n', spilt_son_doc='\n'):
    """
    markdown_content：markdown格式的文件内容
    keywords：检索关键词，列表
    keywords2：检索关键词2，列表
    neighbor_count：邻居数量，当匹配时取出父文档的非空邻居数量
    """
    father_docs = markdown_content.split(spilt_father_doc)
    father_docs = [(spilt_father_doc + doc).strip() for doc in father_docs if doc.strip()]
    son_docs = {}
    for father_doc in father_docs:
        son_doc_temp = father_doc.split(spilt_son_doc)
        son_doc_temp = [(spilt_son_doc + doc).strip() for doc in son_doc_temp if doc.strip()]
        son_docs[father_doc] = son_doc_temp
    
    # 相关文档检索
    matched_indices = set()  # 用于记录需要添加的文档索引
    
    for i, father_doc in enumerate(father_docs):
        is_match = False
        for keyword in keywords:
            if keyword in father_doc:
                if len(keywords2) > 0:
                    for keyword2 in keywords2:
                        if keyword2 in father_doc:
                            is_match = True
                            break
                else:
                    is_match = True
            if is_match:
                break
        
        if is_match:
            # 添加当前匹配的文档和邻居文档
            for offset in range(-neighbor_count, neighbor_count + 1):
                neighbor_index = i + offset
                if 0 <= neighbor_index < len(father_docs):
                    neighbor_doc = father_docs[neighbor_index]
                    # 检查邻居文档是否非空
                    if neighbor_doc.strip():
                        matched_indices.add(neighbor_index)
    
    # 按索引顺序添加到 refer_docs
    refer_docs = []
    for i in sorted(matched_indices):
        refer_docs.append(father_docs[i])
    
    refer_doc_text = "\n".join(refer_docs)            
    return refer_docs, refer_doc_text


def process_file_to_base64(file_path) -> list:
    """
    处理文件，如果是图片文件则直接返回文件名，如果是PDF文件则逐页转为JPG并返回base64编码列表
    
    :param file_path: 文件路径
    :return: 
        - 如果是图片文件：返回文件名字符串
        - 如果是PDF文件：返回包含每页base64编码JPG的列表
        - 如果文件不存在或格式不支持：返回None
    """
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return None
    
    # 获取文件扩展名
    file_ext = os.path.splitext(file_path)[1].lower()
    
    # 图片文件扩展名列表
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']
    
    # 如果是图片文件，直接返回文件名
    if file_ext in image_extensions:
        return [file_path]
    
    # 如果是PDF文件，转换为JPG并返回base64编码列表
    elif file_ext == '.pdf':
        try:
            # 使用现有的pdf_to_images函数将PDF每页转为图像
            images = pdf_to_images(file_path, dpi=200)
            
            base64_list = []
            for i, img in enumerate(images):
                # 将图像转换为JPG格式的字节流
                buffered = BytesIO()
                # 确保图像是RGB模式（JPG不支持RGBA）
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img.save(buffered, format="JPEG", quality=95)
                
                # 将字节流转换为base64编码，并添加data URI前缀
                img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')
                img_data_uri = f"data:image/jpeg;base64,{img_base64}"
                base64_list.append(img_data_uri)
                
                # print(f"已处理PDF第 {i+1} 页")
            
            # print(f"PDF文件 {file_path} 已成功转换为 {len(base64_list)} 页JPG图像")
            return base64_list
            
        except Exception as e:
            print(f"处理PDF文件时出错: {str(e)}")
            return None
    
    else:
        print(f"不支持的文件格式: {file_ext}")
        return None
    

def cal_fn_md5(fn):
    """
    计算文件的md5值
    """
    with open(fn, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()
    
def markdown_json_to_dict(markdown_json):
    """
    将markdown格式转换为dict
    """
    if "```json" in markdown_json:
        pattern = r'```json\s*(.*?)\s*```'
        json_text = re.findall(pattern, markdown_json, re.DOTALL)[0]
    elif "```" in markdown_json:
        pattern = r'```\s*(.*?)\s*```'
        json_text = re.findall(pattern, markdown_json, re.DOTALL)[0]
    else:
        json_text = markdown_json
    return json.loads(json_text)
# -*- coding: utf-8 -*-
"""
主路由模块
"""
from flask import Blueprint, request, jsonify, render_template, redirect, url_for, flash, current_app
from flask_login import login_required, current_user
from models import db, PromptConfig, PromptVersion
from services.database_service import DatabaseService
from utils.auth_utils import require_permission
import json
from utils.default_prompts import DEFAULT_PROMPTS, get_default_prompt, get_all_default_prompts
from utils.hardcoded_prompts import get_default_prompts
from datetime import datetime

main_bp = Blueprint('main', __name__)
db_service = DatabaseService()

# ==================== 页面路由 ====================

@main_bp.route('/')
@login_required
def index():
    """首页"""
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """仪表盘页面"""
    return render_template('dashboard.html')

@main_bp.route('/records')
@login_required
def records():
    """分析记录页面"""
    return render_template('records.html')

@main_bp.route('/record/<int:record_id>')
@login_required
def record_detail(record_id):
    """记录详情页面"""
    return render_template('record_detail.html')

@main_bp.route('/document-analysis')
@login_required
def document_analysis():
    """文档分析页面"""
    return render_template('document_analysis.html')

@main_bp.route('/file-management')
@login_required
def file_management():
    """文件管理页面"""
    return render_template('file_management.html')

@main_bp.route('/review')
@login_required
@require_permission('manage_tags')
def review():
    """复核管理页面"""
    return render_template('review.html')

@main_bp.route('/user-management')
@login_required
@require_permission('admin')
def user_management():
    """用户管理页面"""
    return render_template('user_management.html')

# 确保system_config路由正确定义
@main_bp.route('/system-config', methods=['GET'])
@login_required
@require_permission('all_permissions')  # 只允许管理员访问
def system_config():
    """系统配置页面"""
    return render_template('system_config.html')

@main_bp.route('/mock-config')
@login_required
@require_permission('admin')
def mock_config():
    """挡板配置页面"""
    return render_template('mock_config.html')

@main_bp.route('/model-config')
@login_required
@require_permission('admin')
def model_config():
    """模型配置页面"""
    return render_template('model_config.html')

@main_bp.route('/prompt-config')
@login_required
@require_permission('admin')
def prompt_config():
    """提示词管理页面"""
    return render_template('prompt.html')

@main_bp.route('/prompt_version_management')
@login_required
@require_permission('admin')
def prompt_version_management():
    """提示词管理页面"""
    return render_template('prompt_version_management.html')

@main_bp.route('/help')
@login_required
def help():
    """帮助页面"""
    return render_template('help.html')

# ==================== API路由 ====================

@main_bp.route('/api/dashboard/stats')
@login_required
def dashboard_stats():
    """获取仪表盘统计数据"""
    try:
        # 获取总文件数
        total_files = db_service.get_total_files()
        
        # 获取今日处理数
        today_processed = db_service.get_today_processed()
        
        # 获取待复核数
        pending_reviews = db_service.get_pending_reviews_count()
        
        # 获取准确率
        accuracy_rate = db_service.get_average_accuracy()
        
        # 计算文件数增长率（本月与上月比较）
        files_change = db_service.get_files_growth_rate() or {
            'value': '+12%', 
            'period': '本月'
        }
        
        # 今日处理数与昨日比较
        processed_change = db_service.get_processed_growth_rate() or {
            'value': '+8%', 
            'period': '昨日'
        }
        
        # 待复核数变化（默认无变化）
        pending_change = {
            'value': '无变化', 
            'period': ''
        }
        
        # 准确率与上月比较
        accuracy_change = db_service.get_accuracy_growth_rate() or {
            'value': '+2.5%', 
            'period': '本月'
        }
        
        return jsonify({
            'success': True,
            'data': {
                'total_files': total_files,
                'today_processed': today_processed,
                'pending_reviews': pending_reviews,
                'accuracy_rate': accuracy_rate,
                'files_change': files_change,
                'processed_change': processed_change,
                'pending_change': pending_change,
                'accuracy_change': accuracy_change
            }
        })
    except Exception as e:
        current_app.logger.error(f'获取仪表盘统计数据失败: {e}')
        return jsonify({
            'success': False,
            'message': '获取统计数据失败'
        }), 500

@main_bp.route('/api/dashboard/type-stats')
@login_required
def dashboard_type_stats():
    """获取各类型文件统计"""
    try:
        # 获取时间范围参数（天数）
        days = request.args.get('days', 30, type=int)
        
        # 按类型统计
        type_stats = db_service.get_type_statistics(days=days)
        
        # 转换为前端需要的格式
        stats_data = []
        for stat in type_stats:
            stats_data.append({
                'type': stat['type_name'],
                'count': stat['count'],
                'percentage': stat['percentage']
            })
        
        return jsonify({
            'success': True,
            'data': stats_data
        })
    except Exception as e:
        current_app.logger.error(f'获取类型统计数据失败: {e}')
        # 返回模拟数据
        mock_data = [
            {'type': '期货账户', 'count': 245, 'percentage': 35.2},
            {'type': '理财产品', 'count': 189, 'percentage': 27.1},
            {'type': '券商计息', 'count': 134, 'percentage': 19.2},
            {'type': '期货会员', 'count': 78, 'percentage': 11.2},
            {'type': '宁银费用', 'count': 32, 'percentage': 4.6},
            {'type': '非标交易', 'count': 18, 'percentage': 2.7}
        ]
        return jsonify({
            'success': True,
            'data': mock_data
        })

@main_bp.route('/api/records/recent')
@login_required
def recent_records():
    """获取最近分析记录"""
    try:
        limit = request.args.get('limit', 5, type=int)
        records = db_service.get_recent_records(limit=limit)
        
        return jsonify({
            'success': True,
            'data': [record.to_dict() for record in records]
        })
    except Exception as e:
        current_app.logger.error(f'获取最近记录失败: {e}')
        return jsonify({
            'success': False,
            'message': '获取记录失败'
        }), 500

@main_bp.route('/api/analysis-stats')
@login_required
def analysis_stats():
    """获取分析统计信息"""
    try:
        # 获取总文件数
        total_files = db_service.get_total_files()
        
        # 获取今日处理数
        today_processed = db_service.get_today_processed()
        
        # 获取待复核数
        pending_reviews = db_service.get_pending_reviews_count()
        
        # 按类型统计
        type_stats = db_service.get_type_statistics()
        
        return jsonify({
            'success': True,
            'data': {
                'total_files': total_files,
                'today_processed': today_processed,
                'pending_reviews': pending_reviews,
                'type_stats': type_stats
            }
        })
    except Exception as e:
        current_app.logger.error(f'获取分析统计信息失败: {e}')
        return jsonify({
            'success': False,
            'message': '获取统计信息失败'
        }), 500

@main_bp.route('/api/recent-activities')
@login_required
def recent_activities():
    """获取最近活动"""
    try:
        activities = db_service.get_recent_activities(limit=10)
        return jsonify({
            'success': True,
            'data': [activity.to_dict() for activity in activities]
        })
    except Exception as e:
        current_app.logger.error(f'获取最近活动失败: {e}')
        return jsonify({
            'success': False,
            'message': '获取活动信息失败'
        }), 500

# ==================== 提示词管理 API ====================

@main_bp.route('/api/prompts')
@login_required
def get_prompts():
    """获取提示词列表"""
    analysis_type = request.args.get('analysis_type')
    version = request.args.get('version')
    
    query = PromptConfig.query
    if analysis_type:
        query = query.filter(PromptConfig.analysis_type == analysis_type)
    if version:
        query = query.filter(PromptConfig.version == version)
    
    prompts = query.all()
    return jsonify({
        'success': True,
        'data': [prompt.to_dict() for prompt in prompts]
    })

@main_bp.route('/api/prompts', methods=['POST'])
@login_required
@require_permission('admin')
def create_prompt():
    """创建提示词"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
    
    # 验证必填字段
    required_fields = ['analysis_type', 'prompt_key', 'prompt_content']
    for field in required_fields:
        if field not in data:
            return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400
    
    # 检查同类型同键名的提示词是否存在
    existing = PromptConfig.query.filter_by(
        analysis_type=data['analysis_type'],
        prompt_key=data['prompt_key']
    ).first()
    
    if existing:
        return jsonify({'success': False, 'message': '相同类型和键名的提示词已存在'}), 400
    
    # 创建新提示词
    try:
        prompt = PromptConfig(
            analysis_type=data['analysis_type'],
            prompt_key=data['prompt_key'],
            prompt_content=data['prompt_content'],
            description=data.get('description', ''),
            is_active=data.get('is_active', True),
            is_default=data.get('is_default', False),
            version=data.get('version', 'v1.0'),
            created_by=current_user.id
        )
        db.session.add(prompt)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '提示词创建成功',
            'data': prompt.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建提示词失败: {e}')
        return jsonify({
            'success': False,
            'message': f'创建提示词失败: {str(e)}'
        }), 500

@main_bp.route('/api/prompts/<int:prompt_id>')
@login_required
def get_prompt(prompt_id):
    """获取单个提示词"""
    prompt = db.session.get(PromptConfig, prompt_id)
    if not prompt:
        return jsonify({'success': False, 'message': '提示词不存在'}), 404
    
    return jsonify({
        'success': True,
        'data': prompt.to_dict()
    })

@main_bp.route('/api/prompts/<int:prompt_id>', methods=['PUT'])
@login_required
@require_permission('admin')
def update_prompt(prompt_id):
    """更新提示词"""
    prompt = db.session.get(PromptConfig, prompt_id)
    if not prompt:
        return jsonify({'success': False, 'message': '提示词不存在'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
    
    # 更新提示词内容
    if 'prompt_content' in data:
        prompt.prompt_content = data['prompt_content']
    if 'description' in data:
        prompt.description = data['description']
    if 'is_active' in data:
        prompt.is_active = data['is_active']
    if 'version' in data:
        prompt.version = data['version']
    
    prompt.updated_at = datetime.utcnow()
    db.session.commit()
    
    return jsonify({
        'success': True,
        'message': '提示词更新成功',
        'data': prompt.to_dict()
    })

@main_bp.route('/api/prompts/<int:prompt_id>', methods=['DELETE'])
@login_required
@require_permission('admin')
def delete_prompt(prompt_id):
    """删除提示词"""
    prompt = db.session.get(PromptConfig, prompt_id)
    if not prompt:
        return jsonify({'success': False, 'message': '提示词不存在'}), 404
    
    try:
        db.session.delete(prompt)
        db.session.commit()
        return jsonify({
            'success': True,
            'message': '提示词删除成功'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除提示词失败: {e}')
        return jsonify({
            'success': False,
            'message': f'删除提示词失败: {str(e)}'
        }), 500

@main_bp.route('/api/prompts/init_default', methods=['POST'])
@login_required
@require_permission('admin')
def init_default_prompts():
    """初始化默认提示词"""
    # 获取请求参数
    data = request.get_json()
    reset_only = data.get('reset_only', False)  # 是否只返回默认提示词，不更新数据库
    prompt_type = data.get('type')  # 指定类型
    
    current_app.logger.info(f"接收到初始化默认提示词请求: type={prompt_type}, reset_only={reset_only}")
    
    # 获取硬编码的默认提示词
    hardcoded_prompts = get_default_prompts()
    available_types = list(hardcoded_prompts.keys())
    current_app.logger.info(f"可用的硬编码提示词类型: {available_types}")
    
    # 如果指定了类型且仅需获取默认提示词
    if prompt_type and reset_only:
        current_app.logger.info(f"请求类型 {prompt_type} 的默认提示词")
        
        # 检查请求的类型是否存在
        if prompt_type in hardcoded_prompts:
            prompt_content = hardcoded_prompts[prompt_type]
            current_app.logger.info(f"找到类型 {prompt_type} 的硬编码提示词，长度: {len(prompt_content)}")
            
            # 返回成功响应
            return jsonify({
                'success': True,
                'message': f'获取 {prompt_type} 类型的默认提示词成功',
                'default_prompts': {prompt_type: prompt_content}
            })
        
        # 尝试在备用映射中查找
        mapped_types = {
            'future': ['futures_account', 'futures_member'],
            'futures_member': ['future', 'futures_account', 'non_standard_trade'],
            'futures_account': ['future', 'futures_member'],
            'broker_interest': ['broker_interest'],
            'ningyin_fee': ['ningyin_fee'],
            'financial': ['financial'],
            'account_opening': ['product_manual', 'account_opening'],
            'non_standard_trade': ['futures_member', 'non_standard_trade'],
            'product_manual': ['account_opening', 'product_manual']
        }
        
        # 检查是否有可用的映射类型
        if prompt_type in mapped_types:
            for alt_type in mapped_types[prompt_type]:
                if alt_type in hardcoded_prompts:
                    prompt_content = hardcoded_prompts[alt_type]
                    current_app.logger.info(f"使用映射类型 {alt_type} 的提示词代替 {prompt_type}")
                    
                    return jsonify({
                        'success': True,
                        'message': f'获取 {prompt_type} 类型的默认提示词成功 (映射自 {alt_type})',
                        'default_prompts': {prompt_type: prompt_content}
                    })
        
        # 如果都找不到，返回错误
        current_app.logger.warning(f"未找到类型 {prompt_type} 的默认提示词")
        return jsonify({
            'success': False,
            'message': f'未找到 {prompt_type} 类型的默认提示词',
            'available_types': available_types
        }), 404
    
    # 如果只是请求所有默认提示词但不更新数据库
    if reset_only:
        current_app.logger.info(f"返回所有硬编码默认提示词")
        return jsonify({
            'success': True,
            'message': '获取所有默认提示词成功',
            'default_prompts': hardcoded_prompts
        })
    
    # 以下为更新数据库的逻辑
    # 决定要处理的分析类型
    analysis_types = available_types
    if prompt_type and prompt_type in hardcoded_prompts:
        analysis_types = [prompt_type]
    
    created_count = 0
    updated_count = 0
    
    # 为每个分析类型创建/更新默认提示词
    for analysis_type in analysis_types:
        # 检查是否已存在该类型的提示词
        existing_prompt = PromptConfig.query.filter_by(
            analysis_type=analysis_type,
            prompt_key='system_prompt'
        ).first()
        
        if existing_prompt:
            # 如果存在，更新内容
            existing_prompt.prompt_content = hardcoded_prompts[analysis_type]
            existing_prompt.updated_at = datetime.utcnow()
            updated_count += 1
            current_app.logger.info(f"更新类型 {analysis_type} 的提示词")
        else:
            # 如果不存在，创建新的提示词
            prompt = PromptConfig(
                analysis_type=analysis_type,
                prompt_key='system_prompt',
                prompt_content=hardcoded_prompts[analysis_type],
                description=f'{analysis_type}类型的系统提示词',
                is_active=True,
                is_default=True,
                version='v1.0'
            )
            db.session.add(prompt)
            created_count += 1
            current_app.logger.info(f"创建类型 {analysis_type} 的提示词")
    
    db.session.commit()
    current_app.logger.info(f"初始化完成，新增{created_count}条，更新{updated_count}条提示词")
    
    # 返回结果
    response_prompts = {}
    if prompt_type and prompt_type in hardcoded_prompts:
        response_prompts = {prompt_type: hardcoded_prompts[prompt_type]}
    else:
        response_prompts = hardcoded_prompts
    
    return jsonify({
        'success': True,
        'message': f'初始化完成，新增{created_count}条，更新{updated_count}条提示词',
        'default_prompts': response_prompts
    })

# 默认提示词查看路由（用于调试）
@main_bp.route('/api/prompts/debug-defaults/<analysis_type>', methods=['GET'])
@login_required
@require_permission('admin')
def debug_default_prompts(analysis_type):
    """查看指定分析类型的默认提示词（调试用）"""
    try:
        # 获取硬编码的默认提示词
        hardcoded_prompts = get_default_prompts()
        
        # 检查请求的类型是否存在
        if analysis_type in hardcoded_prompts:
            prompt_content = hardcoded_prompts[analysis_type]
            return jsonify({
                'success': True,
                'analysis_type': analysis_type,
                'content_length': len(prompt_content),
                'content': prompt_content
            })
        
        # 检查映射类型
        mapped_types = {
            'future': ['futures_account', 'futures_member'],
            'futures_member': ['future', 'futures_account', 'non_standard_trade'],
            'futures_account': ['future', 'futures_member'],
            'broker_interest': ['broker_interest'],
            'ningyin_fee': ['ningyin_fee'],
            'financial': ['financial'],
            'account_opening': ['product_manual', 'account_opening'],
            'non_standard_trade': ['futures_member', 'non_standard_trade'],
            'product_manual': ['account_opening', 'product_manual']
        }
        
        # 检查是否有可用的映射类型
        if analysis_type in mapped_types:
            for alt_type in mapped_types[analysis_type]:
                if alt_type in hardcoded_prompts:
                    prompt_content = hardcoded_prompts[alt_type]
                    return jsonify({
                        'success': True,
                        'analysis_type': analysis_type,
                        'mapped_from': alt_type,
                        'content_length': len(prompt_content),
                        'content': prompt_content
                    })
        
        return jsonify({
            'success': False,
            'message': f'找不到类型 {analysis_type} 的默认提示词',
            'available_types': list(hardcoded_prompts.keys())
        }), 404
        
    except Exception as e:
        current_app.logger.error(f'查看默认提示词失败: {e}')
        return jsonify({
            'success': False,
            'message': f'查看默认提示词失败: {str(e)}'
        }), 500

# ==================== 提示词版本管理 API ====================

@main_bp.route('/api/prompt-versions/<analysis_type>')
@login_required
def get_prompt_versions(analysis_type):
    """获取指定分析类型的提示词版本列表，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'获取版本列表，类型: {analysis_type}')
    
    # 记录所有prompts相关的表内容，便于调试
    try:
        all_configs = PromptConfig.query.all()
        current_app.logger.info(f"数据库中共有 {len(all_configs)} 个提示词配置")
        for cfg in all_configs:
            current_app.logger.info(f"配置 ID:{cfg.id}, 类型:{cfg.analysis_type}, 键:{cfg.prompt_key}, 版本:{cfg.version}, 激活:{cfg.is_active}")
    except Exception as e:
        current_app.logger.warning(f"获取所有提示词配置失败: {e}")
    try:
        # 获取指定分析类型的所有提示词配置，不过滤激活状态
        prompts = PromptConfig.query.filter_by(
            analysis_type=analysis_type
            # 不使用is_active=True进行过滤，确保返回所有版本
        ).order_by(
            PromptConfig.version.desc()
        ).all()
        
        # 记录查询到的所有版本
        current_app.logger.info(f'查询到 {len(prompts)} 个版本配置，analysis_type={analysis_type}')
        for p in prompts:
            current_app.logger.info(f'版本信息: ID={p.id}, 类型={p.analysis_type}, '
                                   f'键={p.prompt_key}, 版本号={p.version}, '
                                   f'版本名称={p.description}, 激活={p.is_active}')
        
        # 找出当前激活的版本
        current_version = None
        for prompt in prompts:
            if prompt.is_active and prompt.prompt_key == 'system_prompt':
                current_version = prompt
                current_app.logger.info(f'找到当前激活版本: ID={prompt.id}, 版本={prompt.version}')
                break
                
        # 如果未找到激活版本，记录警告
        if not current_version:
            current_app.logger.warning(f'未找到类型 {analysis_type} 的激活版本！')
        
        # 格式化版本列表
        versions = []
        for prompt in prompts:
            # 只处理系统提示词类型
            if prompt.prompt_key == 'system_prompt':
                is_current = current_version and prompt.id == current_version.id
                
                # 手动构建版本信息，避免依赖to_dict方法可能出现的问题
                # 确保始终显示所有版本，无论是否激活
                
                # 直接从数据库获取实际值，不做任何修改或默认值处理
                version_info = {
                    'id': prompt.id,
                    'analysis_type': prompt.analysis_type,
                    'prompt_key': prompt.prompt_key,
                    'prompt_content': prompt.prompt_content,
                    'prompt': prompt.prompt_content,  # 兼容前端字段
                    'description': prompt.description,  # 原始描述字段
                    'is_active': prompt.is_active,
                    'is_current': is_current,  # 当前激活版本标记
                    'is_default': prompt.is_default,
                    'version': prompt.version,  # 直接使用数据库中的版本号
                    'created_at': prompt.created_at.isoformat() if prompt.created_at else None,
                    'updated_at': prompt.updated_at.isoformat() if prompt.updated_at else None,
                    'type': prompt.prompt_key,
                    'usage_count': 0  # 默认使用次数
                }
                
                # 记录原始数据，确保数据一致性
                current_app.logger.info(f"直接从数据库获取的版本信息: ID={prompt.id}, 版本号={prompt.version}, 描述={prompt.description}")
                
                # 记录每个版本的详细信息
                current_app.logger.info(f"版本ID:{prompt.id}, 版本号:{prompt.version}, 名称:{prompt.description}, 激活:{prompt.is_active}, 当前:{is_current}")
                
                # name字段直接使用description的值，不做任何修改或默认值处理
                # 这确保了前端显示的名称与数据库中存储的完全一致
                version_info['name'] = prompt.description
                current_app.logger.info(f"name字段设置为原始description值: {prompt.description}")
                    
                # 记录完整版本信息，便于调试    
                current_app.logger.info(f"版本详细信息：{json.dumps(version_info, ensure_ascii=False)}")
                versions.append(version_info)
        
        # 创建安全的返回对象
        response_data = {
            'success': True,
            'versions': versions,
            'analysis_type': analysis_type
        }
        
        # 仅在current_version存在时添加
        if current_version:
            try:
                # 手动构建当前版本信息，确保格式一致
                current_version_info = {
                    'id': current_version.id,
                    'analysis_type': current_version.analysis_type,
                    'prompt_key': current_version.prompt_key,
                    'prompt_content': current_version.prompt_content,
                    'prompt': current_version.prompt_content,
                    'description': current_version.description,
                    'name': current_version.description or f'{analysis_type} 版本 {current_version.version}',
                    'is_active': current_version.is_active,
                    'is_current': True,
                    'is_default': current_version.is_default,
                    'version': current_version.version,
                    'created_at': current_version.created_at.isoformat() if current_version.created_at else None,
                    'updated_at': current_version.updated_at.isoformat() if current_version.updated_at else None,
                    'type': current_version.prompt_key,
                    'usage_count': 0
                }
                
                response_data['current_version'] = current_version_info
                current_app.logger.info(f"当前激活版本详细信息：{json.dumps(current_version_info, ensure_ascii=False)}")
            except Exception as e:
                current_app.logger.error(f'构建当前版本信息时出错: {e}')
                response_data['current_version'] = {
                    'id': current_version.id,
                    'analysis_type': current_version.analysis_type,
                    'version': current_version.version,
                    'prompt_content': current_version.prompt_content,
                    'name': current_version.description or f'{analysis_type} 版本 {current_version.version}'
                }
        else:
            response_data['current_version'] = None
            current_app.logger.info("没有找到当前激活的版本")
            
        return jsonify(response_data)
    except Exception as e:
        current_app.logger.error(f'获取版本列表失败: {e}')
        return jsonify({
            'success': False,
            'message': f'获取版本列表失败: {str(e)}'
        }), 500
        
@main_bp.route('/api/prompt-versions/<analysis_type>/<int:version_id>', methods=['GET'])
@login_required
def get_prompt_version(analysis_type, version_id):
    """获取指定的提示词版本，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'获取单个版本，类型: {analysis_type}，版本ID: {version_id}')
    
    try:
        # 获取指定版本
        version = db.session.get(PromptConfig, version_id)
        
        if not version or version.analysis_type != analysis_type:
            return jsonify({
                'success': False, 
                'message': '指定版本不存在或不属于该分析类型'
            }), 404
            
        # 返回更完整的版本信息
        version_data = {
            'id': version.id,
            'version': version.version,
            'prompt': version.prompt_content,
            'prompt_content': version.prompt_content,
            'version_description': version.description,
            'description': version.description,
            'name': version.description or f'{analysis_type} 版本 {version.version}', # 确保name字段
            'created_at': version.created_at.isoformat() if version.created_at else None,
            'is_default': version.is_default,
            'is_active': version.is_active,
            'is_current': version.is_active,
            'analysis_type': version.analysis_type,
            'prompt_key': version.prompt_key,
            'type': version.prompt_key
        }
        
        # 记录完整版本信息，便于调试
        current_app.logger.info(f"获取单个版本详细信息：{json.dumps(version_data, ensure_ascii=False)}")
        
        # 返回结果（注意缩进，这是在try块内部）
        return jsonify({
            'success': True,
            'version': version_data
        })
        
    except Exception as e:
        current_app.logger.error(f'获取版本信息失败: {e}')
        return jsonify({
            'success': False,
            'message': f'获取版本信息失败: {str(e)}'
        }), 500

@main_bp.route('/api/prompt-versions/<analysis_type>', methods=['POST'])
@login_required
@require_permission('admin')
def create_prompt_version(analysis_type):
    """创建新的提示词版本，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'创建新版本，类型: {analysis_type}')
    """创建新的提示词版本"""
    data = request.get_json()
    if not data:
        return jsonify({'success': False, 'message': '请求数据不能为空'}), 400
    
    # 验证必填字段
    required_fields = ['version', 'prompt', 'name']
    for field in required_fields:
        if field not in data:
            return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400
    
    # 处理版本号，确保唯一性
    version_number = data['version']
    prompt_key = data.get('type', 'system_prompt')
    
    # 打印详细信息用于调试
    current_app.logger.info(f"接收到的请求数据: {data}")
    current_app.logger.info(f"分析类型: {analysis_type}, 版本号: {version_number}, 提示词键: {prompt_key}")
    
    # 检查版本是否已存在
    existing_version = PromptConfig.query.filter_by(
        analysis_type=analysis_type,
        version=version_number,
        prompt_key=prompt_key
    ).first()
    
    if existing_version:
        # 如果版本已存在，返回错误提示而不是自动修改用户输入
        current_app.logger.warning(f'版本 {version_number} 已存在，返回错误')
        return jsonify({
            'success': False, 
            'message': f'版本号 {version_number} 已存在，请使用其他版本号'
        }), 400
        
        # 不再自动修改用户输入的版本号，尊重用户输入
        # 下面的代码被注释掉，不再使用
        """
        # 查询同类型版本数量，用于生成唯一版本号
        version_count = PromptConfig.query.filter_by(
            analysis_type=analysis_type,
            prompt_key=prompt_key
        ).count()
        
        # 时间戳 + 计数，确保唯一性
        import time
        timestamp = int(time.time())
        
        version_number = f"{version_number}_{timestamp}_{version_count + 1}"
        current_app.logger.info(f'生成新的唯一版本号: {version_number}')
        """
    
    # 是否设为当前激活版本
    set_as_active = data.get('set_as_active', False)
    
    try:
        # 完全尊重用户输入的版本名称和描述
        version_name = data.get('name', '')
        version_description = data.get('version_description', '')
        
        # 验证版本名称不为空，前端已经做了必填验证
        if not version_name:
            return jsonify({
                'success': False, 
                'message': '版本名称不能为空'
            }), 400
            
        # 直接使用用户输入的名称
        final_description = version_name
        
        # 如果描述为空，则使用名称作为描述，这不影响显示
        if not version_description:
            version_description = version_name
        
        # 记录版本信息
        current_app.logger.info(f"使用完全未修改的用户输入版本名称: {final_description}")
        current_app.logger.info(f"版本名称: {version_name}, 版本描述: {version_description}")
        
        # 创建新版本
        new_version = PromptConfig(
            analysis_type=analysis_type,
            prompt_key=prompt_key,
            prompt_content=data['prompt'],
            description=final_description,  # 使用处理后的描述
            version=version_number,  # 使用可能已修改的版本号
            is_active=False,  # 初始设置为非激活，后续根据需要激活
            created_by=current_user.id
        )
        db.session.add(new_version)
        db.session.flush()  # 确保ID生成
        
        # 如果设为激活版本，需要将其他版本设为非激活
        if set_as_active:
            # 先将所有同类型同key的版本设为非激活
            current_app.logger.info(f'设置所有同类型版本为非激活，类型: {analysis_type}, 键: {prompt_key}')
            PromptConfig.query.filter(
                PromptConfig.analysis_type == analysis_type,
                PromptConfig.prompt_key == prompt_key
            ).update({'is_active': False})
            
            # 然后单独将新版本设为激活
            current_app.logger.info(f'设置新版本(ID:{new_version.id})为激活状态')
            new_version.is_active = True
        
        db.session.commit()
        
        # 验证激活状态
        db.session.refresh(new_version)
        current_app.logger.info(f'新版本激活状态: {new_version.is_active}')
        
        # 手动构建版本信息，确保所有必要字段都存在
        version_data = {
            'id': new_version.id,
            'analysis_type': new_version.analysis_type,
            'prompt_key': new_version.prompt_key,
            'prompt_content': new_version.prompt_content,
            'prompt': new_version.prompt_content,
            'description': new_version.description,
            'name': new_version.description,  # 确保name字段与description保持一致
            'is_active': new_version.is_active,
            'is_default': new_version.is_default,
            'version': new_version.version,
            'created_at': new_version.created_at.isoformat() if new_version.created_at else None,
            'updated_at': new_version.updated_at.isoformat() if new_version.updated_at else None,
            'type': new_version.prompt_key
        }
            
        current_app.logger.info(f"成功创建版本详细信息: {json.dumps(version_data, ensure_ascii=False)}")
        
        # 记录当前该分析类型的所有版本
        all_versions = PromptConfig.query.filter_by(
            analysis_type=analysis_type,
            prompt_key=prompt_key
        ).all()
        current_app.logger.info(f"当前类型 {analysis_type} 共有 {len(all_versions)} 个版本")
        
        return jsonify({
            'success': True,
            'message': '提示词版本创建成功',
            'data': version_data
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'创建提示词版本失败: {e}')
        return jsonify({
            'success': False,
            'message': f'创建提示词版本失败: {str(e)}'
        }), 500

@main_bp.route('/api/prompt-versions/<analysis_type>/activate', methods=['POST'])
@login_required
@require_permission('admin')
def activate_prompt_version(analysis_type):
    """激活提示词版本，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'激活版本，类型: {analysis_type}')
    """激活指定的提示词版本"""
    data = request.get_json()
    if not data or 'version_id' not in data:
        return jsonify({'success': False, 'message': '请求数据不能为空或缺少版本ID'}), 400
    
    version_id = data['version_id']
    
    # 获取要激活的版本
    version_to_activate = db.session.get(PromptConfig, version_id)
    if not version_to_activate or version_to_activate.analysis_type != analysis_type:
        return jsonify({'success': False, 'message': '指定版本不存在或不属于该分析类型'}), 404
    
    try:
        # 将其他版本设为非激活
        PromptConfig.query.filter(
            PromptConfig.analysis_type == analysis_type,
            PromptConfig.prompt_key == version_to_activate.prompt_key,
            PromptConfig.id != version_id
        ).update({'is_active': False})
        
        # 设置指定版本为激活状态
        version_to_activate.is_active = True
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'版本 {version_to_activate.version} 已激活',
            'data': version_to_activate.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'激活版本失败: {e}')
        return jsonify({
            'success': False,
            'message': f'激活版本失败: {str(e)}'
        }), 500

@main_bp.route('/api/prompt-versions/<analysis_type>/<int:version_id>', methods=['DELETE'])
@login_required
@require_permission('admin')
def delete_prompt_version(analysis_type, version_id):
    """删除提示词版本，URL参数会被自动解码"""
    # 记录请求参数，便于调试
    current_app.logger.info(f'删除版本，类型: {analysis_type}，版本ID: {version_id}')
    
    # 获取要删除的版本
    version_to_delete = db.session.get(PromptConfig, version_id)
    if not version_to_delete or version_to_delete.analysis_type != analysis_type:
        return jsonify({'success': False, 'message': '指定版本不存在或不属于该分析类型'}), 404
    
    # 检查是否为当前激活版本
    if version_to_delete.is_active:
        return jsonify({'success': False, 'message': '无法删除当前激活的版本，请先激活其他版本'}), 400
    
    try:
        db.session.delete(version_to_delete)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'版本 {version_to_delete.version} 已删除'
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除版本失败: {e}')
        return jsonify({
            'success': False,
            'message': f'删除版本失败: {str(e)}'
        }), 500

@main_bp.route('/version_test')
@login_required
def version_test():
    """版本测试页面"""
    return render_template('version_test.html')

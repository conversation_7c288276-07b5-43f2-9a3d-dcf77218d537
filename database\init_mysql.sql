-- 多场景智能化文档分析系统 - MySQL数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS nb_bank_ai_poc DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE nb_bank_ai_poc;

-- 1. 用户表
CREATE TABLE `users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `username` VARCHAR(64) UNIQUE NOT NULL COMMENT '用户名',
    `password_hash` VARCHAR(128) NOT NULL COMMENT '密码哈希',
    `role` VARCHAR(20) DEFAULT 'user' COMMENT '角色: user, analyst, admin',
    `status` VARCHAR(20) DEFAULT 'active' COMMENT '状态: active, inactive, suspended',
    `email` VARCHAR(255) DEFAULT NULL COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `last_login` DATETIME COMMENT '最后登录时间',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `created_by` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_username` (`username`),
    INDEX `idx_role` (`role`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 分析记录表
CREATE TABLE `analysis_records` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `ai_result` TEXT COMMENT 'AI识别结果(JSON)',
    `system_result` TEXT COMMENT '系统查询结果(JSON)',
    `comparison_result` TEXT COMMENT '对比结果(JSON)',
    `status` VARCHAR(20) DEFAULT 'pending' COMMENT '状态: pending, processing, completed, failed',
    `file_status` ENUM('active', 'deprecated', 'archived') DEFAULT 'active' COMMENT '文件状态',
    `status_changed_by` INT DEFAULT NULL COMMENT '状态修改人ID',
    `status_changed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '状态修改时间',
    `status_reason` TEXT DEFAULT NULL COMMENT '状态修改原因',
    `file_hash` VARCHAR(64) DEFAULT NULL COMMENT '文件哈希值(用于去重)',
    `file_info` JSON DEFAULT NULL COMMENT '文件详细信息',
    `review_status` ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending' COMMENT '复核状态',
    `review_priority` ENUM('low', 'normal', 'high') DEFAULT 'normal' COMMENT '复核优先级',
    `accuracy_score` DECIMAL(5,4) DEFAULT NULL COMMENT '准确率评分',
    `audit_status` VARCHAR(20) COMMENT '审核状态: pass, fail, pending',
    `audit_comment` TEXT COMMENT '审核备注',
    `audited_by` INT COMMENT '审核人ID',
    `audited_at` DATETIME COMMENT '审核时间',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_analysis_type` (`analysis_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_file_status` (`file_status`),
    INDEX `idx_review_status` (`review_status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_file_hash` (`file_hash`),
    INDEX `idx_accuracy_score` (`accuracy_score`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`audited_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`status_changed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析记录表';

-- 3. 提示词配置表
CREATE TABLE `prompt_config` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `prompt_key` VARCHAR(100) NOT NULL COMMENT '提示词键名',
    `prompt_content` TEXT NOT NULL COMMENT '提示词内容',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `version` VARCHAR(20) DEFAULT 'v1.0' COMMENT '版本号',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY `unique_type_key` (`analysis_type`, `prompt_key`),
    INDEX `idx_analysis_type` (`analysis_type`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_version` (`version`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词配置表';

-- 4. 提示词版本表
CREATE TABLE `prompt_versions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `version` VARCHAR(20) NOT NULL COMMENT '版本号',
    `prompt_content` TEXT NOT NULL COMMENT '提示词内容',
    `description` TEXT DEFAULT NULL COMMENT '版本描述',
    `is_active` BOOLEAN DEFAULT FALSE COMMENT '是否激活',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `performance_score` DECIMAL(5,4) DEFAULT NULL COMMENT '性能评分',
    
    UNIQUE KEY `unique_type_version` (`analysis_type`, `version`),
    INDEX `idx_type_active` (`analysis_type`, `is_active`),
    INDEX `idx_created_at` (`created_at`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词版本表';

-- 5. 挡板数据表
CREATE TABLE `mock_data` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `query_key` VARCHAR(255) NOT NULL COMMENT '查询关键字',
    `query_type` VARCHAR(50) NOT NULL COMMENT '查询类型',
    `mock_result` TEXT COMMENT '挡板返回结果(JSON)',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_query_key` (`query_key`),
    INDEX `idx_query_type` (`query_type`),
    INDEX `idx_is_active` (`is_active`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='挡板数据表';

-- 6. 客户系统数据表
CREATE TABLE `customer_system_data` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `query_key` VARCHAR(255) NOT NULL COMMENT '查询关键字',
    `query_type` VARCHAR(50) NOT NULL COMMENT '查询类型',
    `data_json` TEXT COMMENT '客户系统返回内容(JSON)',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX `idx_query_key` (`query_key`),
    INDEX `idx_query_type` (`query_type`),
    INDEX `idx_is_active` (`is_active`),
    
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户系统数据表';

-- 7. 文件标签表
CREATE TABLE `file_tags` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `file_id` INT NOT NULL COMMENT '文件ID',
    `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `tag_color` VARCHAR(7) DEFAULT '#2563eb' COMMENT '标签颜色',
    `tag_description` TEXT DEFAULT NULL COMMENT '标签描述',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    UNIQUE KEY `unique_file_tag` (`file_id`, `tag_name`),
    INDEX `idx_tag_name` (`tag_name`),
    INDEX `idx_created_by` (`created_by`),

    FOREIGN KEY (`file_id`) REFERENCES `analysis_records`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件标签表';

-- 8. 复核记录表
CREATE TABLE `review_records` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `record_id` INT NOT NULL COMMENT '分析记录ID',
    `reviewer_id` INT NOT NULL COMMENT '复核人ID',
    `review_status` ENUM('approved', 'rejected', 'needs_revision') NOT NULL COMMENT '复核状态',
    `review_comment` TEXT DEFAULT NULL COMMENT '复核意见',
    `corrections` JSON DEFAULT NULL COMMENT '修正内容',
    `review_time` DECIMAL(8,2) DEFAULT NULL COMMENT '复核耗时(秒)',
    `auto_reviewed` BOOLEAN DEFAULT FALSE COMMENT '是否自动复核',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_record_status` (`record_id`, `review_status`),
    INDEX `idx_reviewer` (`reviewer_id`),
    INDEX `idx_created_at` (`created_at`),

    FOREIGN KEY (`record_id`) REFERENCES `analysis_records`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`reviewer_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='复核记录表';

-- 9. 标准答案表
CREATE TABLE `standard_answers` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `file_pattern` VARCHAR(255) DEFAULT NULL COMMENT '文件名模式',
    `standard_result` JSON NOT NULL COMMENT '标准答案结果',
    `confidence_score` DECIMAL(5,4) DEFAULT 1.0000 COMMENT '置信度评分',
    `source_record_id` INT DEFAULT NULL COMMENT '来源记录ID',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',

    INDEX `idx_type_pattern` (`analysis_type`, `file_pattern`),
    INDEX `idx_confidence` (`confidence_score`),
    INDEX `idx_active` (`is_active`),

    FOREIGN KEY (`source_record_id`) REFERENCES `analysis_records`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标准答案表';

-- 10. 模型配置表
CREATE TABLE `model_configs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `model_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '模型标识',
    `model_name` VARCHAR(100) NOT NULL COMMENT '模型名称',
    `api_url` VARCHAR(255) NOT NULL COMMENT 'API地址',
    `api_key` VARCHAR(255) NOT NULL COMMENT 'API密钥',
    `vision_model` VARCHAR(100) DEFAULT NULL COMMENT '视觉模型名称',
    `timeout` INT DEFAULT 30 COMMENT '超时时间(秒)',
    `max_tokens` INT DEFAULT 4096 COMMENT '最大令牌数',
    `temperature` DECIMAL(3,2) DEFAULT 0.70 COMMENT '温度参数',
    `is_active` BOOLEAN DEFAULT FALSE COMMENT '是否激活',
    `last_test_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后测试时间',
    `test_status` ENUM('success', 'failed', 'pending') DEFAULT 'pending' COMMENT '测试状态',
    `response_time` DECIMAL(8,2) DEFAULT 0.00 COMMENT '响应时间(秒)',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_active` (`is_active`),
    INDEX `idx_test_status` (`test_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表';

-- 11. 仪表盘统计表
CREATE TABLE `dashboard_stats` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `analysis_type` VARCHAR(50) DEFAULT NULL COMMENT '分析类型',
    `total_files` INT DEFAULT 0 COMMENT '总文件数',
    `processed_files` INT DEFAULT 0 COMMENT '已处理文件数',
    `accuracy_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '识别准确率',
    `avg_processing_time` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均处理时间(秒)',
    `success_count` INT DEFAULT 0 COMMENT '成功处理数',
    `failed_count` INT DEFAULT 0 COMMENT '失败处理数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_date_type` (`stat_date`, `analysis_type`),
    INDEX `idx_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仪表盘统计数据表';

-- 12. 用户活动日志表
CREATE TABLE `user_activities` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID',
    `request_id` VARCHAR(64) DEFAULT NULL COMMENT '请求ID',
    `action` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `resource` VARCHAR(255) DEFAULT NULL COMMENT '操作资源',
    `details` JSON DEFAULT NULL COMMENT '操作详情',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_session` (`session_id`),
    INDEX `idx_request` (`request_id`),

    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

-- 13. 全局设置表
CREATE TABLE `global_settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `key` VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    `value` TEXT COMMENT '配置值',
    `description` TEXT COMMENT '配置描述',
    `data_type` VARCHAR(20) DEFAULT 'string' COMMENT '数据类型',
    `is_public` BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX `idx_key` (`key`),
    INDEX `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='全局设置表';

-- ==================== 初始化数据 ====================

-- 插入默认用户
INSERT INTO `users` (`username`, `password_hash`, `role`, `status`, `email`) VALUES
('admin', 'pbkdf2:sha256:600000$salt$hash', 'admin', 'active', '<EMAIL>'),
('analyst', 'pbkdf2:sha256:600000$salt$hash', 'analyst', 'active', '<EMAIL>'),
('user', 'pbkdf2:sha256:600000$salt$hash', 'user', 'active', '<EMAIL>');

-- 插入默认系统设置
INSERT INTO `global_settings` (`key`, `value`, `description`, `data_type`, `is_public`) VALUES
('system_name', '多场景智能化文档分析系统', '系统名称', 'string', TRUE),
('system_version', '2.0.0', '系统版本', 'string', TRUE),
('company_name', '宁波银行', '公司名称', 'string', TRUE),
('max_file_size', '52428800', '最大文件大小(50MB)', 'integer', FALSE),
('allowed_extensions', 'pdf,png,jpg,jpeg,gif,bmp,tiff', '允许的文件扩展名', 'string', FALSE),
('mock_mode_enabled', 'true', '是否启用挡板模式', 'boolean', FALSE),
('records_per_page', '20', '每页记录数', 'integer', FALSE);

-- 插入默认模型配置
INSERT INTO `model_configs` (`model_id`, `model_name`, `api_url`, `api_key`, `vision_model`, `is_active`) VALUES
('qwen3-32b', 'Qwen3-32B', 'http://**************:23000/v1', 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553', 'qwen-vl-max', TRUE);

-- 插入默认提示词配置
INSERT INTO `prompt_config` (`analysis_type`, `prompt_key`, `prompt_content`, `description`, `version`) VALUES
('future', 'system_prompt', '这是期货账户分析的默认系统提示词，请根据实际需求进行配置。', '多场景智能化文档分析系统提示词', 'v1.0'),
('financial', 'system_prompt', '这是理财产品分析的默认系统提示词，请根据实际需求进行配置。', '理财产品分析系统提示词', 'v1.0'),
('broker_interest', 'system_prompt', '这是券商计息变更的默认系统提示词，请根据实际需求进行配置。', '券商计息变更系统提示词', 'v1.0'),
('account_opening', 'system_prompt', '这是账户开户场景的默认系统提示词，请根据实际需求进行配置。', '账户开户场景系统提示词', 'v1.0'),
('ningyin_fee', 'system_prompt', '这是宁银费用变更的默认系统提示词，请根据实际需求进行配置。', '宁银费用变更系统提示词', 'v1.0'),
('non_standard_trade', 'system_prompt', '这是非标交易确认单解析的默认系统提示词，请根据实际需求进行配置。', '非标交易确认单解析系统提示词', 'v1.0');

-- 创建索引优化查询性能
CREATE INDEX `idx_analysis_records_composite` ON `analysis_records` (`analysis_type`, `file_status`, `created_at`);
CREATE INDEX `idx_user_activities_composite` ON `user_activities` (`user_id`, `created_at`);
CREATE INDEX `idx_review_records_composite` ON `review_records` (`record_id`, `review_status`, `created_at`);

COMMIT;

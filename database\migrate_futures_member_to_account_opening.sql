-- 数据库迁移脚本：将期货会员类型更改为账户开户场景
-- 执行日期：2025-07-23
-- 说明：将所有 futures_member 类型的数据更新为 account_opening

USE nb_bank_ai_poc;

-- 1. 更新分析记录表中的分析类型
UPDATE analysis_records 
SET analysis_type = 'account_opening' 
WHERE analysis_type = 'futures_member';

-- 2. 更新提示词配置表
UPDATE prompt_config 
SET analysis_type = 'account_opening',
    description = REPLACE(description, '期货交易会员', '账户开户场景'),
    prompt_content = REPLACE(prompt_content, '期货交易会员', '账户开户场景')
WHERE analysis_type = 'futures_member';

-- 3. 更新挡板数据表（如果有相关数据）
UPDATE mock_data 
SET query_type = 'account_opening'
WHERE query_type = 'futures_member';

-- 4. 更新系统设置表（如果有相关配置）
UPDATE global_settings 
SET value = REPLACE(value, 'futures_member', 'account_opening')
WHERE key LIKE '%analysis_types%' OR key LIKE '%type%';

-- 5. 更新用户权限表（如果有相关权限）
UPDATE user_permissions 
SET permission = REPLACE(permission, 'futures_member', 'account_opening')
WHERE permission LIKE '%futures_member%';

-- 6. 更新文件记录表中的分析类型
UPDATE file_records 
SET analysis_type = 'account_opening' 
WHERE analysis_type = 'futures_member';

-- 7. 更新任何可能包含类型信息的JSON字段
UPDATE analysis_records 
SET ai_result = JSON_REPLACE(ai_result, '$.analysis_type', 'account_opening')
WHERE JSON_EXTRACT(ai_result, '$.analysis_type') = 'futures_member';

UPDATE analysis_records 
SET expected_result = JSON_REPLACE(expected_result, '$.analysis_type', 'account_opening')
WHERE JSON_EXTRACT(expected_result, '$.analysis_type') = 'futures_member';

-- 8. 检查更新结果
SELECT 'analysis_records' as table_name, COUNT(*) as count 
FROM analysis_records 
WHERE analysis_type = 'account_opening'
UNION ALL
SELECT 'prompt_config' as table_name, COUNT(*) as count 
FROM prompt_config 
WHERE analysis_type = 'account_opening'
UNION ALL
SELECT 'file_records' as table_name, COUNT(*) as count 
FROM file_records 
WHERE analysis_type = 'account_opening';

-- 9. 验证没有遗留的 futures_member 数据
SELECT 'Remaining futures_member in analysis_records' as check_name, COUNT(*) as count 
FROM analysis_records 
WHERE analysis_type = 'futures_member'
UNION ALL
SELECT 'Remaining futures_member in prompt_config' as check_name, COUNT(*) as count 
FROM prompt_config 
WHERE analysis_type = 'futures_member'
UNION ALL
SELECT 'Remaining futures_member in file_records' as check_name, COUNT(*) as count 
FROM file_records 
WHERE analysis_type = 'futures_member';

COMMIT;

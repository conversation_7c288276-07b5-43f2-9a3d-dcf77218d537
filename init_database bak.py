# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统 - 数据库初始化脚本
"""
import os
import sys
from datetime import datetime, timedelta
import random
from decimal import Decimal
from flask import Flask
from sqlalchemy.exc import IntegrityError
from models import db, User, AnalysisRecord, PromptConfig, GlobalSetting, ModelConfig

def create_app():
    """创建应用实例"""
    app = Flask(__name__)
    
    # 配置数据库
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///document_analysis_system.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # 初始化扩展
    db.init_app(app)
    
    return app

def init_db():
    """初始化数据库"""
    # 创建所有表
    db.create_all()
    print("数据库表创建成功")
    
    # 验证表是否创建成功
    from sqlalchemy import inspect
    inspector = inspect(db.engine)
    tables = inspector.get_table_names()
    print(f"已创建的表: {tables}")
    
    # 检查ModelConfig表是否存在
    if 'model_configs' not in tables:
        print("警告: model_configs 表未成功创建!")
    else:
        print("验证: model_configs 表已创建")
        
    # 检查表的列
    if 'model_configs' in tables:
        columns = [column['name'] for column in inspector.get_columns('model_configs')]
        print(f"model_configs 表的列: {columns}")

def create_admin_user():
    """创建管理员用户"""
    try:
        admin = User(username='admin', role='admin')
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("管理员用户创建成功")
    except IntegrityError:
        db.session.rollback()
        print("管理员用户已存在，跳过创建")

def create_normal_user():
    """创建普通用户"""
    try:
        user = User(username='user', role='user')
        user.set_password('user123')
        db.session.add(user)
        db.session.commit()
        print("普通用户创建成功")
    except IntegrityError:
        db.session.rollback()
        print("普通用户已存在，跳过创建")

def create_analyst_user():
    """创建分析师用户"""
    try:
        analyst = User(username='analyst', role='analyst')
        analyst.set_password('analyst123')
        db.session.add(analyst)
        db.session.commit()
        print("分析师用户创建成功")
    except IntegrityError:
        db.session.rollback()
        print("分析师用户已存在，跳过创建")

def create_default_prompts():
    """创建默认提示词"""
    # 定义默认提示词
    default_prompts = [
        {
            'analysis_type': 'non_standard_trade',
            'prompt_key': 'base_prompt',
            'prompt_content': '你是一名非标交易确认单解析专家...',
            'description': '非标交易确认单基础提示词',
            'version': 'v1.0'
            # 移除 is_default 字段
        },
        {
            'analysis_type': 'futures_account',
            'prompt_key': 'base_prompt',
            'prompt_content': '你是一名期货账户文件解析专家...',
            'description': '期货账户基础提示词',
            'version': 'v1.0'
            # 移除 is_default 字段
        }
    ]
    
    for prompt in default_prompts:
        try:
            existing = PromptConfig.query.filter_by(
                analysis_type=prompt['analysis_type'],
                prompt_key=prompt['prompt_key']
            ).first()
            
            if existing:
                print(f"提示词已存在: {prompt['analysis_type']} - {prompt['prompt_key']}")
                continue
                
            prompt_config = PromptConfig(**prompt)
            db.session.add(prompt_config)
            db.session.commit()
            print(f"创建默认提示词: {prompt['analysis_type']} - {prompt['prompt_key']}")
        except IntegrityError as e:
            db.session.rollback()
            print(f"提示词创建失败: {prompt['analysis_type']} - {prompt['prompt_key']} - {str(e)}")

def create_global_settings():
    """创建全局设置"""
    settings = [
        {
            'key': 'system_name',
            'value': '多场景智能化文档分析系统',
            'description': '系统名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'system_version',
            'value': '2.0.0',
            'description': '系统版本',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'company_name',
            'value': '宁波银行',
            'description': '公司名称',
            'data_type': 'string',
            'is_public': True
        },
        {
            'key': 'auto_analysis',
            'value': 'true',
            'description': '上传后自动分析',
            'data_type': 'boolean',
            'is_public': True
        }
    ]
    
    for setting in settings:
        try:
            global_setting = GlobalSetting(**setting)
            db.session.add(global_setting)
            db.session.commit()
            print(f"创建全局设置: {setting['key']}")
        except IntegrityError:
            db.session.rollback()
            print(f"全局设置已存在: {setting['key']}")

def create_test_analysis_records(db):
    """创建测试分析记录"""
    from models import AnalysisRecord, User
    from datetime import datetime, timedelta
    from sqlalchemy import func
    import random
    import json
    
    print("开始创建测试分析记录...")
    
    # 删除现有记录
    count = db.session.query(AnalysisRecord).delete()
    db.session.commit()
    print(f"删除 {count} 条现有记录")
    
    # 获取用户
    user_id = db.session.query(User.id).filter_by(role='admin').first()[0]
    analyst_id = db.session.query(User.id).filter_by(role='analyst').first()
    if analyst_id:
        analyst_id = analyst_id[0]
    else:
        analyst_id = user_id
    
    # 定义分析类型
    analysis_types = [
        'futures_account', 'wealth_management', 'broker_interest', 
        'account_opening', 'ningxia_bank_fee', 'non_standard_trade'
    ]
    
    # 记录计数
    counts = {
        'futures_account': 80,
        'wealth_management': 60,
        'broker_interest': 40,
        'account_opening': 25,
        'ningxia_bank_fee': 20,
        'non_standard_trade': 15
    }
    
    total_count = 0
    for analysis_type, count in counts.items():
        if analysis_type not in analysis_types:
            continue
            
        print(f"创建 {analysis_type} 类型记录: {count} 条")
        
        for i in range(count):
            # 创建记录
            created_at = datetime.now() - timedelta(days=random.randint(0, 30))
            
            # 随机状态
            status_choices = ['pending', 'processing', 'completed', 'failed']
            status_weights = [0.1, 0.2, 0.6, 0.1]  # 60%的记录是已完成状态
            status = random.choices(status_choices, weights=status_weights)[0]
            
            # 随机复核状态
            review_status_choices = ['pending', 'approved', 'rejected', 'needs_revision']
            review_status_weights = [0.5, 0.3, 0.1, 0.1]  # 50%的记录待复核
            review_status = random.choices(review_status_choices, weights=review_status_weights)[0]
            
            # 随机复核优先级
            priority_choices = ['low', 'normal', 'high']
            priority_weights = [0.3, 0.5, 0.2]  # 大部分是正常优先级
            review_priority = random.choices(priority_choices, weights=priority_weights)[0]
            
            # 随机准确率
            accuracy_score = round(random.uniform(0.6, 1.0), 4) if status == 'completed' else None
            
            record = AnalysisRecord(
                filename=f"{analysis_type}_sample_{i+1}.pdf",
                analysis_type=analysis_type,
                status=status,
                review_status=review_status,
                review_priority=review_priority,
                accuracy_score=accuracy_score,
                created_by=user_id if i % 2 == 0 else analyst_id,
                created_at=created_at,
                updated_at=created_at
            )
            
            # 如果是已完成状态，设置一些结果数据
            if status == 'completed':
                # 模拟AI结果
                ai_result = {
                    "extracted_fields": {
                        "customer_name": f"客户{i+1}",
                        "amount": random.randint(1000, 1000000) / 100,
                        "date": (datetime.now() - timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d")
                    }
                }
                record.set_ai_result(ai_result)
                
                # 模拟预期结果
                expected_result = ai_result.copy()
                # 有时会有一些差异
                if random.random() > 0.8:
                    expected_result["extracted_fields"]["customer_name"] = f"客户名称{i+1}"
                
                record.set_expected_result(expected_result)
                
                # 计算字段准确率
                record.calculate_field_accuracy()
                
            db.session.add(record)
            total_count += 1
            
            # 批量提交
            if i % 20 == 0:
                db.session.commit()
    
    db.session.commit()
    print(f"成功创建 {total_count} 条测试分析记录")

def create_test_review_records(db):
    """创建测试复核记录"""
    from models import AnalysisRecord, ReviewRecord, User
    from datetime import datetime, timedelta
    import random
    import json
    
    print("开始创建测试复核记录...")
    
    # 删除现有复核记录
    from models import ReviewRecord
    count = db.session.query(ReviewRecord).delete()
    db.session.commit()
    print(f"删除 {count} 条现有复核记录")
    
    # 获取已完成的记录
    records = AnalysisRecord.query.filter(
        AnalysisRecord.status == 'completed',
        AnalysisRecord.review_status.in_(['approved', 'rejected', 'needs_revision'])
    ).all()
    
    # 获取分析师用户
    analysts = User.query.filter_by(role='analyst').all()
    if not analysts:
        analysts = User.query.filter_by(role='admin').all()
    
    # 为部分记录创建复核记录
    total_created = 0
    for record in records:
        # 只为50%的记录创建复核记录
        if random.random() > 0.5:
            continue
            
        # 选择一个分析师
        reviewer = random.choice(analysts)
        
        # 创建复核时间
        created_at = record.created_at + timedelta(hours=random.randint(1, 24))
        if created_at > datetime.now():
            created_at = datetime.now() - timedelta(hours=random.randint(1, 10))
        
        # 复核耗时(秒)
        review_time = round(random.uniform(60, 600), 2)  # 1-10分钟
        
        # 创建复核记录
        review = ReviewRecord(
            record_id=record.id,
            reviewer_id=reviewer.id,
            review_status=record.review_status,  # 使用记录中的复核状态
            review_comment=f"复核意见：{'数据准确' if record.review_status == 'approved' else '存在错误需要修正'}",
            corrections={"fields": []} if record.review_status != 'approved' else None,
            review_time=review_time,
            auto_reviewed=False,
            created_at=created_at
        )
        
        db.session.add(review)
        total_created += 1
        
        # 每20条提交一次
        if total_created % 20 == 0:
            db.session.commit()
    
    db.session.commit()
    print(f"成功创建 {total_created} 条测试复核记录")


def create_default_model_configs():
    """创建默认模型配置"""
    models = [
        {
            'model_id': 'gpt-3.5-turbo',
            'model_name': 'GPT-3.5 Turbo',
            'api_url': 'https://api.openai.com/v1',
            'api_key': 'sk-your-openai-api-key',
            'timeout': 60,
            'max_tokens': 4096,
            'temperature': 0.7,
            'is_active': True
        },
        {
            'model_id': 'gpt-4',
            'model_name': 'GPT-4',
            'api_url': 'https://api.openai.com/v1',
            'api_key': 'sk-your-openai-api-key',
            'timeout': 120,
            'max_tokens': 8192,
            'temperature': 0.7,
            'is_active': False
        }
    ]
    
    for model_data in models:
        try:
            existing = ModelConfig.query.filter_by(model_id=model_data['model_id']).first()
            if existing:
                print(f"模型配置已存在: {model_data['model_id']}")
                continue
                
            model = ModelConfig(**model_data)
            db.session.add(model)
            db.session.commit()
            print(f"创建默认模型配置: {model_data['model_id']}")
        except IntegrityError:
            db.session.rollback()
            print(f"模型配置创建失败: {model_data['model_id']}")

def main():
    """主函数"""
    from flask import Flask
    from models import db, User
    from werkzeug.security import generate_password_hash
    
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///document_analysis_system.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db.init_app(app)
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("数据库表创建成功")
        
        # 检查表是否被创建
        from sqlalchemy import inspect
        inspector = inspect(db.engine)
        tables = inspector.get_table_names()
        print(f"已创建的表: {tables}")
        
        # 确认model_configs表是否创建
        if 'model_configs' in tables:
            print("验证: model_configs 表已创建")
            columns = [column['name'] for column in inspector.get_columns('model_configs')]
            print(f"model_configs 表的列: {columns}")
        else:
            print("警告: model_configs 表未创建!")
            
        # 创建用户
        create_admin_user()
        create_normal_user()
        create_analyst_user()
        
        # 创建提示词
        try:
            create_default_prompts()
        except Exception as e:
            print(f"跳过创建默认提示词: {e}")
            
        # 创建其他数据
        create_global_settings()
        create_test_analysis_records(db)
        create_test_review_records(db)  # 添加创建复核记录的调用
        create_default_model_configs()  # 添加默认模型配置
        
        print("数据库初始化完成!")

if __name__ == '__main__':
    main()

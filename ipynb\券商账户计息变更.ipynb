{"cells": [{"cell_type": "code", "execution_count": 1, "id": "57fe6775", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:45:51.022603Z", "start_time": "2025-06-03T08:45:50.981234Z"}, "code_folding": [19, 30, 54, 74, 88, 104, 150]}, "outputs": [], "source": ["from util_img_process import *"]}, {"cell_type": "code", "execution_count": 2, "id": "d2fb523c", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:51:03.141606Z", "start_time": "2025-06-03T08:51:03.135551Z"}}, "outputs": [], "source": ["img_prompt = \"\"\"请提取图中全部的：['客户号（账号）','资产名称','计息利率(年化)','计息起始日']，请以JSON格式输出\n", "注意：\n", "1. 请完整提取，请勿遗漏\n", "2. 对跨页数据的拼接需仔细思考后处理\n", "\n", "输出格式样例：\n", "```json\n", "[\n", "\t{\"客户号（账号）\": \"22900711\", \"资产名称\": \"汇添富远景成长一年持有期混合型证券投资基金\", \"计息利率(年化)\": \"1.4%\", \"计息起始日\": \"2025年1月2日\"},\n", "\t{\"客户号（账号）\": \"22920228\", \"资产名称\": \"汇添富稳利60天滚动持有短债债券型证券投资基金\", \"计息利率(年化)\": \"1.4%\", \"计息起始日\": \"未提及\"}\n", "]\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 3, "id": "40a98099", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:52:57.397957Z", "start_time": "2025-06-03T08:52:55.846876Z"}, "scrolled": true}, "outputs": [], "source": ["pdf_file_name = \"test_data/大模型样例/券商账户计息变更/关于调整人民币客户保证金利率的通知.pdf_1745473420969.pdf\"\n", "if pdf_file_name.endswith('.pdf'):\n", "    process_pdf(\n", "            pdf_path=pdf_file_name,     # 输入PDF路径\n", "            output_path=\"output.jpg\", # 输出图片路径\n", "            dpi=200,                  # 控制转换质量（推荐150-300）\n", "            border=15                 # 内容边界保留距离（像素）\n", "        )\n", "    res = chat_bot_img(img_prompt, img_url='output.jpg')\n", "else:\n", "    res = chat_bot_img(img_prompt, img_url=pdf_file_name)"]}, {"cell_type": "code", "execution_count": 4, "id": "0ae4b9f7", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:52:58.068316Z", "start_time": "2025-06-03T08:52:58.061326Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "\t{\"客户号（账号）\": \"31006666108800297534\", \"资产名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\", \"计息利率(年化)\": \"0.000%\", \"计息起始日\": \"2019年9月26日\"}\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "d8ea3f76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 66, "id": "57fe6775", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:45:51.022603Z", "start_time": "2025-06-03T08:45:50.981234Z"}, "code_folding": [19, 30, 54, 74, 88, 104, 150]}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import *\n", "from mineru_pdf import *\n", "from PIL import Image\n", "import json\n", "from util_img_process import process_pdf\n", "import hashlib"]}, {"cell_type": "code", "execution_count": 67, "id": "f71b7675", "metadata": {}, "outputs": [], "source": ["def cal_file_md5(fn):\n", "    \"\"\"\n", "    计算文件的md5值\n", "    \"\"\"\n", "    with open(fn, 'rb') as f:\n", "        return hashlib.md5(f.read()).hexdigest()\n", "\n", "def pdf2img(fn):\n", "    \"\"\"\n", "    强制使用ocr，转换pdf为图片形式的pdf\n", "    \"\"\"\n", "    # 如果是PDF文件，先转换为图片\n", "    if fn.lower().endswith('.pdf'):\n", "        md5_value = cal_file_md5(fn)\n", "        processed_filename = f\"processed_{md5_value}.jpg\"\n", "        os.makedirs('./temp', exist_ok=True)\n", "        processed_filepath = f\"./temp/{processed_filename}\"\n", "\n", "        # 使用PDF处理函数\n", "        process_pdf(fn, processed_filepath)\n", "        analysis_filepath = processed_filepath\n", "    else:\n", "        analysis_filepath = fn\n", "    return analysis_filepath"]}, {"cell_type": "code", "execution_count": 94, "id": "ffac0a1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成：结果已保存至 ./temp/processed_2157db222a832bd477e9e860271471a0.jpg\n"]}], "source": ["\n", "fn = \"../大模型样例/券商账户计息变更/关于调整人民币客户保证金利率的通知.pdf_1745473420969.pdf\"\n", "# 将pdf转为图片形式的pdf,以启用ocr\n", "# 强制OCR\n", "fn = pdf2img(fn)\n", "if fn.lower().endswith('.pdf'):\n", "    out_pdf_fn = fn\n", "else:\n", "    # 将图片转为pdf\n", "    fn_end = fn.split('.')[-1]  \n", "    fix_fn = \".\".join(fn.split('.')[:-1]) + \".pdf\"\n", "    out_pdf_fn = convert_images_to_pdf(fn, fix_fn)\n", "\n", "markdown_data = trans_pdf_to_markdown(out_pdf_fn, parse_method='ocr')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 95, "id": "c9e14cf7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 国泰君安证券股份有限公司\n", "\n", "公告［2024]692号\n", "\n", "# 关于调整人民币客户保证金利率的通知\n", "\n", "# 各分公司、各营业部：\n", "\n", "参照主要商业银行近期活期存款利率的调整情况，公司经研究决定，自2024年11月11日起，经纪业务、融资融券业务、交易所股票期权业务、贵金属现货合约代理业务人民币客户保证金标准年利率由原来的 $0 . 1 5 \\%$ 调整为 $0 , 1 0 \\%$ 。\n", "\n", "具体提示如下：\n", "\n", "1、总部将于11月11日统一将营业部默认利率模板的活期利率值从 $0 . 1 5 \\%$ 下调至 $0 . 1 0 \\%$ 。此次调整不涉及营业部的非默认利率模板，非默认利率仍由营业部自行设置。 安2、适用默认利率模板的个人客户不实行分段计息，2024年4季度统一按 $0 , 1 0 \\%$ 的年利率计息；适用默认利率模板的非个人客户实行分段计息，11月11日之前产生的应计利息按 $0 , 1 5 \\%$ 年利率计算，11月11日起（含当日）产生的应计利息按 $ { \\mathrm { : 0 . 1 0 \\% } }$ 年利率计算。3、对于产品客户，请营业部梳理相关服务协议中的利率条款约定，确保按照协议约定执行并做好客户沟通及通知工作。\n", "\n", "特此通知，请各单位遵照执行。\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 91, "id": "926eaac7", "metadata": {}, "outputs": [], "source": ["sys_prompt = \"\"\"你是一个专业的银行托管部经理，负责从客户给定的内容中提取基金产品的计息变更信息，并按以下要求结构化输出：\n", "1. 提取字段：\n", "   - 【产品名称】指基金名称或系列基金的名称，如未提及具体的产品或系列产品，或明确提及全公司所有产品都发生了计息变更，则提取全公司名称\n", "   - 【产品类别】单产品、系列产品、全公司产品\n", "   - 【利率(年化)】利率信息，如包含分段利率，则根据起止日期，拆分输出\n", "   - 【开始时间】格式化为YYYY-MM-DD\n", "   - 【截止时间】格式化为YYYY-MM-DD，未提及则留空\n", "   - 【计息天数】一般可能为360、365或者实际天数，需要根据文档中对于计息天数的描述转换为数字，如果文档中表明实际天数，则返回“实际天数”四个字，如果文档没有展示可以为空\n", "   - 【备注】备注信息\n", "\n", "2. 特殊要求：\n", "   - 日期必须转换为YYYY-MM-DD格式（示例：2025年1月2日→2025-01-02）\n", "   - 利率保留原始百分比符号（如1.4%）\n", "   - 跨页数据需逻辑拼接\n", "   - 产品名称必定为XX理财、XX基金、XX系列、XX公司、XX全公司\n", "   - 当内容中没有提及具体的产品或系列，则提取全公司名称，同时产品类别为全公司产品\n", "\n", "3. 输出格式：\n", "```json\n", "[\n", "  {\n", "    \"产品名称\": \"汇添富远景成长系列\",\n", "    \"产品类别\": \"系列产品\",\n", "    \"利率(年化)\": \"0-30天:1.2%;31-60天:1.5%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"2025-06-30\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"\"\n", "  },\n", "  {\n", "    \"产品名称\": \"稳利债券基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%;1.8%\",\n", "    \"开始时间\": \"2025-03-01\",\n", "    \"截止时间\": \"2025-06-30\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"非个人客户实行分段计息，7月1日之前产生的应计利息按1.4%年利率计算，7月1日起（含当日）产生的应计利息按1.8%年利率计算\"\n", "  }\n", "]\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 92, "id": "c1b1fc6d", "metadata": {}, "outputs": [], "source": ["chat_bot = ChatBot(system_prompt=sys_prompt)\n", "\n", "res = chat_bot.chat(messages=[{\"role\": \"user\", \"content\": markdown_content}])"]}, {"cell_type": "code", "execution_count": 93, "id": "d2fb523c", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:51:03.141606Z", "start_time": "2025-06-03T08:51:03.135551Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "  {\n", "    \"产品名称\": \"汇添富远景成长一年持有期混合型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳利60天滚动持有短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富成长领先混合型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富北证50成份指数型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证800指数增强型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证2000交易型开放式指数证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳益60天持有期债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证信息技术应用创新产业交易型开放式指数证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富添添乐双鑫债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳福60天滚动持有中短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富基金管理股份有限公司\",\n", "    \"产品类别\": \"全公司产品\",\n", "    \"利率(年化)\": \"1.4%\",\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月支付利息，支付时间为每月20号之前\"\n", "  }\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 4, "id": "0ae4b9f7", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:52:58.068316Z", "start_time": "2025-06-03T08:52:58.061326Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "\t{\"客户号（账号）\": \"31006666108800297534\", \"资产名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\", \"计息利率(年化)\": \"0.000%\", \"计息起始日\": \"2019年9月26日\"}\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "d8ea3f76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "ocr"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "id": "57fe6775", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:45:51.022603Z", "start_time": "2025-06-03T08:45:50.981234Z"}, "code_folding": [19, 30, 54, 74, 88, 104, 150]}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import *\n", "from mineru_pdf import *\n", "import json\n", "from util_img_process import process_pdf\n", "import hashlib"]}, {"cell_type": "code", "execution_count": 2, "id": "ffac0a1e", "metadata": {}, "outputs": [], "source": ["\n", "fn = \"../大模型样例/券商账户计息变更/2024_01_18_利率说明函.pdf_1733482509324.pdf_1745473248502.pdf\"\n", "# 将pdf转为图片形式的pdf,以启用ocr\n", "# 强制OCR\n", "markdown_data = trans_pdf_to_markdown(fn, parse_method='ocr', backend='vlm-sglang-engine')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 3, "id": "c9e14cf7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 利率说明函\n", "\n", "普商银行股份有限公司：\n", "\n", "贵公司 晋商银行击败理财产品 已在我司开立普通证券资金账户，账号：_763677，该账户的资金余额按季度支付活期存款利率，年利率模板为_0.2%（年化）。\n", "\n", "特此说明\n", "\n", "![](images/6c28f6a9d068f62db0a451f5a6923917df4e8e69224842667af8b348170796a2.jpg)\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 4, "id": "926eaac7", "metadata": {}, "outputs": [], "source": ["sys_prompt = \"\"\"你是一位资深的银行托管部经理，负责从客户提供的文件（邮件、公告、合同、通知等）中**抽取基金计息变更要素**，并按以下要求结构化输出 JSON 数组。\n", "\n", "=====================【字段定义】=====================\n", "1. 【产品名称】\n", "   • 如果正文**出现了具体的基金、系列基金**名称，就逐一提取这些名称；  \n", "   • 只有在正文出现 “本公司全部产品 / 全公司所有基金 / X X 公司所有产品”等措辞，**且未列举任何具体产品**时，才将“X X 公司”作为【产品名称】，并认定为“全公司产品”。\n", "\n", "2. 【产品类别】枚举值：单产品 / 系列产品 / 全公司产品  \n", "   • 判断依据同上：列举多只不同基金 ⇒ “单产品”（多行输出）；  \n", "     明确写“系列基金 / 系列产品” ⇒ “系列产品”；  \n", "     出现“全公司全部产品”且未列举 ⇒ “全公司产品”。\n", "\n", "3. 【利率(年化)】——*嵌套 JSON*  \n", "   用 **键值对** 表示分段利率，遵循以下规则：  \n", "   • **单一利率** → `{\"all\": \"X.X%\"}`  \n", "   • **按客户类型分段** → 外层键为 “个人”“非个人”“机构”等；值可以是：  \n", "     - 单一值 → `\"0.10%\"`  \n", "     - **再按日期分段** → 内层对象，键格式：  \n", "       - `START:YYYY-MM-DD` 表示 *起始至该日（含）*  \n", "       - `YYYY-MM-DD:END` 表示 *该日起（含）至结束*  \n", "       - `YYYY-MM-DD至YYYY-MM-DD` 表示闭区间  \n", "     例：`\"非个人\": {\"START:2024-11-10\": \"0.15%\", \"2024-11-11:END\": \"0.10%\"}`  \n", "   • **仅按日期分段**（不区分客户类型） → 直接用内层对象：  \n", "     `{\"START:2025-01-01\": \"1.5%\", \"2025-07-01:END\": \"1.2%\"}`  \n", "   • 多维分段时，优先按 **客户类型** 外层、**日期** 内层。\n", "\n", "4. 【开始时间】【截止时间】  \n", "   • 如文档给出单一“计息起始日”，则写入【开始时间】；【截止时间】留空，除非正文给出明确结束日期。  \n", "   • 如果分段计息，每条分段各自写一行 JSON（推荐）或在【利率(年化)】内注明日期区间并将最早日期写入【开始时间】，最晚日期写入【截止时间】。\n", "\n", "5. 【计息天数】  \n", "   • 常见值 360 / 365 / “实际天数”。若文档只出现“/360”或“/365”，直接填数字；若写“按实际天数”，填“实际天数”；否则留空。\n", "\n", "6. 【备注】  \n", "   • 放无法归入前述字段但对计息有影响的信息，如“利率随市场调整”“按月结息至期货账户”等。  \n", "   • 对于按客户类型分段的说明，也可以简要复述，以便人工复核。\n", "\n", "=====================【格式要求】=====================\n", "• 日期全部转为 `YYYY-MM-DD`。  \n", "• 跨页或 HTML 表格拆分的内容需合并后再识别。  \n", "• 输出 **JSON 数组**，字段顺序固定，示例如下：\n", "\n", "```json\n", "[\n", "  {\n", "    \"产品名称\": \"汇添富远景成长一年持有期混合型基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"国泰君安证券股份有限公司\",\n", "    \"产品类别\": \"全公司产品\",\n", "    \"利率(年化)\": {\"个人\": \"0.10%\", \"非个人\": {\"START:2024-11-10\": \"0.15%\", \"2024-11-11:END\": \"0.10%\"}},\n", "    \"开始时间\": \"2024-11-11\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": \"\",\n", "    \"备注\": \"经纪/两融/期权/贵金属保证金统一执行\"\n", "  }\n", "]\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "c1b1fc6d", "metadata": {}, "outputs": [], "source": ["chat_bot = ChatBot(system_prompt=sys_prompt)\n", "\n", "res = chat_bot.chat(messages=[{\"role\": \"user\", \"content\": markdown_content}], top_p=0.5, temperature=0.3)"]}, {"cell_type": "code", "execution_count": 6, "id": "d2fb523c", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:51:03.141606Z", "start_time": "2025-06-03T08:51:03.135551Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "  {\n", "    \"产品名称\": \"晋商银行击败理财产品\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"0.2%\"},\n", "    \"开始时间\": \"\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": \"\",\n", "    \"备注\": \"资金余额按季度支付活期存款利率\"\n", "  }\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 4, "id": "0ae4b9f7", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:52:58.068316Z", "start_time": "2025-06-03T08:52:58.061326Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "\t{\"客户号（账号）\": \"31006666108800297534\", \"资产名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\", \"计息利率(年化)\": \"0.000%\", \"计息起始日\": \"2019年9月26日\"}\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "d8ea3f76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
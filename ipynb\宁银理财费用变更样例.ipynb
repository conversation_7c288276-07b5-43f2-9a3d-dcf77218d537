{"cells": [{"cell_type": "code", "execution_count": 2, "id": "dd8d5298", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:57:14.448462Z", "start_time": "2025-06-03T01:57:14.430596Z"}}, "outputs": [], "source": ["import docx\n", "import json\n", "from docx.table import Table, _Cell, _Row\n", "from docx.text.paragraph import Paragraph\n", "import logging\n", "from docx.oxml import parse_xml\n", "from docx.oxml.ns import nsdecls\n", "from copy import deepcopy\n", "import re\n", "from docx.oxml.ns import qn\n", "import pandas as pd\n", "import os\n", "from pdf2docx import Converter\n", "import uuid\n", "import PyPDF2\n", "import tqdm\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b8ccb5ec", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:52:33.808949Z", "start_time": "2025-06-03T01:52:33.801853Z"}, "scrolled": true}, "outputs": [], "source": ["def pdf_to_word_pdf2docx(pdf_path, word_path):\n", "    cv = Converter(pdf_path)\n", "    cv.convert(word_path, start=0, end=None)\n", "    cv.close()"]}, {"cell_type": "code", "execution_count": 4, "id": "0a10300d", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:41:28.587183Z", "start_time": "2025-06-03T01:41:28.565795Z"}, "code_folding": [0, 14, 45]}, "outputs": [], "source": ["def check_vertical_merge(cell):\n", "    tc = cell._tc\n", "    tc_pr = tc.get_or_add_tcPr()\n", "    vmerge = tc_pr.find(qn(\"w:vMerge\"))\n", "    \n", "    if vmerge is not None:\n", "        val = vmerge.get(qn(\"w:val\"))\n", "        # 特殊处理多个restart的情况\n", "        if val == \"restart\":\n", "            return \"restart\"\n", "        elif val == \"continue\":\n", "            return \"continue\"\n", "    return \"None\"\n", "\n", "def fix_merge_table(fix_table):\n", "    new_talbe = pd.DataFrame()\n", "    min_length = {}\n", "    for table_i, table in fix_table.groupby('table_id'):\n", "        if table_i == 0:\n", "            new_talbe = table.copy()\n", "            # 固化字段长度用以校验\n", "            for col in table.columns:\n", "                min_length[col] = table[col].iloc[:-1].astype(str).map(len).mean()\n", "        else:\n", "            first_row = table.iloc[0]\n", "            # 计算字段达成率\n", "            success_num = 0\n", "            for col in table.columns:\n", "                if len(str(first_row[col])) >= min_length[col]:\n", "                    success_num += 1\n", "            success_ratio = success_num / len(min_length)\n", "\n", "            # 如果字段达成率低于70%则合并\n", "            if success_ratio <= 0.7:\n", "                for col in table.columns:\n", "                    if col == 'table_id':\n", "                        continue\n", "                    fix_idx = new_talbe[new_talbe[col]!='!restart!'].iloc[-1].name\n", "                    new_talbe.loc[fix_idx, col] += first_row[col].strip() if isinstance(first_row[col], str) else first_row[col]\n", "                table = table.iloc[1:]\n", "            new_talbe = pd.concat([new_talbe, table], ignore_index=True)\n", "    fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "    del fin_table['table_id']\n", "    return fin_table\n", "\n", "def df_to_json_rows(df):\n", "    json_list = df.to_dict(orient='records')\n", "    return json.dumps(json_list, indent=2, ensure_ascii=False)"]}, {"cell_type": "code", "execution_count": 5, "id": "9fab3879", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:58:25.037861Z", "start_time": "2025-06-03T01:58:25.016799Z"}, "code_folding": [13], "scrolled": true}, "outputs": [], "source": ["def pdf_to_markdown(pdf_fn):\n", "    temp_fn = f\"{uuid.uuid1().hex}.docx\"\n", "    # 使用示例\n", "    pdf_to_word_pdf2docx(pdf_fn, temp_fn)\n", "    # 加载Word文档\n", "    doc = docx.Document(temp_fn)\n", "    out_markdown = \"\"\n", "\n", "    table_list = []\n", "    now_table = None\n", "    header = []\n", "    child_i = 0\n", "    table_id = 0\n", "    for child in doc.element.body:\n", "    #     print(child_i, type(child))\n", "        if isinstance(child, docx.oxml.text.paragraph.CT_P):  # 段落\n", "            if child.text.replace('\\n', '').strip() == '':\n", "                continue\n", "            if now_table is not None:\n", "                print(child.text.replace('\\n', '').strip())\n", "                print(\"重置表格\")\n", "                out_markdown += \"\\n```json\\n\" + df_to_json_rows(fix_merge_table(now_table)) + \"\\n```\\n\"\n", "                now_table = None\n", "                header = []\n", "                table_id = 0\n", "            out_markdown += child.text.replace('\\n', '').strip() + '\\n'\n", "        elif isinstance(child, docx.oxml.table.CT_Tbl): # 表格\n", "            data = []\n", "            table = Table(child, doc)\n", "            last_tc = {}\n", "            for row in table.rows:\n", "                row_data = []\n", "                cols_id = 0\n", "                for cell in row.cells:\n", "                    tc = cell._tc  # 获取底层 XML 元素\n", "                    # 检查是否是合并单元格的后续行\n", "                    if check_vertical_merge(cell) == \"restart\":\n", "                        if last_tc.get(cols_id, None) is None:\n", "                            last_tc[cols_id] = tc\n", "                        else:\n", "                            row_data.append('!restart!')  # 非首行合并单元格，输出空\n", "                            continue\n", "\n", "                    # 否则提取文本并清理\n", "                    text = cell.text.strip().replace('\\n', '').replace('\\t', ' ')\n", "                    row_data.append(text)\n", "                    cols_id += 1\n", "                data.append(row_data)\n", "\n", "            if now_table is None:\n", "                header = data[0]\n", "                data = data[1:]\n", "            out_df = pd.DataFrame(data, columns=header)\n", "            out_df['table_id'] = table_id\n", "            if now_table is None:\n", "                now_table = out_df.copy()\n", "            else:\n", "                now_table = pd.concat([now_table, out_df])\n", "            table_id += 1\n", "        child_i += 1\n", "    \n", "    os.remove(temp_fn)\n", "    return out_markdown"]}, {"cell_type": "code", "execution_count": 6, "id": "984967e5", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:42.838347Z", "start_time": "2025-06-03T01:54:42.826771Z"}, "code_folding": [0]}, "outputs": [], "source": ["def is_scanned_pdf(pdf_path, threshold=0.9):\n", "    \"\"\"\n", "    判断PDF是否为扫描件\n", "    :param pdf_path: PDF文件路径\n", "    :param threshold: 判断为扫描件的阈值（页面中可提取文本的比例）\n", "    :return: True（扫描件）或 False（可转换文本）\n", "    \"\"\"\n", "    try:\n", "        # 方法1：检查PDF中是否包含可提取文本\n", "        with open(pdf_path, 'rb') as file:\n", "            reader = PyPDF2.PdfReader(file)\n", "            total_pages = len(reader.pages)\n", "            text_pages = 0\n", "            \n", "            for page in reader.pages:\n", "                text = page.extract_text()\n", "                if text and len(text.strip()) > 10:  # 如果有可读文本\n", "                    text_pages += 1\n", "            \n", "            text_ratio = text_pages / total_pages\n", "            \n", "            # 如果大部分页面都有可提取文本，则认为是可转换PDF\n", "            if text_ratio > threshold:\n", "                return False\n", "    except:\n", "        traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 7, "id": "1f33976f", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:43.163043Z", "start_time": "2025-06-03T01:54:43.156272Z"}}, "outputs": [], "source": ["test_path = \"test_data/宁银理财费用变更样例\"\n", "pdf_fns = [x for x in os.listdir(test_path) if x.lower().endswith('pdf')]"]}, {"cell_type": "code", "execution_count": 11, "id": "392be698", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:43.673719Z", "start_time": "2025-06-03T01:54:43.668715Z"}}, "outputs": [], "source": ["pdf_fn = \"关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf\""]}, {"cell_type": "code", "execution_count": 13, "id": "d30171e2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert /app/宁波银行POC/test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] Terminated in 6.25s.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：（1）自优惠起始日开始，投资者在赎回时，均不收取浮动管理费。（2）\n", "重置表格\n", "宁银理财有限责任公司\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n"]}], "source": ["markdown_data = pdf_to_markdown('/app/宁波银行POC/test_data/宁银理财费用变更样例/' + pdf_fn)"]}, {"cell_type": "code", "execution_count": 18, "id": "6a72988a", "metadata": {}, "outputs": [], "source": ["pdf_fn = \"test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf\""]}, {"cell_type": "code", "execution_count": 20, "id": "a7c89d1a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] Terminated in 6.35s.\n"]}], "source": ["temp_fn = f\"{uuid.uuid1().hex}.docx\"\n", "# 使用示例\n", "pdf_to_word_pdf2docx(pdf_fn, temp_fn)\n", "# 加载Word文档\n", "doc = docx.Document(temp_fn)"]}, {"cell_type": "code", "execution_count": 21, "id": "bbbaf271", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0 <class 'docx.oxml.text.paragraph.CT_P'>\n", "0 <class 'docx.oxml.text.paragraph.CT_P'>\n", "1 <class 'docx.oxml.text.paragraph.CT_P'>\n", "2 <class 'docx.oxml.text.paragraph.CT_P'>\n", "3 <class 'docx.oxml.table.CT_Tbl'>\n", "4 <class 'docx.oxml.text.paragraph.CT_P'>\n", "4 <class 'docx.oxml.text.paragraph.CT_P'>\n", "4 <class 'docx.oxml.text.paragraph.CT_P'>\n", "4 <class 'docx.oxml.table.CT_Tbl'>\n", "5 <class 'docx.oxml.text.paragraph.CT_P'>\n", "5 <class 'docx.oxml.text.paragraph.CT_P'>\n", "5 <class 'docx.oxml.text.paragraph.CT_P'>\n", "5 <class 'docx.oxml.table.CT_Tbl'>\n", "6 <class 'docx.oxml.text.paragraph.CT_P'>\n", "6 <class 'docx.oxml.text.paragraph.CT_P'>\n", "6 <class 'docx.oxml.text.paragraph.CT_P'>\n", "6 <class 'docx.oxml.table.CT_Tbl'>\n", "7 <class 'docx.oxml.text.paragraph.CT_P'>\n", "7 <class 'docx.oxml.text.paragraph.CT_P'>\n", "7 <class 'docx.oxml.text.paragraph.CT_P'>\n", "7 <class 'docx.oxml.table.CT_Tbl'>\n", "8 <class 'docx.oxml.text.paragraph.CT_P'>\n", "8 <class 'docx.oxml.text.paragraph.CT_P'>\n", "8 <class 'docx.oxml.text.paragraph.CT_P'>\n", "8 <class 'docx.oxml.table.CT_Tbl'>\n", "9 <class 'docx.oxml.text.paragraph.CT_P'>\n", "9 <class 'docx.oxml.text.paragraph.CT_P'>\n", "9 <class 'docx.oxml.text.paragraph.CT_P'>\n", "9 <class 'docx.oxml.table.CT_Tbl'>\n", "10 <class 'docx.oxml.text.paragraph.CT_P'>\n", "10 <class 'docx.oxml.text.paragraph.CT_P'>\n", "10 <class 'docx.oxml.text.paragraph.CT_P'>\n", "10 <class 'docx.oxml.table.CT_Tbl'>\n", "11 <class 'docx.oxml.text.paragraph.CT_P'>\n", "11 <class 'docx.oxml.text.paragraph.CT_P'>\n", "11 <class 'docx.oxml.text.paragraph.CT_P'>\n", "11 <class 'docx.oxml.table.CT_Tbl'>\n", "12 <class 'docx.oxml.text.paragraph.CT_P'>\n", "12 <class 'docx.oxml.text.paragraph.CT_P'>\n", "12 <class 'docx.oxml.text.paragraph.CT_P'>\n", "12 <class 'docx.oxml.table.CT_Tbl'>\n", "13 <class 'docx.oxml.text.paragraph.CT_P'>\n", "13 <class 'docx.oxml.text.paragraph.CT_P'>\n", "13 <class 'docx.oxml.text.paragraph.CT_P'>\n", "13 <class 'docx.oxml.table.CT_Tbl'>\n", "14 <class 'docx.oxml.text.paragraph.CT_P'>\n", "14 <class 'docx.oxml.text.paragraph.CT_P'>\n", "14 <class 'docx.oxml.text.paragraph.CT_P'>\n", "14 <class 'docx.oxml.table.CT_Tbl'>\n", "15 <class 'docx.oxml.text.paragraph.CT_P'>\n", "15 <class 'docx.oxml.text.paragraph.CT_P'>\n", "15 <class 'docx.oxml.text.paragraph.CT_P'>\n", "15 <class 'docx.oxml.table.CT_Tbl'>\n", "16 <class 'docx.oxml.text.paragraph.CT_P'>\n", "16 <class 'docx.oxml.text.paragraph.CT_P'>\n", "16 <class 'docx.oxml.text.paragraph.CT_P'>\n", "16 <class 'docx.oxml.table.CT_Tbl'>\n", "17 <class 'docx.oxml.text.paragraph.CT_P'>\n", "17 <class 'docx.oxml.text.paragraph.CT_P'>\n", "17 <class 'docx.oxml.text.paragraph.CT_P'>\n", "17 <class 'docx.oxml.table.CT_Tbl'>\n", "18 <class 'docx.oxml.text.paragraph.CT_P'>\n", "注*：（1）自优惠起始日开始，投资者在赎回时，均不收取浮动管理费。（2）\n", "重置表格\n", "19 <class 'docx.oxml.text.paragraph.CT_P'>\n", "20 <class 'docx.oxml.text.paragraph.CT_P'>\n", "21 <class 'docx.oxml.text.paragraph.CT_P'>\n", "22 <class 'docx.oxml.table.CT_Tbl'>\n", "23 <class 'docx.oxml.text.paragraph.CT_P'>\n", "宁银理财有限责任公司\n", "重置表格\n", "24 <class 'docx.oxml.text.paragraph.CT_P'>\n", "25 <class 'docx.oxml.section.CT_SectPr'>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n"]}], "source": ["out_markdown = \"\"\n", "\n", "table_list = []\n", "now_table = None\n", "header = []\n", "child_i = 0\n", "table_id = 0\n", "for child in doc.element.body:\n", "    print(child_i, type(child))\n", "    if isinstance(child, docx.oxml.text.paragraph.CT_P):  # 段落\n", "        if child.text.replace('\\n', '').strip() == '':\n", "            continue\n", "        if now_table is not None:\n", "            print(child.text.replace('\\n', '').strip())\n", "            print(\"重置表格\")\n", "            out_markdown += \"\\n```json\\n\" + df_to_json_rows(fix_merge_table(now_table)) + \"\\n```\\n\"\n", "            now_table = None\n", "            header = []\n", "            table_id = 0\n", "        out_markdown += child.text.replace('\\n', '').strip() + '\\n'\n", "    elif isinstance(child, docx.oxml.table.CT_Tbl): # 表格\n", "        data = []\n", "        table = Table(child, doc)\n", "        last_tc = {}\n", "        for row in table.rows:\n", "            row_data = []\n", "            cols_id = 0\n", "            for cell in row.cells:\n", "                tc = cell._tc  # 获取底层 XML 元素\n", "                # 检查是否是合并单元格的后续行\n", "                if check_vertical_merge(cell) == \"restart\":\n", "                    if last_tc.get(cols_id, None) is None:\n", "                        last_tc[cols_id] = tc\n", "                    else:\n", "                        row_data.append('!restart!')  # 非首行合并单元格，输出空\n", "                        continue\n", "\n", "                # 否则提取文本并清理\n", "                text = cell.text.strip().replace('\\n', '').replace('\\t', ' ')\n", "                row_data.append(text)\n", "                cols_id += 1\n", "            data.append(row_data)\n", "\n", "        if now_table is None:\n", "            header = data[0]\n", "            data = data[1:]\n", "        out_df = pd.DataFrame(data, columns=header)\n", "        out_df['table_id'] = table_id\n", "        if now_table is None:\n", "            now_table = out_df.copy()\n", "        else:\n", "            now_table = pd.concat([now_table, out_df])\n", "        table_id += 1\n", "    child_i += 1"]}, {"cell_type": "code", "execution_count": 10, "id": "c358a9f0", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:58:59.525342Z", "start_time": "2025-06-03T01:58:26.532586Z"}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/14 [00:00<?, ?it/s][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分现金管理类产品固定管理费及销售服务费优惠的公告(1.24).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 3.84s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "  7%|▋         | 1/14 [00:04<00:52,  4.03s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠起始日及截止日当日均享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分宁欣日日薪产品固定管理费及销售服务费优惠的公(1.24).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/4) Page 1\n", "[INFO] (2/4) Page 2\n", "[INFO] (3/4) Page 3\n", "[INFO] (4/4) Page 4\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/4) Page 1\n", "[INFO] (2/4) Page 2\n", "[INFO] (3/4) Page 3\n", "[INFO] (4/4) Page 4\n", "[INFO] Terminated in 8.60s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 14%|█▍        | 2/14 [00:13<01:23,  6.94s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品费率优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠起始日及截止日当日均享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/3) Page 1\n", "[INFO] (2/3) Page 2\n", "[INFO] (3/3) Page 3\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/3) Page 1\n", "[INFO] (2/3) Page 2\n", "[INFO] (3/3) Page 3\n", "[INFO] Terminated in 1.69s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 21%|██▏       | 3/14 [00:14<00:50,  4.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费及浮动管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/15) Page 1\n", "[INFO] (2/15) Page 2\n", "[INFO] (3/15) Page 3\n", "[INFO] (4/15) Page 4\n", "[INFO] (5/15) Page 5\n", "[INFO] (6/15) Page 6\n", "[INFO] (7/15) Page 7\n", "[INFO] (8/15) Page 8\n", "[INFO] (9/15) Page 9\n", "[INFO] (10/15) Page 10\n", "[INFO] (11/15) Page 11\n", "[INFO] (12/15) Page 12\n", "[INFO] (13/15) Page 13\n", "[INFO] (14/15) Page 14\n", "[INFO] (15/15) Page 15\n", "[INFO] Terminated in 6.38s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 29%|██▊       | 4/14 [00:21<00:55,  5.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：（1）自优惠起始日开始，投资者在赎回时，均不收取浮动管理费。（2）\n", "重置表格\n", "宁银理财有限责任公司\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费和销售服务费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/9) Page 1\n", "[INFO] (2/9) Page 2\n", "[INFO] (3/9) Page 3\n", "[INFO] (4/9) Page 4\n", "[INFO] (5/9) Page 5\n", "[INFO] (6/9) Page 6\n", "[INFO] (7/9) Page 7\n", "[INFO] (8/9) Page 8\n", "[INFO] (9/9) Page 9\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/9) Page 1\n", "[INFO] (2/9) Page 2\n", "[INFO] (3/9) Page 3\n", "[INFO] (4/9) Page 4\n", "[INFO] (5/9) Page 5\n", "[INFO] (6/9) Page 6\n", "[INFO] (7/9) Page 7\n", "[INFO] (8/9) Page 8\n", "[INFO] (9/9) Page 9\n", "[INFO] Terminated in 7.57s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 36%|███▌      | 5/14 [00:29<00:58,  6.46s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费优惠的公告(1.24).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Terminated in 0.26s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 43%|████▎     | 6/14 [00:30<00:34,  4.37s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财部分产品固定管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 0.49s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 50%|█████     | 7/14 [00:30<00:21,  3.12s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财晶耀固定收益类封闭式人民币理财170号产品固定管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Terminated in 0.26s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 57%|█████▋    | 8/14 [00:31<00:13,  2.23s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣日日薪39号浮动管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 0.25s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 64%|██████▍   | 9/14 [00:31<00:08,  1.62s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣日日薪39号产品固定管理费及销售服务费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注：（1）自优惠起始日开始，投资者在赎回时，均不收取浮动管理费。（2）优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Terminated in 0.26s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 71%|███████▏  | 10/14 [00:31<00:04,  1.22s/it][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣固定收益类封闭式理财2287号产品固定管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Terminated in 0.20s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 79%|███████▊  | 11/14 [00:31<00:02,  1.09it/s][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣固定收益类封闭式理财2267号产品固定管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 0.31s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 86%|████████▌ | 12/14 [00:32<00:01,  1.33it/s][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣固定收益类封闭式理财2256号产品费用优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 0.31s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", " 93%|█████████▎| 13/14 [00:32<00:00,  1.57it/s][INFO] Start to convert test_data/宁银理财费用变更样例/关于宁银理财宁欣固定收益类封闭式理财1906号产品固定管理费优惠的公告(1.23).pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 0.35s.\n", "/tmp/ipykernel_70706/4244119367.py:42: FutureWarning: DataFrame.fillna with 'method' is deprecated and will raise in a future version. Use obj.ffill() or obj.bfill() instead.\n", "  fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "100%|██████████| 14/14 [00:33<00:00,  2.36s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for pdf_fn in tqdm.tqdm(pdf_fns):\n", "    full_path = f\"{test_path}/{pdf_fn}\"\n", "    if is_scanned_pdf(full_path):\n", "        raise\n", "    else:\n", "        input_file_path = f\"{test_path}/{pdf_fn}\"\n", "        markdown_data = pdf_to_markdown(input_file_path)\n", "        out_fn = pdf_fn.split('.')[0]\n", "        with open(f'out_data/{out_fn}.md', 'w', encoding='utf-8') as f:\n", "            f.write(markdown_data)"]}, {"cell_type": "code", "execution_count": null, "id": "665536f1", "metadata": {"ExecuteTime": {"end_time": "2025-05-28T02:00:15.923968Z", "start_time": "2025-05-28T02:00:15.918408Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7ff278d0", "metadata": {"ExecuteTime": {"end_time": "2025-05-23T08:39:40.958305Z", "start_time": "2025-05-23T08:39:40.907785Z"}, "scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce4acfeb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
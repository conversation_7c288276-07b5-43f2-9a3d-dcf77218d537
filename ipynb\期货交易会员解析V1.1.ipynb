{"cells": [{"cell_type": "code", "execution_count": 1, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [], "source": ["from util_img_process import *"]}, {"cell_type": "code", "execution_count": 30, "id": "f27c8632", "metadata": {}, "outputs": [], "source": ["img_prompt_1 = \"\"\"\n", "    请从图片中提取以下完整字段信息，严格按照JSON格式输出：\n", "    1. 产品名称（一般指代资管计划的名称）\n", "    2. 资金账号\n", "    3. 交易所会员号（4位数字）也被称为会员编号：\n", "      - 上期所：也被称作上海期货交易所、上海交易所\n", "      - 大商所：也被称作大连商品交易所、大连交易所\n", "      - 郑商所：也被称作郑州商品交易所、郑州交易所\n", "      - 中金所：也被称作中国金融期货交易所、金融交易所\n", "      - 上能所：也被称作上海能源交易所、能源中心\n", "      - 广期所：也被称作广州期货交易所、广州交易所\n", "    4. 开始时间（格式：YYYY-MM-DD， 写明的开始日期或者函件落款日期）\n", "    5. 结束时间（格式：YYYY-MM-DD， 写明的结束日期或者函件落款日期）\n", "    6. 请勿遗漏任何一个交易所，如该交易所不存在或未提及则输出\"/\"\n", "\n", "    === 处理要求 ===\n", "    1. 完整性处理：\n", "      - 跨页数据自动拼接（如长文本的交易编码对应名称）\n", "      - 对每个交易所会员号分别识别\n", "      - 图片中不存在的数据字段输出\"None\"\n", "\n", "    2. 格式规范：\n", "      ▸ 使用标准JSON格式（无注释、无尾随逗号）\n", "      ▸ 字典键名严格使用中文（如\"郑州交易所\"非\"zhengzhou\"）\n", "\n", "    3. 错误防护：\n", "      - 排除页码/水印/无关表格干扰\n", "      - 账号类数字需完整提取（包含前置0）\n", "\n", "    4. 输出示例：\n", "    ```json\n", "    {\n", "      \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "      \"资金账号\": \"2120061\",\n", "      \"交易所会员号\": {\n", "        \"上期所\": \"0121\",\n", "        \"大商所\": \"/\",\n", "        \"郑商所\": \"0059\",\n", "        \"中金所\": \"0170\",\n", "        \"上能所\": \"8059\",\n", "        \"广期所\": \"0021\"\n", "      },\n", "      \"开始时间\": \"2025-01-01\",\n", "      \"结束时间\": \"2025-01-15\"\n", "    }\n", "    ```\n", "\n", "    请务必核对：\n", "    1. 是否遗漏6个交易所中的任何一个\n", "    2. 三个密码字段是否混淆\n", "    3. JSON最后键值对无逗号\n", "    \"\"\"\n", "\n", "img_prompt_2 = \"\"\"\n", "    请从图片中提取开户信息中的交易编码信息，严格按照JSON格式输出，特别要注意的是，每个交易所的交易编码一般为8位数字或\"/\"\n", "    特别要注意的是，我需要是交易编码是指8位数字，而不是会员、会员编号，同时请勿遗漏任何一个交易所，如该交易所不存在则输出\"/\"\n", "    交易所名称：\n", "      - 上期所：也被称作上海期货交易所、上海交易所\n", "      - 大商所：也被称作大连商品交易所、大连交易所\n", "      - 郑商所：也被称作郑州商品交易所、郑州交易所\n", "      - 中金所（投机）：也被称作中国金融期货交易所、金融交易所，投机是指投机交易账户\n", "      - 中金所（套保）：也被称作中国金融期货交易所、金融交易所，套保是指套期保值交易账户\n", "      - 上能所：也被称作上海能源交易所、能源中心\n", "      - 广期所：也被称作广州期货交易所、广州交易所\n", "\n", "    输出示例：\n", "    ```json\n", "    {\n", "      \"交易编码\": {\n", "          \"上期所\": \"81010373\",\n", "          \"大商所\": \"/\",\n", "          \"郑商所\": \"99871700\",\n", "          \"中金所（投机）\": \"00185013\",\n", "          \"中金所（套保）\": \"/\",\n", "          \"上能所\": \"81010376\",\n", "          \"广期所\": \"04471686\"\n", "      }\n", "    }\n", "    ```\n", "    \n", "\n", "    请务必核对：\n", "    1. 图片中可能存在多个交易编码，需要全部识别\n", "    2. JSON最后键值对无逗号\n", "    3. 交易编码如果存在则必须是8位数字，不存在则输出\"/\"\n", "    4. 如交易编码识别出的位数不等于8位，则输出\"/\"\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 40, "id": "aaee57bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["处理完成：结果已保存至 output.jpg\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/app/宁波银行POC/util_img_process.py\", line 264, in chat_bot_img\n", "    chat_completion = client.chat.completions.create(\n", "                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/root/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_utils/_utils.py\", line 287, in wrapper\n", "    return func(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/root/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/resources/chat/completions/completions.py\", line 925, in create\n", "    return self._post(\n", "           ^^^^^^^^^^^\n", "  File \"/root/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_base_client.py\", line 1239, in post\n", "    return cast(ResponseT, self.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n", "                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/root/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_base_client.py\", line 1034, in request\n", "    raise self._make_status_error_from_response(err.response) from None\n", "openai.NotFoundError: Error code: 404 - {'error': {'message': ' (request id: 2025070317194636050961345278039)', 'type': 'upstream_error', 'param': '404', 'code': 'bad_response_status_code'}}\n"]}, {"ename": "ValueError", "evalue": "与AI服务通信时发生错误: Error code: 404 - {'error': {'message': ' (request id: 2025070317194636050961345278039)', 'type': 'upstream_error', 'param': '404', 'code': 'bad_response_status_code'}}", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mNotFoundError\u001b[39m                             <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/app/宁波银行POC/util_img_process.py:264\u001b[39m, in \u001b[36mchat_bot_img\u001b[39m\u001b[34m(prompt, img_url, system_prompt, model, max_tokens, top_p, temperature, max_pixels, fixed_height)\u001b[39m\n\u001b[32m    263\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m264\u001b[39m     chat_completion = client.chat.completions.create(\n\u001b[32m    265\u001b[39m         messages=temp_his,\n\u001b[32m    266\u001b[39m         model=model,\n\u001b[32m    267\u001b[39m         top_p=top_p,\n\u001b[32m    268\u001b[39m         temperature=temperature,\n\u001b[32m    269\u001b[39m         max_tokens=max_tokens\n\u001b[32m    270\u001b[39m     )\n\u001b[32m    271\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m chat_completion.choices[\u001b[32m0\u001b[39m].message.content    \n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_utils/_utils.py:287\u001b[39m, in \u001b[36mrequired_args.<locals>.inner.<locals>.wrapper\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m    286\u001b[39m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(msg)\n\u001b[32m--> \u001b[39m\u001b[32m287\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m func(*args, **kwargs)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/resources/chat/completions/completions.py:925\u001b[39m, in \u001b[36mCompletions.create\u001b[39m\u001b[34m(self, messages, model, audio, frequency_penalty, function_call, functions, logit_bias, logprobs, max_completion_tokens, max_tokens, metadata, modalities, n, parallel_tool_calls, prediction, presence_penalty, reasoning_effort, response_format, seed, service_tier, stop, store, stream, stream_options, temperature, tool_choice, tools, top_logprobs, top_p, user, web_search_options, extra_headers, extra_query, extra_body, timeout)\u001b[39m\n\u001b[32m    924\u001b[39m validate_response_format(response_format)\n\u001b[32m--> \u001b[39m\u001b[32m925\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._post(\n\u001b[32m    926\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33m/chat/completions\u001b[39m\u001b[33m\"\u001b[39m,\n\u001b[32m    927\u001b[39m     body=maybe_transform(\n\u001b[32m    928\u001b[39m         {\n\u001b[32m    929\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmessages\u001b[39m\u001b[33m\"\u001b[39m: messages,\n\u001b[32m    930\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmodel\u001b[39m\u001b[33m\"\u001b[39m: model,\n\u001b[32m    931\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33maudio\u001b[39m\u001b[33m\"\u001b[39m: audio,\n\u001b[32m    932\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mfrequency_penalty\u001b[39m\u001b[33m\"\u001b[39m: frequency_penalty,\n\u001b[32m    933\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mfunction_call\u001b[39m\u001b[33m\"\u001b[39m: function_call,\n\u001b[32m    934\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mfunctions\u001b[39m\u001b[33m\"\u001b[39m: functions,\n\u001b[32m    935\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mlogit_bias\u001b[39m\u001b[33m\"\u001b[39m: logit_bias,\n\u001b[32m    936\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mlogprobs\u001b[39m\u001b[33m\"\u001b[39m: logprobs,\n\u001b[32m    937\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmax_completion_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_completion_tokens,\n\u001b[32m    938\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmax_tokens\u001b[39m\u001b[33m\"\u001b[39m: max_tokens,\n\u001b[32m    939\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmetadata\u001b[39m\u001b[33m\"\u001b[39m: metadata,\n\u001b[32m    940\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mmodalities\u001b[39m\u001b[33m\"\u001b[39m: modalities,\n\u001b[32m    941\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mn\u001b[39m\u001b[33m\"\u001b[39m: n,\n\u001b[32m    942\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mparallel_tool_calls\u001b[39m\u001b[33m\"\u001b[39m: parallel_tool_calls,\n\u001b[32m    943\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mprediction\u001b[39m\u001b[33m\"\u001b[39m: prediction,\n\u001b[32m    944\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mpresence_penalty\u001b[39m\u001b[33m\"\u001b[39m: presence_penalty,\n\u001b[32m    945\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mreasoning_effort\u001b[39m\u001b[33m\"\u001b[39m: reasoning_effort,\n\u001b[32m    946\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mresponse_format\u001b[39m\u001b[33m\"\u001b[39m: response_format,\n\u001b[32m    947\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mseed\u001b[39m\u001b[33m\"\u001b[39m: seed,\n\u001b[32m    948\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mservice_tier\u001b[39m\u001b[33m\"\u001b[39m: service_tier,\n\u001b[32m    949\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mstop\u001b[39m\u001b[33m\"\u001b[39m: stop,\n\u001b[32m    950\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mstore\u001b[39m\u001b[33m\"\u001b[39m: store,\n\u001b[32m    951\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mstream\u001b[39m\u001b[33m\"\u001b[39m: stream,\n\u001b[32m    952\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mstream_options\u001b[39m\u001b[33m\"\u001b[39m: stream_options,\n\u001b[32m    953\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtemperature\u001b[39m\u001b[33m\"\u001b[39m: temperature,\n\u001b[32m    954\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtool_choice\u001b[39m\u001b[33m\"\u001b[39m: tool_choice,\n\u001b[32m    955\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtools\u001b[39m\u001b[33m\"\u001b[39m: tools,\n\u001b[32m    956\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtop_logprobs\u001b[39m\u001b[33m\"\u001b[39m: top_logprobs,\n\u001b[32m    957\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mtop_p\u001b[39m\u001b[33m\"\u001b[39m: top_p,\n\u001b[32m    958\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33muser\u001b[39m\u001b[33m\"\u001b[39m: user,\n\u001b[32m    959\u001b[39m             \u001b[33m\"\u001b[39m\u001b[33mweb_search_options\u001b[39m\u001b[33m\"\u001b[39m: web_search_options,\n\u001b[32m    960\u001b[39m         },\n\u001b[32m    961\u001b[39m         completion_create_params.CompletionCreateParamsStreaming\n\u001b[32m    962\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m stream\n\u001b[32m    963\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m completion_create_params.CompletionCreateParamsNonStreaming,\n\u001b[32m    964\u001b[39m     ),\n\u001b[32m    965\u001b[39m     options=make_request_options(\n\u001b[32m    966\u001b[39m         extra_headers=extra_headers, extra_query=extra_query, extra_body=extra_body, timeout=timeout\n\u001b[32m    967\u001b[39m     ),\n\u001b[32m    968\u001b[39m     cast_to=ChatCompletion,\n\u001b[32m    969\u001b[39m     stream=stream \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m    970\u001b[39m     stream_cls=Stream[ChatCompletionChunk],\n\u001b[32m    971\u001b[39m )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_base_client.py:1239\u001b[39m, in \u001b[36mSyncAPIClient.post\u001b[39m\u001b[34m(self, path, cast_to, body, options, files, stream, stream_cls)\u001b[39m\n\u001b[32m   1236\u001b[39m opts = FinalRequestOptions.construct(\n\u001b[32m   1237\u001b[39m     method=\u001b[33m\"\u001b[39m\u001b[33mpost\u001b[39m\u001b[33m\"\u001b[39m, url=path, json_data=body, files=to_httpx_files(files), **options\n\u001b[32m   1238\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m1239\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m cast(ResponseT, \u001b[38;5;28mself\u001b[39m.request(cast_to, opts, stream=stream, stream_cls=stream_cls))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/anaconda3/envs/ocr/lib/python3.11/site-packages/openai/_base_client.py:1034\u001b[39m, in \u001b[36mSyncAPIClient.request\u001b[39m\u001b[34m(self, cast_to, options, stream, stream_cls)\u001b[39m\n\u001b[32m   1033\u001b[39m     log.debug(\u001b[33m\"\u001b[39m\u001b[33mRe-raising status error\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m-> \u001b[39m\u001b[32m1034\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;28mself\u001b[39m._make_status_error_from_response(err.response) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[32m   1036\u001b[39m \u001b[38;5;28;01mbreak\u001b[39;00m\n", "\u001b[31mNotFoundError\u001b[39m: Error code: 404 - {'error': {'message': ' (request id: 2025070317194636050961345278039)', 'type': 'upstream_error', 'param': '404', 'code': 'bad_response_status_code'}}", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[40]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m pdf_file_name.endswith(\u001b[33m'\u001b[39m\u001b[33m.pdf\u001b[39m\u001b[33m'\u001b[39m):\n\u001b[32m      4\u001b[39m     process_pdf(\n\u001b[32m      5\u001b[39m             pdf_path=pdf_file_name,     \u001b[38;5;66;03m# 输入PDF路径\u001b[39;00m\n\u001b[32m      6\u001b[39m             output_path=\u001b[33m\"\u001b[39m\u001b[33moutput.jpg\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;66;03m# 输出图片路径\u001b[39;00m\n\u001b[32m      7\u001b[39m             dpi=\u001b[32m200\u001b[39m,                  \u001b[38;5;66;03m# 控制转换质量（推荐150-300）\u001b[39;00m\n\u001b[32m      8\u001b[39m             border=\u001b[32m15\u001b[39m                 \u001b[38;5;66;03m# 内容边界保留距离（像素）\u001b[39;00m\n\u001b[32m      9\u001b[39m         )\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m     res1 = chat_bot_img(img_prompt_1, img_url=\u001b[33m'\u001b[39m\u001b[33moutput.jpg\u001b[39m\u001b[33m'\u001b[39m, temperature=\u001b[32m0.3\u001b[39m, model=model)\n\u001b[32m     11\u001b[39m     res2 = chat_bot_img(img_prompt_2, img_url=\u001b[33m'\u001b[39m\u001b[33moutput.jpg\u001b[39m\u001b[33m'\u001b[39m, temperature=\u001b[32m0.3\u001b[39m, model=model)\n\u001b[32m     12\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m/app/宁波银行POC/util_img_process.py:275\u001b[39m, in \u001b[36mchat_bot_img\u001b[39m\u001b[34m(prompt, img_url, system_prompt, model, max_tokens, top_p, temperature, max_pixels, fixed_height)\u001b[39m\n\u001b[32m    273\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m api_error:\n\u001b[32m    274\u001b[39m     traceback.print_exc()\n\u001b[32m--> \u001b[39m\u001b[32m275\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m与AI服务通信时发生错误: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mstr\u001b[39m(api_error)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[31mValueError\u001b[39m: 与AI服务通信时发生错误: Error code: 404 - {'error': {'message': ' (request id: 2025070317194636050961345278039)', 'type': 'upstream_error', 'param': '404', 'code': 'bad_response_status_code'}}"]}], "source": ["pdf_file_name = \"test_data/大模型样例/期货交易会员解析/均衡成长-期货户-中信期货.pdf_1745473949235.pdf\"\n", "model = \"qwen2.5vl:72b\"\n", "if pdf_file_name.endswith('.pdf'):\n", "    process_pdf(\n", "            pdf_path=pdf_file_name,     # 输入PDF路径\n", "            output_path=\"output.jpg\", # 输出图片路径\n", "            dpi=200,                  # 控制转换质量（推荐150-300）\n", "            border=15                 # 内容边界保留距离（像素）\n", "        )\n", "    res1 = chat_bot_img(img_prompt_1, img_url='output.jpg', temperature=0.3, model=model)\n", "    res2 = chat_bot_img(img_prompt_2, img_url='output.jpg', temperature=0.3, model=model)\n", "else:\n", "    res1 = chat_bot_img(img_prompt_1, img_url=pdf_file_name, temperature=0.3, model=model)\n", "    res2 = chat_bot_img(img_prompt_2, img_url=pdf_file_name, temperature=0.3, model=model)"]}, {"cell_type": "code", "execution_count": 39, "id": "d26d64b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"中欧均衡成长混合型证券投资基金\",\n", "  \"资金账号\": \"120520336\",\n", "  \"交易所会员号\": {\n", "    \"上期所\": \"0148\",\n", "    \"大商所\": \"0110\",\n", "    \"郑商所\": \"0055\",\n", "    \"中金所\": \"0018\",\n", "    \"上能所\": \"8148\",\n", "    \"广期所\": \"0008\"\n", "  },\n", "  \"开始时间\": \"2023-07-07\",\n", "  \"结束时间\": \"2023-07-07\"\n", "}\n", "```\n", "```json\n", "{\n", "  \"交易编码\": {\n", "      \"上期所\": \"0148\",\n", "      \"大商所\": \"0110\",\n", "      \"郑商所\": \"0055\",\n", "      \"中金所（投机）\": \"0018\",\n", "      \"中金所（套保）\": \"0018\",\n", "      \"上能所\": \"8148\",\n", "      \"广期所\": \"0008\"\n", "  }\n", "}\n", "```\n"]}], "source": ["print(res1)\n", "print(res2)\n"]}, {"cell_type": "code", "execution_count": 22, "id": "b34c59fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"合煦智远嘉选混合型证券投资基金\",\n", "  \"资金账号\": \"96661001\",\n", "  \"交易所会员号\": {\n", "    \"郑州交易所\": \"0206\",\n", "    \"大连交易所\": \"0101\",\n", "    \"上海交易所\": \"0249\",\n", "    \"金融交易所\": \"0316\",\n", "    \"能源中心\": \"8249\",\n", "    \"广州交易所\": \"0101\"\n", "  },\n", "  \"开始时间\": \"2024-07-03\",\n", "  \"结束时间\": \"2024-07-03\"\n", "}\n", "```\n"]}], "source": ["print(res1)"]}, {"cell_type": "code", "execution_count": 23, "id": "526d26d5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"交易编码\": {\n", "    \"上期所\": \"0249\",\n", "    \"大商所\": \"0101\",\n", "    \"郑商所\": \"0206\",\n", "    \"中金所（投机）\": \"0316\",\n", "    \"中金所（套保）\": \"None\",\n", "    \"上能所\": \"8249\",\n", "    \"广期所\": \"0101\"\n", "  }\n", "}\n", "```\n"]}], "source": ["print(res2)"]}, {"cell_type": "code", "execution_count": null, "id": "a4dd30e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "dc19d9b3", "metadata": {}, "source": ["测试下来发现：\"test_data/大模型样例/期货交易会员解析/均衡成长-期货户-中信期货.pdf_1745473949235.pdf\"\n", "这文件在测试中，始终无法正确捕获交易编码，其余字段表现正常"]}, {"cell_type": "markdown", "id": "be3f6955", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
import os
import requests
from PIL import Image

# 后端服务URL
BACKEND_URL = "http://**************:30000"


def trans_pdf_to_markdown(file_path, 
               server_url=BACKEND_URL,
               return_md=True,
               return_images=True,
               return_middle_json=True,
               return_model_output=False,
               return_content_list=False,
               start_page_id=0,
               end_page_id=99999,
               parse_method='auto',
               lang_list='ch',
               output_dir='./output',
               backend='pipeline',
               table_enable=True,
               formula_enable=True,
               convert_to_scanned=False) -> dict:
    """
    封装文件解析接口
    
    :param file_path: 要上传的文件路径
    :param server_url: 接口URL，默认为'http://**************:30000/file_parse'
    :param return_md: 是否返回markdown格式，默认为True
    :param return_images: 是否返回图片，默认为False
    :param return_middle_json: 是否返回中间JSON，默认为False
    :param return_model_output: 是否返回模型输出，默认为False
    :param return_content_list: 是否返回内容列表，默认为False
    :param start_page_id: 起始页码，默认为0
    :param end_page_id: 结束页码，默认为99999
    :param parse_method: 解析方法，默认为'auto'，可选值为'auto'、'ocr'、'text'
    :param lang_list: 语言列表，默认为'ch'
    :param output_dir: 输出目录，默认为'./output'
    :param backend: 后端类型，默认为'pipeline'，可选值为'pipeline'、'vlm-transformers'、'vlm-sglang-engine'、'vlm-sglang-client'
    :param table_enable: 是否启用表格解析，默认为True
    :param formula_enable: 是否启用公式解析，默认为True
    :param convert_to_scanned: 是否将PDF转换为扫描PDF，默认为False
    :return: 接口响应结果
    """
    if 'file_parse' not in server_url:
        server_url = f"{server_url}/file_parse"
    try:
        file_path, is_image = detect_and_convert_to_pdf(file_path)    # 判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
        with open(file_path, 'rb') as f:
            files = {
                'files': (file_path.split('/')[-1], f, 'application/pdf'),
                'return_md': (None, str(return_md).lower()),
                'return_images': (None, str(return_images).lower()),
                'return_middle_json': (None, str(return_middle_json).lower()),
                'return_model_output': (None, str(return_model_output).lower()),
                'return_content_list': (None, str(return_content_list).lower()),
                'start_page_id': (None, str(start_page_id)),
                'end_page_id': (None, str(end_page_id)),
                'parse_method': (None, parse_method),
                'lang_list': (None, lang_list),
                'output_dir': (None, output_dir),
                'server_url': (None, 'string'),
                'backend': (None, backend),
                'table_enable': (None, str(table_enable).lower()),
                'formula_enable': (None, str(formula_enable).lower()),
                'convert_to_scanned': (None, str(convert_to_scanned).lower())
            }
            
            headers = {
                'accept': 'application/json'
            }
            
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
            
            return response.json()
        
        if is_image:
            os.remove(file_path) # 删除临时生成的PDF文件
    except IOError as e:
        print(f"文件读取错误: {e}")
        return {
            'success': False,
            'error': f'文件读取失败: {str(e)}',
            'error_type': 'file_read_error'
        }
    except requests.exceptions.ConnectionError as e:
        print(f"文件处理服务连接失败: {e}")
        return {
            'success': False,
            'error': '文件处理服务暂时不可用，建议稍后重试',
            'error_type': 'service_unavailable',
            'can_retry': True
        }
    except requests.exceptions.Timeout as e:
        print(f"文件处理服务超时: {e}")
        return {
            'success': False,
            'error': '文件处理超时，建议稍后重试或检查文件大小',
            'error_type': 'service_timeout',
            'can_retry': True
        }
    except requests.exceptions.RequestException as e:
        print(f"文件处理服务请求错误: {e}")
        return {
            'success': False,
            'error': f'文件处理请求失败: {str(e)}',
            'error_type': 'request_error',
            'can_retry': True
        }


def pdf_to_markdown(pdf_path, markdown_output_path):
    """
    将PDF文件转换为Markdown文件（通过HTTP调用后端服务）
    
    Args:
        pdf_path: PDF文件路径
        markdown_output_path: 输出的Markdown文件路径
    
    Returns:
        str: 生成的markdown内容
    """
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(markdown_output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # 准备文件上传
        with open(pdf_path, 'rb') as f:
            files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
            
            # 准备请求参数
            data = {
                'parse_method': 'auto',  # 使用自动解析方法
                'is_json_md_dump': False,  # 不需要后端保存文件
                'return_layout': False,
                'return_info': False,
                'return_content_list': False,
                'return_images': False
            }
            
            # 发送POST请求到后端
            response = requests.post(
                f"{BACKEND_URL}/file_parse",
                files=files,
                data=data,
                timeout=300  # 5分钟超时
            )
        
        # 检查响应状态
        if response.status_code != 200:
            raise Exception(f"后端服务返回错误: {response.status_code}, {response.text}")
        
        # 解析响应
        result = response.json()
        
        if 'error' in result:
            raise Exception(f"后端处理错误: {result['error']}")
        
        # 获取markdown内容
        md_content = result.get('md_content', '')
        
        if not md_content:
            raise Exception("后端未返回markdown内容")
        
        # 保存markdown文件
        with open(markdown_output_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        return md_content
        
    except requests.exceptions.ConnectionError as e:
        raise Exception(f"文件处理服务连接失败，请检查服务状态: {str(e)}")
    except requests.exceptions.Timeout as e:
        raise Exception(f"文件处理超时，请稍后重试或检查文件大小: {str(e)}")
    except requests.exceptions.RequestException as e:
        raise Exception(f"文件处理服务请求错误: {str(e)}")
    except Exception as e:
        raise Exception(f"PDF转换失败: {str(e)}")


def convert_images_to_pdf(input_path, output_path=None):
    """
    将图片文件转换为PDF文档
    
    参数:
    - input_path: 输入路径，可以是单个图片文件路径或包含图片的文件夹路径
    - output_path: 可选的输出PDF文件路径。如果未提供，则使用输入文件/文件夹的名称
    
    支持格式:
    - 图片: JPEG, PNG, BMP, GIF, TIFF, WebP等Pillow支持的格式
    - 多页PDF: 支持多张图片合并到同一个PDF文件中
    
    返回:
    - 成功时返回PDF文件路径，失败时返回None
    """
    try:
        # 确定输出路径
        if output_path is None:
            if os.path.isfile(input_path):
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = base_name + ".pdf"
            elif os.path.isdir(input_path):
                folder_name = os.path.basename(input_path.rstrip(os.sep))
                output_path = folder_name + ".pdf"
        
        # 处理单个文件的情况
        if os.path.isfile(input_path):
            with Image.open(input_path) as img:
                if img.mode == 'RGBA':
                    img = img.convert('RGB')
                img.save(output_path, "PDF", resolution=100.0)
            return output_path
        
        # 处理文件夹的情况
        elif os.path.isdir(input_path):
            images = []
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
            
            # 收集所有支持的图片文件
            for filename in sorted(os.listdir(input_path)):
                if any(filename.lower().endswith(ext) for ext in valid_extensions):
                    filepath = os.path.join(input_path, filename)
                    try:
                        with Image.open(filepath) as img:
                            if img.mode == 'RGBA':
                                img = img.convert('RGB')
                            images.append(img.copy())
                    except Exception as e:
                        print(f"警告: 无法处理图片 {filename}: {e}")
            
            # 如果没有找到图片，返回None
            if not images:
                print("错误: 没有找到支持的图片文件")
                return None
            
            # 保存为多页PDF
            images[0].save(
                output_path,
                "PDF",
                resolution=100.0,
                save_all=True,
                append_images=images[1:]
            )
            return output_path
        
        else:
            print(f"错误: 路径 '{input_path}' 不存在或无法访问")
            return None
    
    except Exception as e:
        print(f"图片转PDF失败: {e}")
        return None


def detect_and_convert_to_pdf(input_path):
    """
    判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
    支持单张图片、图片文件夹、PDF文件。
    返回：元组，第一个元素是PDF文件路径或None，第二个元素是是否是图片，是图片则返回True，否则返回False
    """
    # 支持的图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
    if os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext == '.pdf':
            return input_path, False  # 已经是PDF
        elif ext in image_extensions:
            # 单张图片转PDF
            return convert_images_to_pdf(input_path), True
        else:
            print(f"不支持的文件类型: {input_path}")
            return None, False
    elif os.path.isdir(input_path):
        # 文件夹，假设里面是图片
        return convert_images_to_pdf(input_path), True
    else:
        print(f"路径不存在: {input_path}")
        return None, False

if __name__ == "__main__":
    # 示例使用
    pdf_path = "abc.pdf"  # 替换为实际PDF路径
    markdown_path = "output/abc.md"  # 输出的Markdown路径
    
    try:
        md_content = pdf_to_markdown(pdf_path, markdown_path)
        print(f"Markdown文件已生成: {markdown_path}")
        print(f"内容长度: {len(md_content)} 字符")
    except Exception as e:
        print(f"转换失败: {e}")
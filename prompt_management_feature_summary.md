# 提示词管理功能说明文档

## 1. 功能概述

提示词管理功能是本系统的核心功能之一，用于管理和维护不同业务场景下的AI提示词（Prompt）。系统支持提示词的增删改查、版本管理、激活切换等功能，为文档分析提供基础支撑。


## 2. 前端页面

### 2.1 提示词配置页面
文件路径：[templates/prompt_config.html](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/templates/prompt_config.html)

主要功能：
- 显示所有提示词列表
- 按主要功能筛选提示词
- 新增、编辑、删除提示词
- 初始化默认提示词

### 2.2 提示词版本管理页面
文件路径：[templates/prompt_version_management.html](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/templates/prompt_version_management.html)

主要功能：
- 显示提示词版本列表
- 切换当前激活版本
- 创建新版本
- 删除指定版本

## 3. 后端实现

### 3.1 数据模型
文件路径：[models.py](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py)

模型类：[PromptConfig](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L332-L398)

主要字段：
- [id](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L333-L333)：主键ID
- [name](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L334-L334)：提示词名称
- [type](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L335-L335)：提示词类型（如system_prompt、img_prompt_1等）
- [main_type](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L336-L336)：主要功能分类（如future、financial、broker_interest等）
- [prompt](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L337-L337)：提示词内容
- [version](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L342-L342)：版本号
- [is_active](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L343-L343)：是否为当前激活版本
- [parent_version_id](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L345-L345)：父版本ID
- [usage_count](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L349-L349)：使用次数
- [created_by](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L353-L353)：创建者ID
- [updated_by](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L354-L354)：更新者ID

### 3.2 路由接口
文件路径：[app.py](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py)

#### 页面路由
- `GET /prompt_config`：访问提示词配置页面
- `GET /prompt_version_management`：访问提示词版本管理页面

#### API接口
- `GET /api/prompts`：获取提示词列表
- `POST /api/prompts`：创建提示词
- `PUT /api/prompts/<int:pid>`：更新提示词
- `DELETE /api/prompts/<int:pid>`：删除提示词
- `POST /api/prompts/init_default`：初始化默认提示词
- `GET /api/prompts/<analysis_type>`：获取特定分析类型的提示词
- `POST /api/prompts/save`：保存提示词配置
- `GET /api/prompts/<analysis_type>/versions`：获取提示词版本列表
- `POST /api/prompts/<analysis_type>/switch-version`：切换提示词版本
- `POST /api/prompts/<analysis_type>/create-version`：创建新提示词版本
- `DELETE /api/prompts/<analysis_type>/versions/<int:version_id>`：删除提示词版本

### 3.3 默认提示词
文件路径：[app.py](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py)

变量：[DEFAULT_PROMPTS](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py#L3402-L3696)

包含各业务场景的默认提示词：
- 期货账户分析
- 券商账户计息变更
- 期货交易会员
- 宁银费用变更
- 理财产品说明书

## 4. 功能流程

### 4.1 提示词基本操作流程

#### 查看提示词列表
1. 用户访问 `/prompt_config` 页面
2. 前端调用 `GET /api/prompts` 接口获取提示词列表
3. 后端查询数据库中的提示词记录
4. 如果数据库中无记录，则返回默认提示词 [DEFAULT_PROMPTS](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py#L3402-L3696)
5. 前端渲染提示词列表

#### 新增提示词
1. 用户点击"新增提示词"按钮
2. 用户填写提示词信息并提交
3. 前端调用 `POST /api/prompts` 接口
4. 后端验证参数并保存到数据库
5. 返回保存结果

#### 编辑提示词
1. 用户点击某条提示词的"编辑"按钮
2. 用户修改提示词信息并提交
3. 前端调用 `PUT /api/prompts/<int:pid>` 接口
4. 后端更新数据库中的记录
5. 返回更新结果

#### 删除提示词
1. 用户点击某条提示词的"删除"按钮
2. 用户确认删除操作
3. 前端调用 `DELETE /api/prompts/<int:pid>` 接口
4. 后端从数据库中删除记录
5. 返回删除结果

#### 初始化默认提示词
1. 用户点击"初始化默认提示词"按钮
2. 前端调用 `POST /api/prompts/init_default` 接口
3. 后端将 [DEFAULT_PROMPTS](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py#L3402-L3696) 中的提示词保存到数据库（避免重复）
4. 返回初始化结果

### 4.2 提示词版本管理流程

#### 查看版本列表
1. 用户访问 `/prompt_version_management` 页面
2. 用户选择要查看的分析类型
3. 前端调用 `GET /api/prompts/<analysis_type>/versions` 接口
4. 后端查询该分析类型的所有提示词版本
5. 前端渲染版本列表，标识当前激活版本

#### 切换激活版本
1. 用户在版本列表中选择要激活的版本
2. 用户点击"设为当前版本"按钮
3. 前端调用 `POST /api/prompts/<analysis_type>/switch-version` 接口
4. 后端将该版本设为激活状态，其他版本设为非激活状态
5. 返回切换结果

#### 创建新版本
1. 用户点击"创建新版本"按钮
2. 用户填写版本信息（名称、类型、内容、版本号等）
3. 用户可选择是否设为激活版本
4. 前端调用 `POST /api/prompts/<analysis_type>/create-version` 接口
5. 后端创建新版本记录，处理激活状态
6. 返回创建结果

#### 删除版本
1. 用户在版本列表中选择要删除的版本
2. 用户点击"删除"按钮
3. 前端调用 `DELETE /api/prompts/<analysis_type>/versions/<int:version_id>` 接口
4. 后端验证是否可删除（非激活版本且无子版本依赖）
5. 从数据库中删除记录
6. 返回删除结果

## 5. 权限控制

提示词管理功能需要用户登录后才能使用，通过 `@login_required` 装饰器进行权限控制。

## 6. 技术细节

### 6.1 版本管理机制
- 每个分析类型可以有多个版本的提示词
- 通过 [is_active](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L343-L343) 字段标识当前激活版本
- 通过 [parent_version_id](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L345-L345) 字段维护版本间的父子关系
- 支持版本使用统计（[usage_count](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L349-L349)、[last_used_at](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/models.py#L350-L350)）

### 6.2 默认提示词机制
- 系统内置 [DEFAULT_PROMPTS](file:///d:/project/nbbanktest1.8/nb_bank_ai_poc/app.py#L3402-L3696) 作为默认提示词
- 当数据库中无对应记录时，使用默认提示词
- 可通过接口将默认提示词初始化到数据库

### 6.3 提示词类型
- `system_prompt`：系统提示词，通常是主要的分析指令
- `img_prompt_1`、`img_prompt_2`：针对图片内容的提取指令

# 提示词管理前端代码提取文档

## 1. 独立提示词配置页面 (prompt_config.html)

### 1.1 HTML结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title data-i11n="prompt_config.title">提示词管理</title>
    <style>
      body {
        background: linear-gradient(120deg, #f8fafc 0%, #ffffff 100%);
        color: #1f2937;
      }
      .container {
        background: #fff;
        color: #1f2937;
        border: 1px solid #e5e7eb;
        box-shadow: 0 4px 24px rgba(30,58,138,0.06);
      }
      h1 {
        color: #2563eb;
      }
      table {
        background: #fff;
        color: #1f2937;
      }
      th {
        background: #1e3a8a;
        color: #fff;
      }
      tr:nth-child(even) {
        background: #f9fafb;
      }
      tr:nth-child(odd) {
        background: #fff;
      }
      tr:hover {
        background: #eff6ff;
      }
      .btn {
        background: #2563eb;
        color: #fff;
        border: 1px solid #2563eb;
      }
      .btn:hover {
        background: #1d4ed8;
      }
      .btn-secondary {
        background: #374151;
        color: #fff;
        border: 1px solid #374151;
      }
      .btn-secondary:hover {
        background: #2563eb;
        color: #fff;
      }
      .btn-danger {
        background: #dc2626;
        color: #fff;
        border: 1px solid #dc2626;
      }
      .btn-danger:hover {
        background: #b91c1c;
      }
      .form-row label {
        color: #2563eb;
      }
    </style>
</head>
<body>
    <div class="container">
        <h1 data-i18n="prompt_config.header">提示词管理</h1>
        <div style="margin-bottom:18px;">
            <label style="color:#FFD700;font-weight:600;" data-i18n="prompt_config.filter_by_function">按主要功能筛选：</label>
            <select id="mainTypeFilter" onchange="filterPromptsByMainType()" style="padding:6px 12px;border-radius:4px;background:#222;color:#FFD700;">
                <option value="" data-i18n="prompt_config.all_functions">全部功能</option>
                <option value="future" data-i18n="prompt_config.future_account">开户文件解析</option>
                <option value="financial">理财产品说明书</option>
                <option value="broker_interest">券商账户计息变更</option>
                <option value="futures_member">非标交易确认单解析</option>
                <option value="ningyin_fee">宁银理财费用变更</option>
                <option value="product_manual">账户开户场景</option>
            </select>
            <button class="btn" onclick="showAddModal()" data-i18n="prompt_config.add_prompt">新增提示词</button>
            <button class="btn btn-secondary" onclick="initDefaultPrompts()" data-i18n="prompt_config.init_default">初始化默认提示词</button>
        </div>
        <table>
            <thead>
                <tr>
                    <th data-i18n="prompt_config.table.name">名称</th>
                    <th data-i18n="prompt_config.table.main_function">主要功能</th>
                    <th data-i18n="prompt_config.table.type">类型</th>
                    <th data-i18n="prompt_config.table.content">内容</th>
                    <th data-i18n="prompt_config.table.actions">操作</th>
                </tr>
            </thead>
            <tbody id="promptTableBody">
                <!-- 动态生成 -->
            </tbody>
        </table>
    </div>
    <!-- 编辑/新增弹窗 -->
    <div class="modal" id="promptModal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h2 id="modalTitle">新增提示词</h2>
            <form id="promptForm">
                <input type="hidden" id="promptId">
                <div class="form-row">
                    <label for="promptName">名称:</label>
                    <input type="text" id="promptName" required>
                </div>
                <div class="form-row">
                    <label for="promptMainType">主要功能:</label>
                    <select id="promptMainType" required>
                        <option value="future">开户文件解析</option>
                        <option value="financial">理财产品说明书</option>
                        <option value="broker_interest">券商账户计息变更</option>
                        <option value="futures_member">非标交易确认单解析</option>
                        <option value="ningyin_fee">宁银理财费用变更</option>
                        <option value="product_manual">账户开户场景</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="promptType">类型:</label>
                    <select id="promptType" required>
                        <option value="system_prompt">系统提示词</option>
                        <option value="img_prompt_1">图片提示词1</option>
                        <option value="img_prompt_2">图片提示词2</option>
                    </select>
                </div>
                <div class="form-row">
                    <label for="promptContent">内容:</label>
                    <textarea id="promptContent" rows="15" required></textarea>
                </div>
                <div class="form-row">
                    <button type="submit" class="btn">保存</button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 页面加载完成后获取提示词列表
        document.addEventListener('DOMContentLoaded', function() {
            loadPrompts();
        });
        
        // 获取提示词列表
        function loadPrompts() {
            fetch('/api/prompts')
                .then(response => response.json())
                .then(data => {
                    renderPrompts(data);
                })
                .catch(error => {
                    console.error('获取提示词失败:', error);
                });
        }
        
        // 渲染提示词列表
        function renderPrompts(prompts) {
            const tbody = document.getElementById('promptTableBody');
            tbody.innerHTML = '';
            
            prompts.forEach(prompt => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${prompt.name}</td>
                    <td>${getMainTypeText(prompt.main_type)}</td>
                    <td>${prompt.type}</td>
                    <td style="max-width: 400px; word-wrap: break-word;">${prompt.prompt.substring(0, 100)}${prompt.prompt.length > 100 ? '...' : ''}</td>
                    <td>
                        <button class="btn btn-secondary" onclick="editPrompt(${prompt.id})">编辑</button>
                        <button class="btn btn-danger" onclick="deletePrompt(${prompt.id})">删除</button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }
        
        // 获取主要功能文本
        function getMainTypeText(mainType) {
            const mainTypeMap = {
                'future': '开户文件解析',
                'financial': '理财产品说明书',
                'broker_interest': '券商账户计息变更',
                'futures_member': '非标交易确认单解析',
                'ningyin_fee': '宁银理财费用变更',
                'product_manual': '账户开户场景'
            };
            return mainTypeMap[mainType] || mainType;
        }
        
        // 按主要功能筛选提示词
        function filterPromptsByMainType() {
            const mainType = document.getElementById('mainTypeFilter').value;
            const url = mainType ? `/api/prompts?main_type=${mainType}` : '/api/prompts';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    renderPrompts(data);
                })
                .catch(error => {
                    console.error('筛选提示词失败:', error);
                });
        }
        
        // 显示新增弹窗
        function showAddModal() {
            document.getElementById('modalTitle').textContent = '新增提示词';
            document.getElementById('promptForm').reset();
            document.getElementById('promptId').value = '';
            document.getElementById('promptModal').style.display = 'block';
        }
        
        // 编辑提示词
        function editPrompt(id) {
            fetch(`/api/prompts/${id}`)
                .then(response => response.json())
                .then(prompt => {
                    document.getElementById('modalTitle').textContent = '编辑提示词';
                    document.getElementById('promptId').value = prompt.id;
                    document.getElementById('promptName').value = prompt.name;
                    document.getElementById('promptMainType').value = prompt.main_type;
                    document.getElementById('promptType').value = prompt.type;
                    document.getElementById('promptContent').value = prompt.prompt;
                    document.getElementById('promptModal').style.display = 'block';
                })
                .catch(error => {
                    console.error('获取提示词详情失败:', error);
                });
        }
        
        // 删除提示词
        function deletePrompt(id) {
            if (confirm('确定要删除这个提示词吗？')) {
                fetch(`/api/prompts/${id}`, {
                    method: 'DELETE'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadPrompts();
                    } else {
                        alert('删除失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('删除提示词失败:', error);
                });
            }
        }
        
        // 关闭弹窗
        function closeModal() {
            document.getElementById('promptModal').style.display = 'none';
        }
        
        // 表单提交处理
        document.getElementById('promptForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const id = document.getElementById('promptId').value;
            const name = document.getElementById('promptName').value;
            const mainType = document.getElementById('promptMainType').value;
            const type = document.getElementById('promptType').value;
            const prompt = document.getElementById('promptContent').value;
            
            const data = {
                name: name,
                main_type: mainType,
                type: type,
                prompt: prompt
            };
            
            const method = id ? 'PUT' : 'POST';
            const url = id ? `/api/prompts/${id}` : '/api/prompts';
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeModal();
                    loadPrompts();
                } else {
                    alert('保存失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('保存提示词失败:', error);
            });
        });
        
        // 初始化默认提示词
        function initDefaultPrompts() {
            if (confirm('确定要初始化默认提示词吗？这将会把默认提示词添加到数据库中。')) {
                fetch('/api/prompts/init_default', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`初始化成功，新增了 ${data.inserted} 个默认提示词。`);
                        loadPrompts();
                    } else {
                        alert('初始化失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('初始化默认提示词失败:', error);
                });
            }
        }
        
        // 点击弹窗外关闭弹窗
        window.onclick = function(event) {
            const modal = document.getElementById('promptModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
```

## 2. 独立提示词版本管理页面 (prompt_version_management.html)

### 2.1 HTML结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词版本管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        
        .content {
            padding: 24px;
        }
        
        .analysis-type-selector {
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .analysis-type-selector label {
            font-weight: 600;
            color: #374151;
        }
        
        .analysis-type-selector select {
            padding: 8px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            min-width: 200px;
        }
        
        .version-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
        }
        
        .current-version {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .current-version-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .current-version-number {
            font-weight: 700;
            color: #2563eb;
            font-size: 1.2em;
        }
        
        .current-version-stats {
            font-size: 0.9em;
            color: #6b7280;
        }
        
        .action-buttons {
            display: flex;
            gap: 12px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #2563eb;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37,99,235,0.3);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(107,114,128,0.3);
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(16,185,129,0.3);
        }
        
        .versions-list {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
        }
        
        .versions-header {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
            background: #f3f4f6;
            padding: 16px;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .version-item {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr 1fr 1fr;
            padding: 16px;
            border-bottom: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .version-item:hover {
            background: #f9fafb;
        }
        
        .version-item.current {
            background: #eff6ff;
            border-left: 4px solid #2563eb;
        }
        
        .version-number {
            font-weight: 700;
            color: #2563eb;
        }
        
        .version-description {
            color: #4b5563;
            font-size: 0.9em;
        }
        
        .version-stats {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 0.9em;
            color: #6b7280;
        }
        
        .version-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }
        
        .badge.current {
            background: #2563eb;
            color: white;
        }
        
        .badge.parent {
            background: #fbbf24;
            color: #92400e;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 24px;
            position: relative;
        }
        
        .close {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            cursor: pointer;
            color: #9ca3af;
        }
        
        .close:hover {
            color: #6b7280;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-family: 'Microsoft YaHei', sans-serif;
            font-size: 14px;
        }
        
        .form-group textarea {
            min-height: 200px;
            resize: vertical;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37,99,235,0.1);
        }
        
        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        
        .loading-spinner {
            border: 4px solid #e5e7eb;
            border-top: 4px solid #2563eb;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .versions-header,
            .version-item {
                grid-template-columns: 1fr 2fr 1fr;
            }
            
            .version-stats,
            .version-actions {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 提示词版本管理</h1>
            <a href="/prompt_config" class="back-btn">
                ← 返回提示词管理
            </a>
        </div>
        
        <div class="content">
            <div class="analysis-type-selector">
                <label for="analysisType">选择分析类型:</label>
                <select id="analysisType" onchange="loadVersions()">
                    <option value="future">开户文件解析</option>
                    <option value="financial">理财产品说明书</option>
                    <option value="broker_interest">券商账户计息变更</option>
                    <option value="futures_member">非标交易确认单解析</option>
                    <option value="ningyin_fee">宁银理财费用变更</option>
                    <option value="product_manual">账户开户场景</option>
                </select>
            </div>
            
            <div class="version-controls">
                <div class="current-version">
                    <div class="current-version-info">
                        <div class="current-version-number" id="currentVersionNumber">-</div>
                        <div class="current-version-stats" id="currentVersionStats">当前未激活任何版本</div>
                    </div>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-success" onclick="showCreateVersionModal()">
                        + 创建新版本
                    </button>
                    <button class="btn btn-secondary" onclick="loadVersions()">
                        🔄 刷新
                    </button>
                </div>
            </div>
            
            <div class="versions-list">
                <div class="versions-header">
                    <div>版本号</div>
                    <div>描述</div>
                    <div>统计信息</div>
                    <div>创建时间</div>
                    <div>操作</div>
                </div>
                <div id="versionsList">
                    <div class="loading">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 创建版本弹窗 -->
    <div class="modal" id="createVersionModal">
        <div class="modal-content">
            <span class="close" onclick="closeCreateVersionModal()">&times;</span>
            <h2>🚀 创建新版本</h2>
            <form id="createVersionForm">
                <div class="form-group">
                    <label for="versionNumber">版本号:</label>
                    <input type="text" id="versionNumber" required placeholder="例如: 1.0, 2.1, v1.0...">
                </div>
                <div class="form-group">
                    <label for="versionName">版本名称:</label>
                    <input type="text" id="versionName" required placeholder="例如: 优化开户信息提取规则">
                </div>
                <div class="form-group">
                    <label for="versionType">提示词类型:</label>
                    <select id="versionType" required>
                        <option value="system_prompt">系统提示词</option>
                        <option value="img_prompt_1">图片提示词1</option>
                        <option value="img_prompt_2">图片提示词2</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="versionPrompt">提示词内容:</label>
                    <textarea id="versionPrompt" required placeholder="请输入提示词内容..."></textarea>
                </div>
                <div class="form-group">
                    <label for="versionDescription">版本描述:</label>
                    <textarea id="versionDescription" placeholder="描述此版本的改进内容和变更点..."></textarea>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="setAsActive"> 设为当前激活版本
                    </label>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeCreateVersionModal()">取消</button>
                    <button type="submit" class="btn btn-primary">创建版本</button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadVersions();
        });
        
        // 加载版本列表
        function loadVersions() {
            const analysisType = document.getElementById('analysisType').value;
            const versionsList = document.getElementById('versionsList');
            
            versionsList.innerHTML = `
                <div class="loading">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
            `;
            
            fetch(`/api/prompts/${analysisType}/versions`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderVersions(data.data);
                    } else {
                        versionsList.innerHTML = `<div class="loading"><p>加载失败: ${data.message}</p></div>`;
                    }
                })
                .catch(error => {
                    versionsList.innerHTML = `<div class="loading"><p>加载失败: ${error.message}</p></div>`;
                });
        }
        
        // 渲染版本列表
        function renderVersions(data) {
            const versionsList = document.getElementById('versionsList');
            const currentVersionNumber = document.getElementById('currentVersionNumber');
            const currentVersionStats = document.getElementById('currentVersionStats');
            
            // 更新当前版本信息
            if (data.current_version) {
                currentVersionNumber.textContent = data.current_version;
                currentVersionStats.textContent = `共 ${data.total_count} 个版本`;
            } else {
                currentVersionNumber.textContent = '-';
                currentVersionStats.textContent = '当前未激活任何版本';
            }
            
            // 渲染版本列表
            if (data.versions && data.versions.length > 0) {
                versionsList.innerHTML = data.versions.map(version => `
                    <div class="version-item ${version.is_current ? 'current' : ''}">
                        <div>
                            <div class="version-number">${version.version}</div>
                            ${version.is_current ? '<span class="badge current">当前版本</span>' : ''}
                            ${version.parent_version_id ? '<span class="badge parent">衍生版本</span>' : ''}
                        </div>
                        <div>
                            <div>${version.name}</div>
                            <div class="version-description">${version.version_description || '无描述'}</div>
                        </div>
                        <div class="version-stats">
                            <div>使用次数: ${version.usage_count || 0}</div>
                            <div>最后使用: ${version.last_used_at ? new Date(version.last_used_at).toLocaleString() : '从未使用'}</div>
                        </div>
                        <div>
                            ${version.created_at ? new Date(version.created_at).toLocaleDateString() : '未知'}
                        </div>
                        <div class="version-actions">
                            ${!version.is_current ? `
                                <button class="btn btn-primary" onclick="switchVersion('${version.main_type}', ${version.id})" title="设为当前版本">
                                    设为当前
                                </button>
                                <button class="btn btn-secondary" onclick="deleteVersion('${version.main_type}', ${version.id})" title="删除版本">
                                    删除
                                </button>
                            ` : `
                                <button class="btn btn-secondary" disabled title="当前版本无法删除">
                                    当前版本
                                </button>
                            `}
                        </div>
                    </div>
                `).join('');
            } else {
                versionsList.innerHTML = `
                    <div class="loading">
                        <p>暂无版本数据</p>
                    </div>
                `;
            }
        }
        
        // 切换版本
        function switchVersion(analysisType, versionId) {
            if (!confirm('确定要切换到此版本吗？')) {
                return;
            }
            
            fetch(`/api/prompts/${analysisType}/switch-version`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    version_id: versionId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`已切换到版本 ${data.data.current_version}`);
                    loadVersions();
                } else {
                    alert('切换失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('切换失败: ' + error.message);
            });
        }
        
        // 删除版本
        function deleteVersion(analysisType, versionId) {
            if (!confirm('确定要删除此版本吗？此操作不可恢复。')) {
                return;
            }
            
            fetch(`/api/prompts/${analysisType}/versions/${versionId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('删除成功');
                    loadVersions();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('删除失败: ' + error.message);
            });
        }
        
        // 显示创建版本弹窗
        function showCreateVersionModal() {
            // 清空表单
            document.getElementById('createVersionForm').reset();
            document.getElementById('createVersionModal').style.display = 'flex';
        }
        
        // 关闭创建版本弹窗
        function closeCreateVersionModal() {
            document.getElementById('createVersionModal').style.display = 'none';
        }
        
        // 创建版本表单提交
        document.getElementById('createVersionForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const analysisType = document.getElementById('analysisType').value;
            const versionData = {
                version: document.getElementById('versionNumber').value,
                name: document.getElementById('versionName').value,
                type: document.getElementById('versionType').value,
                prompt: document.getElementById('versionPrompt').value,
                version_description: document.getElementById('versionDescription').value,
                set_as_active: document.getElementById('setAsActive').checked
            };
            
            fetch(`/api/prompts/${analysisType}/create-version`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(versionData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('版本创建成功');
                    closeCreateVersionModal();
                    loadVersions();
                } else {
                    alert('创建失败: ' + data.message);
                }
            })
            .catch(error => {
                alert('创建失败: ' + error.message);
            });
        });
        
        // 点击弹窗外关闭弹窗
        window.onclick = function(event) {
            const createModal = document.getElementById('createVersionModal');
            if (event.target == createModal) {
                closeCreateVersionModal();
            }
        }
    </script>
</body>
</html>
```

## 3. 首页中的提示词管理面板 (index.html)

### 3.1 HTML结构

```html
<!-- 提示词管理面板 -->
<div id="prompt-config-panel" class="content-panel" style="display: none;">
  <div class="card">
    <div class="card-title">提示词管理</div>
    <div class="prompt-config-content">
      <!-- 功能类型和版本选择器 -->
      <div class="prompt-selectors" style="display: flex; gap: 24px; margin-bottom: 24px; align-items: end;">
        <div class="prompt-type-selector">
          <label style="font-weight: 600; margin-bottom: 8px; display: block;">选择功能类型：</label>
          <select id="promptTypeSelect" style="padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px; min-width: 200px;" onchange="loadPromptConfig()">
            <option value="future">开户文件解析</option>
            <option value="financial">理财产品说明书</option>
            <option value="broker_interest">券商账户计息变更</option>
            <option value="futures_member">非标交易确认单解析</option>
            <option value="ningyin_fee">宁银理财费用变更</option>
            <option value="product_manual">账户开户场景</option>
          </select>
        </div>

        <div class="version-selector">
          <label style="font-weight: 600; margin-bottom: 8px; display: block;">选择版本：</label>
          <select id="versionSelect" style="padding: 8px 12px; border: 1px solid #e5e7eb; border-radius: 6px; min-width: 150px;" onchange="loadVersionContent()">
            <option value="">请先选择功能类型</option>
          </select>
        </div>

        <div class="version-actions" style="display: flex; gap: 8px;">
          <button class="btn btn-success btn-small" onclick="showCreateVersionModal()" style="background: #10b981; padding: 8px 16px; font-size: 14px;">
            <span>+</span> 新建版本
          </button>
          <button class="btn btn-secondary btn-small" onclick="showVersionHistory()" style="padding: 8px 16px; font-size: 14px;">
            <span>📋</span> 版本历史
          </button>
        </div>
      </div>

      <!-- 当前版本信息 -->
      <div id="currentVersionInfo" class="version-info" style="display: none; margin-bottom: 20px; padding: 12px; background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%); border-radius: 8px; border-left: 4px solid #2196f3;">
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <div>
            <span style="font-weight: 600; color: #1976d2;">当前使用版本：</span>
            <span id="currentVersionNumber" style="font-weight: 700; color: #1976d2; font-size: 1.1em;"></span>
            <span id="currentVersionName" style="margin-left: 12px; color: #666;"></span>
          </div>
          <div style="font-size: 14px; color: #666;">
            <span>使用次数：</span><span id="currentVersionUsage">0</span>
            <span style="margin-left: 12px;">最后使用：</span><span id="currentVersionLastUsed">从未使用</span>
          </div>
        </div>
        <div id="currentVersionDescription" style="margin-top: 8px; font-size: 14px; color: #555; font-style: italic;"></div>
      </div>

      <div class="prompt-editor">
        <div class="prompt-section" style="margin-bottom: 24px;">
          <label style="font-weight: 600; margin-bottom: 8px; display: block;">系统提示词：</label>
          <textarea id="systemPrompt" style="width: 100%; height: 400px; padding: 12px; border: 1px solid #e5e7eb; border-radius: 6px; font-family: monospace; resize: vertical;" placeholder="请输入完整的系统提示词，包括基本规则、输出格式、具体任务、字段要求、业务逻辑等..."></textarea>
        </div>

        <!-- 版本备注区域 -->
        <div class="version-note-section" style="margin-bottom: 20px;">
          <label style="font-weight: 600; margin-bottom: 8px; display: block;">版本更新说明：</label>
          <textarea id="versionNote" style="width: 100%; height: 80px; padding: 12px; border: 1px solid #e5e7eb; border-radius: 6px; resize: vertical;" placeholder="请描述本次更新的内容和改进点（可选）..."></textarea>
        </div>

        <div class="prompt-actions">
          <button class="btn btn-primary" onclick="savePromptConfig()">
            <span>💾</span> 保存为新版本
          </button>
          <button class="btn btn-secondary" onclick="updateCurrentVersion()">
            <span>✏️</span> 更新当前版本
          </button>
          <button class="btn btn-secondary" onclick="resetPromptConfig()">
            <span>🔄</span> 重置为默认
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
```

### 3.2 JavaScript代码

```javascript
// 显示提示词配置面板
function showPromptConfig() {
  showContentPanel('prompt-config-panel');
  loadPromptConfig();
}

// 加载提示词配置
function loadPromptConfig() {
  const analysisType = document.getElementById('promptTypeSelect').value;
  
  // 加载版本列表
  fetch(`/api/prompts/${analysisType}/versions`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        // 更新版本选择器
        const versionSelect = document.getElementById('versionSelect');
        versionSelect.innerHTML = '<option value="">请选择版本</option>';
        
        data.data.versions.forEach(version => {
          const option = document.createElement('option');
          option.value = version.id;
          option.textContent = `${version.version} - ${version.name}`;
          if (version.is_current) {
            option.selected = true;
          }
          versionSelect.appendChild(option);
        });
        
        // 更新当前版本信息
        const currentVersion = data.data.versions.find(v => v.is_current);
        if (currentVersion) {
          document.getElementById('currentVersionInfo').style.display = 'block';
          document.getElementById('currentVersionNumber').textContent = currentVersion.version;
          document.getElementById('currentVersionName').textContent = currentVersion.name;
          document.getElementById('currentVersionUsage').textContent = currentVersion.usage_count || 0;
          document.getElementById('currentVersionLastUsed').textContent = 
            currentVersion.last_used_at ? new Date(currentVersion.last_used_at).toLocaleString() : '从未使用';
          document.getElementById('currentVersionDescription').textContent = currentVersion.version_description || '';
        } else {
          document.getElementById('currentVersionInfo').style.display = 'none';
        }
        
        // 加载当前版本内容
        loadVersionContent();
      }
    })
    .catch(error => {
      console.error('加载版本列表失败:', error);
    });
}

// 加载版本内容
function loadVersionContent() {
  const versionId = document.getElementById('versionSelect').value;
  const analysisType = document.getElementById('promptTypeSelect').value;
  
  if (!versionId) {
    document.getElementById('systemPrompt').value = '';
    return;
  }
  
  fetch(`/api/prompts/${analysisType}/versions`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        const version = data.data.versions.find(v => v.id == versionId);
        if (version) {
          document.getElementById('systemPrompt').value = version.prompt || '';
        }
      }
    })
    .catch(error => {
      console.error('加载版本内容失败:', error);
    });
}

// 保存提示词配置
function savePromptConfig() {
  const analysisType = document.getElementById('promptTypeSelect').value;
  const systemPrompt = document.getElementById('systemPrompt').value;
  const versionNote = document.getElementById('versionNote').value;
  
  if (!systemPrompt.trim()) {
    alert('请输入系统提示词');
    return;
  }
  
  const versionData = {
    type: analysisType,
    system_prompt: systemPrompt,
    version_description: versionNote
  };
  
  fetch('/api/prompts/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(versionData)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert('保存成功');
      loadPromptConfig(); // 重新加载配置
    } else {
      alert('保存失败: ' + data.message);
    }
  })
  .catch(error => {
    alert('保存失败: ' + error.message);
  });
}

// 更新当前版本
function updateCurrentVersion() {
  const analysisType = document.getElementById('promptTypeSelect').value;
  const systemPrompt = document.getElementById('systemPrompt').value;
  
  if (!systemPrompt.trim()) {
    alert('请输入系统提示词');
    return;
  }
  
  const versionData = {
    type: analysisType,
    system_prompt: systemPrompt
  };
  
  fetch('/api/prompts/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(versionData)
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      alert('更新成功');
      loadPromptConfig(); // 重新加载配置
    } else {
      alert('更新失败: ' + data.message);
    }
  })
  .catch(error => {
    alert('更新失败: ' + error.message);
  });
}

// 重置为默认
function resetPromptConfig() {
  if (!confirm('确定要重置为默认提示词吗？')) {
    return;
  }
  
  const analysisType = document.getElementById('promptTypeSelect').value;
  
  fetch(`/api/prompts/${analysisType}`)
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        document.getElementById('systemPrompt').value = data.system_prompt || '';
        alert('已重置为默认提示词');
      } else {
        alert('重置失败: ' + data.message);
      }
    })
    .catch(error => {
      alert('重置失败: ' + error.message);
    });
}

// 显示创建版本模态框
function showCreateVersionModal() {
  // 实现创建版本的模态框显示逻辑
  alert('创建版本功能');
}

// 显示版本历史
function showVersionHistory() {
  // 实现版本历史显示逻辑
  alert('版本历史功能');
}
```

## 4. 菜单导航代码 (index.html)

### 4.1 HTML菜单结构

```html
<!-- 系统管理菜单 -->
<li class="menu-item admin-only system-menu">
  <a href="#" class="menu-link menu-toggle" data-submenu="systemManagement">
    <span class="menu-icon icon-settings">⚙️</span><span data-i18n="systemManagement">系统管理</span>
  </a>
  <ul class="submenu" id="systemManagement">
    <li class="submenu-item">
        <a href="#" class="submenu-link" data-panel="records-management-panel" onclick="showRecordsPage()">
        <span class="menu-icon">📋</span><span data-i18n="分析记录管理">分析记录管理</span>
      </a>
    </li>
    <li class="submenu-item">
      <a href="#" class="submenu-link" data-panel="review-panel" onclick="showReviewPage()">
        <span class="menu-icon">🔍</span><span data-i18n="管理员复核">管理员复核</span>
      </a>
    </li>
    <li class="submenu-item">
      <a href="#" class="submenu-link" data-panel="prompt-config-panel" onclick="showPromptConfig()">
        <span class="menu-icon">💡</span><span data-i18n="提示词管理">提示词管理</span>
      </a>
    </li>
    <li class="submenu-item">
      <a href="#" class="submenu-link" data-panel="user-management-panel" onclick="showUserManagement()">
        <span class="menu-icon">👥</span><span data-i18n="用户管理">用户管理</span>
      </a>
    </li>
    <li class="submenu-item">
      <a href="#" class="submenu-link" data-panel="system-settings-panel" onclick="showSystemSettings()">
        <span class="menu-icon">🔧</span><span data-i18n="系统设置">系统设置</span>
      </a>
    </li>
  </ul>
</li>
```

## 总结

本文档提取了系统中所有与提示词管理相关的前端代码，包括：

1. **独立的提示词配置页面** - 提供完整的提示词增删改查功能
2. **独立的提示词版本管理页面** - 专门用于管理提示词的版本
3. **首页中的提示词管理面板** - 集成在主界面中的提示词管理功能
4. **菜单导航代码** - 用于访问提示词管理功能的导航链接

这些前端代码与后端API接口配合工作，实现了完整的提示词管理功能。
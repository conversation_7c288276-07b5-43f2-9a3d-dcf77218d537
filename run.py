#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多场景智能化文档分析系统启动脚本
"""

import os
import sys
import subprocess

def check_requirements():
    """检查依赖是否安装"""
    try:
        import flask
        import flask_cors
        from PIL import Image
        import openai
        import requests
        import fitz
        import numpy
        import pandas
        import tqdm
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def install_requirements():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("多场景智能化文档分析系统")
    print("=" * 50)
    
    # 检查依赖
    if not check_requirements():
        response = input("是否自动安装依赖？(y/n): ")
        if response.lower() == 'y':
            if not install_requirements():
                return
        else:
            print("请手动运行: pip install -r requirements.txt")
            return
    
    # 创建必要目录
    directories = ['uploads', 'processed', 'static', 'templates']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ 目录结构检查完成")
    
    # 启动应用
    print("🚀 启动Web应用...")
    print("📱 请在浏览器中访问: http://localhost:5106")
    print("🛑 按 Ctrl+C 停止应用")
    print("-" * 50)
    
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5106)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main() 
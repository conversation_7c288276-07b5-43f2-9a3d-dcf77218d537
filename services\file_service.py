# -*- coding: utf-8 -*-
"""
文件处理服务
"""
import os
import uuid
import hashlib
import fitz  # PyMuPDF
from PIL import Image
from datetime import datetime
from flask import current_app
from utils.file_utils import (
    get_upload_folder, get_processed_folder, 
    calculate_file_hash, get_file_info
)

class FileService:
    """文件处理服务类"""
    
    def __init__(self):
        pass
    
    def process_pdf_to_images(self, pdf_path, analysis_type):
        """将PDF转换为图片"""
        try:
            # 打开PDF文件
            doc = fitz.open(pdf_path)
            processed_folder = get_processed_folder(analysis_type)
            os.makedirs(processed_folder, exist_ok=True)
            
            images = []
            base_filename = os.path.splitext(os.path.basename(pdf_path))[0]
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                
                # 设置缩放比例，提高图片质量
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)
                
                # 生成图片文件名
                image_filename = f"{base_filename}_page_{page_num + 1}_{uuid.uuid4().hex[:8]}.jpg"
                image_path = os.path.join(processed_folder, image_filename)
                
                # 保存图片
                pix.save(image_path)
                
                images.append({
                    'page_num': page_num + 1,
                    'filename': image_filename,
                    'filepath': image_path,
                    'width': pix.width,
                    'height': pix.height
                })
            
            doc.close()
            
            return {
                'success': True,
                'images': images,
                'total_pages': len(images)
            }
            
        except Exception as e:
            current_app.logger.error(f"PDF转图片失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def process_image_file(self, image_path, analysis_type):
        """处理图片文件"""
        try:
            processed_folder = get_processed_folder(analysis_type)
            os.makedirs(processed_folder, exist_ok=True)
            
            # 打开图片
            with Image.open(image_path) as img:
                # 获取图片信息
                width, height = img.size
                format_name = img.format
                
                # 如果图片过大，进行压缩
                max_size = (2048, 2048)
                if width > max_size[0] or height > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    width, height = img.size
                
                # 生成处理后的文件名
                base_filename = os.path.splitext(os.path.basename(image_path))[0]
                processed_filename = f"processed_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}_{base_filename}.jpg"
                processed_path = os.path.join(processed_folder, processed_filename)
                
                # 转换为RGB模式并保存为JPEG
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                img.save(processed_path, 'JPEG', quality=85, optimize=True)
            
            return {
                'success': True,
                'processed_image': {
                    'filename': processed_filename,
                    'filepath': processed_path,
                    'width': width,
                    'height': height,
                    'original_format': format_name
                }
            }
            
        except Exception as e:
            current_app.logger.error(f"图片处理失败: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def validate_file(self, file_path, analysis_type):
        """验证文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return {
                    'valid': False,
                    'error': '文件不存在'
                }
            
            # 获取文件信息
            file_info = get_file_info(file_path)
            if not file_info:
                return {
                    'valid': False,
                    'error': '无法获取文件信息'
                }
            
            # 检查文件大小
            max_size = current_app.config.get('MAX_CONTENT_LENGTH', 50 * 1024 * 1024)
            if file_info['file_size'] > max_size:
                return {
                    'valid': False,
                    'error': f'文件大小超过限制({max_size / 1024 / 1024:.1f}MB)'
                }
            
            # 检查文件类型
            allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', set())
            file_ext = os.path.splitext(file_path)[1].lower().lstrip('.')
            if file_ext not in allowed_extensions:
                return {
                    'valid': False,
                    'error': f'不支持的文件类型: {file_ext}'
                }
            
            # 验证分析类型
            valid_types = current_app.config.get('ANALYSIS_TYPES', {}).keys()
            if analysis_type not in valid_types:
                return {
                    'valid': False,
                    'error': f'无效的分析类型: {analysis_type}'
                }
            
            # 如果是PDF文件，检查是否可以正常打开
            if file_ext == 'pdf':
                try:
                    doc = fitz.open(file_path)
                    if len(doc) == 0:
                        return {
                            'valid': False,
                            'error': 'PDF文件为空或损坏'
                        }
                    doc.close()
                except Exception as e:
                    return {
                        'valid': False,
                        'error': f'PDF文件损坏: {str(e)}'
                    }
            
            # 如果是图片文件，检查是否可以正常打开
            elif file_ext in ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff']:
                try:
                    with Image.open(file_path) as img:
                        img.verify()
                except Exception as e:
                    return {
                        'valid': False,
                        'error': f'图片文件损坏: {str(e)}'
                    }
            
            return {
                'valid': True,
                'file_info': file_info
            }
            
        except Exception as e:
            current_app.logger.error(f"文件验证失败: {str(e)}")
            return {
                'valid': False,
                'error': f'文件验证失败: {str(e)}'
            }
    
    def check_file_duplicate(self, file_path, analysis_type):
        """检查文件是否重复"""
        try:
            file_hash = calculate_file_hash(file_path)
            if not file_hash:
                return {
                    'is_duplicate': False,
                    'error': '无法计算文件哈希值'
                }
            
            # 查询数据库中是否存在相同哈希值的文件
            from models import AnalysisRecord
            existing_record = AnalysisRecord.query.filter_by(
                file_hash=file_hash,
                analysis_type=analysis_type,
                file_status='active'
            ).first()
            
            if existing_record:
                return {
                    'is_duplicate': True,
                    'existing_record': existing_record.to_dict(),
                    'file_hash': file_hash
                }
            
            return {
                'is_duplicate': False,
                'file_hash': file_hash
            }
            
        except Exception as e:
            current_app.logger.error(f"重复检查失败: {str(e)}")
            return {
                'is_duplicate': False,
                'error': str(e)
            }
    
    def cleanup_temp_files(self, file_paths):
        """清理临时文件"""
        cleaned_count = 0
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    cleaned_count += 1
            except Exception as e:
                current_app.logger.warning(f"清理临时文件失败 {file_path}: {str(e)}")
        
        return cleaned_count
    
    def get_file_preview_info(self, file_path):
        """获取文件预览信息"""
        try:
            file_info = get_file_info(file_path)
            if not file_info:
                return None
            
            preview_info = {
                'filename': os.path.basename(file_path),
                'file_size': file_info['file_size'],
                'file_type': file_info['file_type'],
                'created_time': file_info['created_time'].isoformat(),
                'modified_time': file_info['modified_time'].isoformat()
            }
            
            # 如果是PDF文件，获取页数
            if file_info['file_type'] == 'pdf':
                preview_info['page_count'] = file_info.get('page_count', 0)
            
            # 如果是图片文件，获取尺寸
            elif file_info['file_type'] == 'image':
                try:
                    with Image.open(file_path) as img:
                        preview_info['width'] = img.width
                        preview_info['height'] = img.height
                        preview_info['format'] = img.format
                except Exception:
                    pass
            
            return preview_info
            
        except Exception as e:
            current_app.logger.error(f"获取文件预览信息失败: {str(e)}")
            return None

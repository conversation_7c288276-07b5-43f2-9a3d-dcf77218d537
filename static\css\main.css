/* 多场景智能化文档分析系统 - 现代化样式文件 */

/* 全局样式变量 */
:root {
    /* 主色调 */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --primary-bg: #f0f4ff;

    /* 辅助色 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* 中性色 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 布局 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --topbar-height: 70px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 动画 */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 重置和基础样式 */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--gray-50);
    color: var(--gray-700);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.modern-body {
    overflow-x: hidden;
}

/* ==================== 侧边栏样式 ==================== */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border-right: 1px solid var(--gray-200);
    z-index: 1000;
    transition: var(--transition);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid var(--gray-200);
    height: var(--topbar-height);
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: var(--transition);
}

.brand-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.brand-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.brand-text {
    overflow: hidden;
    transition: var(--transition);
}

.brand-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1;
}

.sidebar.collapsed .brand-text {
    opacity: 0;
    width: 0;
}

.sidebar-toggle {
    width: 32px;
    height: 32px;
    border: none;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
}

.sidebar-toggle:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

.sidebar-menu {
    padding: 1rem 0;
    height: calc(100vh - var(--topbar-height));
    overflow-y: auto;
    overflow-x: hidden;
}

.menu-section {
    margin-bottom: 2rem;
}

.menu-title {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 1.25rem;
    margin-bottom: 0.75rem;
    transition: var(--transition);
}

.sidebar.collapsed .menu-title {
    opacity: 0;
    height: 0;
    margin: 0;
    padding: 0;
}

.menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.25rem;
    color: var(--gray-700);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    border-radius: 0;
}

.menu-item:hover {
    background: var(--primary-bg);
    color: var(--primary-color);
    text-decoration: none;
}

.menu-item.active {
    background: var(--primary-bg);
    color: var(--primary-color);
    font-weight: 600;
}

.menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--primary-color);
}

.menu-item i {
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
    flex-shrink: 0;
}

.menu-item span {
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    white-space: nowrap;
}

.sidebar.collapsed .menu-item span {
    opacity: 0;
    width: 0;
}

.sidebar.collapsed .menu-item {
    justify-content: center;
    padding: 0.75rem;
}

/* 移动端侧边栏 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }

    .sidebar.mobile-show {
        transform: translateX(0);
    }

    .sidebar.collapsed {
        width: var(--sidebar-width);
        transform: translateX(-100%);
    }

    .sidebar.collapsed.mobile-show {
        transform: translateX(0);
    }
}

/* ==================== 主内容区域 ==================== */
.main-content {
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.main-content.expanded {
    margin-left: var(--sidebar-collapsed-width);
}

.top-navbar {
    height: var(--topbar-height);
    background: white;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.menu-toggle {
    display: none;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.menu-toggle:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.navbar-item {
    position: relative;
}

.notification-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.notification-btn:hover {
    background: var(--gray-200);
    color: var(--gray-800);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 18px;
    height: 18px;
    background: var(--danger-color);
    color: white;
    border-radius: 50%;
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
}

.user-dropdown {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.user-dropdown:hover {
    background: var(--gray-100);
}

.user-avatar {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    line-height: 1.2;
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
    line-height: 1;
}

.content-wrapper {
    flex: 1;
    padding: 1.5rem;
    background: var(--gray-50);
}

.content-footer {
    background: white;
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--gray-500);
}

.footer-right {
    display: flex;
    gap: 1.5rem;
}

.footer-right a {
    color: var(--gray-500);
    text-decoration: none;
    transition: var(--transition);
}

.footer-right a:hover {
    color: var(--primary-color);
}

/* 移动端遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

body.sidebar-open {
    overflow: hidden;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
    }

    .main-content.expanded {
        margin-left: 0;
    }

    .menu-toggle {
        display: flex;
    }

    .page-title {
        font-size: 1.25rem;
    }

    .user-info {
        display: none;
    }

    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .footer-right {
        gap: 1rem;
    }
}

/* ==================== 卡片样式 ==================== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    background: white;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
}

/* 现代化卡片变体 */
.modern-card {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.modern-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.gradient-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.gradient-card .card-header {
    background: transparent;
    border-bottom-color: rgba(255, 255, 255, 0.2);
    color: white;
}

/* ==================== 按钮样式 ==================== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    font-size: 0.875rem;
    padding: 0.625rem 1.25rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #3730a3 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

.btn-lg {
    padding: 0.875rem 2rem;
    font-size: 1rem;
    font-weight: 600;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* ==================== 表单样式 ==================== */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition);
    background: white;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-control::placeholder {
    color: var(--gray-400);
}

.form-select {
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    transition: var(--transition);
    background: white;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
    color: var(--primary-color);
}

/* 输入组样式 */
.input-group .form-control {
    border-radius: 0;
}

.input-group .form-control:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .form-control:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.input-group-text {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
}

/* ==================== 表格样式 ==================== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    box-shadow: var(--shadow);
    margin-bottom: 0;
}

.table thead th {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
    padding: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-700);
    font-size: 0.875rem;
}

.table tbody tr:hover {
    background: var(--gray-50);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 现代化表格 */
.modern-table {
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
}

.modern-table thead th {
    background: white;
    border-bottom: 2px solid var(--gray-200);
    font-weight: 700;
    color: var(--gray-900);
}

.modern-table tbody tr {
    transition: var(--transition);
}

.modern-table tbody tr:hover {
    background: var(--primary-bg);
    transform: scale(1.01);
}

/* ==================== 模态框样式 ==================== */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-header {
    background: white;
    border-bottom: 1px solid var(--gray-200);
    padding: 1.5rem;
}

.modal-title {
    font-weight: 700;
    color: var(--gray-900);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    padding: 1rem 1.5rem;
    gap: 0.75rem;
}

/* ==================== 徽章样式 ==================== */
.badge {
    font-weight: 600;
    font-size: 0.75rem;
    border-radius: var(--border-radius-sm);
    padding: 0.375rem 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge-primary {
    background: var(--primary-color);
    color: white;
}

.badge-success {
    background: var(--success-color);
    color: white;
}

.badge-warning {
    background: var(--warning-color);
    color: white;
}

.badge-danger {
    background: var(--danger-color);
    color: white;
}

.badge-secondary {
    background: var(--gray-500);
    color: white;
}

/* 现代化徽章 */
.modern-badge {
    background: var(--primary-bg);
    color: var(--primary-color);
    border: 1px solid var(--primary-light);
}

/* ==================== 进度条样式 ==================== */
.progress {
    height: 12px;
    border-radius: var(--border-radius);
    background: var(--gray-200);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    border-radius: var(--border-radius);
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* 圆形进度条 */
.progress-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--gray-200) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 80%;
    height: 80%;
    border-radius: 50%;
    background: white;
    position: absolute;
}

/* ==================== 警告框样式 ==================== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.modern-alert {
    display: flex;
    align-items: center;
    box-shadow: var(--shadow);
}

.alert-success {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    color: #065f46;
    border-left-color: var(--success-color);
}

.alert-warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    color: #92400e;
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-info {
    background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
    color: #1e40af;
    border-left-color: var(--info-color);
}

.alert .btn-close {
    filter: none;
    opacity: 0.7;
}

.alert .btn-close:hover {
    opacity: 1;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modal-header {
    border-bottom: 1px solid #e5e7eb;
    border-radius: 12px 12px 0 0;
}

.modal-footer {
    border-top: 1px solid #e5e7eb;
    border-radius: 0 0 12px 12px;
}

/* ==================== 下拉菜单样式 ==================== */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    background: white;
    min-width: 200px;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: var(--gray-700);
    transition: var(--transition);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.dropdown-item:hover {
    background: var(--primary-bg);
    color: var(--primary-color);
}

.dropdown-item:active {
    background: var(--primary-color);
    color: white;
}

.dropdown-header {
    padding: 0.75rem 1rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: var(--gray-200);
}

.dropdown-footer {
    padding: 0.5rem 1rem 0.75rem;
    border-top: 1px solid var(--gray-200);
    margin-top: 0.5rem;
}

.dropdown-footer a {
    font-size: 0.875rem;
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

/* 用户菜单样式 */
.user-menu {
    min-width: 280px;
}

.user-menu .dropdown-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--gray-50);
    margin: -0.5rem 0 0.5rem;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.user-avatar-large {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
}

/* 通知下拉菜单 */
.notification-dropdown {
    min-width: 320px;
    max-height: 400px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.notification-text {
    font-size: 0.8125rem;
    color: var(--gray-600);
    line-height: 1.4;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--gray-400);
}

/* ==================== 分页样式 ==================== */
.pagination {
    gap: 0.25rem;
}

.pagination .page-link {
    border: 1px solid var(--gray-300);
    color: var(--gray-700);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: var(--transition);
    background: white;
}

.pagination .page-link:hover {
    background: var(--primary-bg);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    box-shadow: var(--shadow);
}

.pagination .page-item.disabled .page-link {
    color: var(--gray-400);
    background: var(--gray-100);
    border-color: var(--gray-200);
}

/* ==================== 工具提示样式 ==================== */
.tooltip-inner {
    background: var(--gray-800);
    border-radius: var(--border-radius);
    font-size: 0.8125rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--gray-800);
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--gray-800);
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--gray-800);
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--gray-800);
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: var(--dark-color);
    border-radius: var(--border-radius);
}

/* ==================== 加载动画 ==================== */
.spinner-border {
    width: 2rem;
    height: 2rem;
    border-width: 0.25rem;
    border-color: var(--primary-color);
    border-right-color: transparent;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.125rem;
}

.spinner-grow {
    background-color: var(--primary-color);
}

/* 自定义加载器 */
.modern-loader {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.pulse-loader {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* 骨架屏加载 */
.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: var(--border-radius);
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
}

.skeleton-text:last-child {
    width: 60%;
    margin-bottom: 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .stat-number {
        font-size: 2rem !important;
    }
    
    .upload-area {
        padding: 2rem !important;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .stat-number {
        font-size: 1.5rem !important;
    }
    
    .upload-area {
        padding: 1.5rem !important;
    }
    
    .upload-area h5 {
        font-size: 1rem;
    }
    
    .upload-area p {
        font-size: 0.875rem;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.scale-in {
    animation: scaleIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停动画 */
.hover-lift {
    transition: var(--transition);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: var(--transition);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* 页面加载动画 */
.page-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid var(--gray-200);
    border-top: 6px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}

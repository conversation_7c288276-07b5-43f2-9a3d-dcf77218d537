/**
 * API工具类 - 封装了所有API调用
 */
const API = {
    /**
     * 发送GET请求
     * @param {string} url - API地址
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回响应数据
     */
    get: function(url, params = {}) {
        const queryString = Object.keys(params)
            .filter(key => params[key] !== undefined && params[key] !== null)
            .map(key => encodeURIComponent(key) + '=' + encodeURIComponent(params[key]))
            .join('&');
        
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return fetch(fullUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送POST请求
     * @param {string} url - API地址
     * @param {Object} data - 请求体数据
     * @returns {Promise} - 返回响应数据
     */
    post: function(url, data = {}) {
        return fetch(url, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送PUT请求
     * @param {string} url - API地址
     * @param {Object} data - 请求体数据
     * @returns {Promise} - 返回响应数据
     */
    put: function(url, data = {}) {
        return fetch(url, {
            method: 'PUT',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            credentials: 'same-origin',
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 发送DELETE请求
     * @param {string} url - API地址
     * @returns {Promise} - 返回响应数据
     */
    delete: function(url) {
        return fetch(url, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        });
    },
    
    /**
     * 上传文件
     * @param {string} url - API地址
     * @param {FormData} formData - 表单数据
     * @param {Function} onProgress - 进度回调
     * @returns {Promise} - 返回响应数据
     */
    upload: function(url, formData, onProgress) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.open('POST', url);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            // 进度事件
            if (onProgress) {
                xhr.upload.onprogress = function(event) {
                    if (event.lengthComputable) {
                        const percentComplete = (event.loaded / event.total) * 100;
                        onProgress(percentComplete);
                    }
                };
            }
            
            // 完成事件
            xhr.onload = function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('解析响应失败'));
                    }
                } else {
                    reject(new Error(`HTTP error! status: ${xhr.status}`));
                }
            };
            
            // 错误事件
            xhr.onerror = function() {
                reject(new Error('请求失败'));
            };
            
            xhr.send(formData);
        });
    }
};

/**
 * 工具函数集
 */
const Utils = {
    /**
     * 显示消息提示
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型: success, error, warning, info
     */
    showMessage: function(message, type = 'info') {
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            alert(`${type.toUpperCase()}: ${message}`);
        }
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节大小
     * @returns {string} - 格式化后的文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 格式化日期时间
     * @param {string} dateString - ISO格式的日期字符串
     * @returns {string} - 格式化后的日期时间
     */
    formatDateTime: function(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).format(date);
    },
    
    /**
     * 格式化日期
     * @param {string} dateString - ISO格式的日期字符串
     * @returns {string} - 格式化后的日期
     */
    formatDate: function(dateString) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        }).format(date);
    },
    
    /**
     * 生成随机ID
     * @returns {string} - 随机ID
     */
    generateId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    },
    
    /**
     * 保存文件标签
     * @param {number} fileId - 文件ID
     * @param {string} tagName - 标签名称
     * @param {string} tagColor - 标签颜色
     * @returns {Promise} - 返回响应数据
     */
    saveTag: function(fileId, tagName, tagColor = '#2563eb') {
        return API.post('/api/tags', {
            file_id: fileId,
            tag_name: tagName,
            tag_color: tagColor
        });
    },
    
    /**
     * 删除文件标签
     * @param {number} tagId - 标签ID
     * @returns {Promise} - 返回响应数据
     */
    deleteTag: function(tagId) {
        return API.delete(`/api/tags/${tagId}`);
    }
};

// 添加到全局作用域
window.API = API;
window.Utils = Utils;
/**
 * 多场景智能化文档分析系统 - 主JavaScript文件
 */

// 全局变量
window.AppConfig = {
    apiBaseUrl: '/api',
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedExtensions: ['pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'],
    analysisTypes: {
        'futures_account': '期货账户',
        'wealth_management': '理财产品',
        'broker_interest': '券商计息',
        'account_opening': '账户开户场景',
        'ningyin_fee': '宁银费用',
        'non_standard_trade': '非标交易'
    }
};

// 工具函数
const Utils = {
    /**
     * 显示消息提示
     */
    showMessage: function(message, type = 'info', duration = 5000) {
        const container = document.getElementById('messageContainer');
        if (!container) return;

        const alertClass = {
            'error': 'alert-danger',
            'success': 'alert-success',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show modern-alert" role="alert">
                <i class="bi bi-${this.getIconByType(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        container.innerHTML = alertHtml;

        // 自动消失
        if (duration > 0) {
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, duration);
        }
    },
    
    /**
     * 根据类型获取图标
     */
    getIconByType: function(type) {
        const icons = {
            'error': 'exclamation-triangle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    },
    
    /**
     * 格式化日期时间
     */
    formatDateTime: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    },
    
    /**
     * 获取文件扩展名
     */
    getFileExtension: function(filename) {
        return filename.split('.').pop().toLowerCase();
    },
    
    /**
     * 验证文件类型
     */
    validateFileType: function(filename) {
        const ext = this.getFileExtension(filename);
        return window.AppConfig.allowedExtensions.includes(ext);
    },
    
    /**
     * 验证文件大小
     */
    validateFileSize: function(size) {
        return size <= window.AppConfig.maxFileSize;
    },

    /**
     * 获取分析类型的中文名称
     */
    getAnalysisTypeName: function(typeId) {
        return window.AppConfig.analysisTypes[typeId] || typeId;
    },
    
    /**
     * 防抖函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    /**
     * 节流函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    /**
     * 深拷贝对象
     */
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    },
    
    /**
     * 生成UUID
     */
    generateUUID: function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    /**
     * 数字动画效果
     */
    animateNumber: function(element, start, end, duration, suffix = '') {
        const startTime = performance.now();
        const difference = end - start;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = start + (difference * easeOutQuart);

            if (suffix === '%') {
                element.textContent = current.toFixed(1) + suffix;
            } else {
                element.textContent = Math.floor(current).toLocaleString() + suffix;
            }

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    },

    /**
     * 获取状态颜色
     */
    getStatusColor: function(status) {
        const colors = {
            'pending': 'warning',
            'processing': 'info',
            'completed': 'success',
            'failed': 'danger',
            'reviewing': 'primary'
        };
        return colors[status] || 'secondary';
    },

    /**
     * 获取状态文本
     */
    getStatusText: function(status) {
        const texts = {
            'pending': '待处理',
            'processing': '处理中',
            'completed': '已完成',
            'failed': '失败',
            'reviewing': '复核中'
        };
        return texts[status] || status;
    },

    /**
     * 复制到剪贴板
     */
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            return navigator.clipboard.writeText(text).then(() => {
                this.showMessage('已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                this.showMessage('复制失败', 'error');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showMessage('已复制到剪贴板', 'success');
            } catch (err) {
                console.error('复制失败:', err);
                this.showMessage('复制失败', 'error');
            }
            document.body.removeChild(textArea);
        }
    },

    /**
     * 下载文件
     */
    downloadFile: function(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
};

// API请求封装
const API = {
    /**
     * 发送GET请求
     */
    get: function(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        
        return axios.get(fullUrl)
            .then(response => response.data)
            .catch(error => {
                console.error('API GET Error:', error);
                throw error;
            });
    },
    
    /**
     * 发送POST请求
     */
    post: function(url, data = {}) {
        return axios.post(url, data)
            .then(response => response.data)
            .catch(error => {
                console.error('API POST Error:', error);
                throw error;
            });
    },
    
    /**
     * 发送PUT请求
     */
    put: function(url, data = {}) {
        return axios.put(url, data)
            .then(response => response.data)
            .catch(error => {
                console.error('API PUT Error:', error);
                throw error;
            });
    },
    
    /**
     * 发送DELETE请求
     */
    delete: function(url) {
        return axios.delete(url)
            .then(response => response.data)
            .catch(error => {
                console.error('API DELETE Error:', error);
                throw error;
            });
    },
    
    /**
     * 上传文件
     */
    uploadFile: function(file, analysisType, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', analysisType);
        
        const config = {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        };
        
        if (onProgress) {
            config.onUploadProgress = function(progressEvent) {
                const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                onProgress(percentCompleted);
            };
        }
        
        return axios.post('/api/upload', formData, config)
            .then(response => response.data)
            .catch(error => {
                console.error('File Upload Error:', error);
                throw error;
            });
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 设置axios默认配置
    axios.defaults.timeout = 30000;
    axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
    
    // 全局错误处理
    axios.interceptors.response.use(
        response => response,
        error => {
            if (error.response) {
                switch (error.response.status) {
                    case 401:
                        Utils.showMessage('登录已过期，请重新登录', 'error');
                        setTimeout(() => {
                            window.location.href = '/auth/login';
                        }, 2000);
                        break;
                    case 403:
                        Utils.showMessage('权限不足', 'error');
                        break;
                    case 404:
                        Utils.showMessage('请求的资源不存在', 'error');
                        break;
                    case 500:
                        Utils.showMessage('服务器内部错误', 'error');
                        break;
                    default:
                        Utils.showMessage(error.response.data.message || '请求失败', 'error');
                }
            } else if (error.request) {
                Utils.showMessage('网络连接失败，请检查网络', 'error');
            } else {
                Utils.showMessage('请求配置错误', 'error');
            }
            return Promise.reject(error);
        }
    );
    
    // 添加页面加载动画
    document.body.classList.add('fade-in');
});

// 导出到全局
if (!window.Utils) {
    window.Utils = Utils;
}
window.API = API;

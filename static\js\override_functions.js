// override_functions.js - 覆盖函数以修复版本显示问题
console.log('加载覆盖函数...');

// 覆盖loadPromptConfig函数
function loadPromptConfig() {
    console.log('使用覆盖的loadPromptConfig函数');
    
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // 获取当前分析类型
    const analysisTypeElement = document.getElementById('promptTypeSelect');
    if (!analysisTypeElement) {
        console.error('找不到分析类型选择器元素');
        return;
    }
    const analysisType = analysisTypeElement.value;
    console.log('当前分析类型:', analysisType);
    
    // 添加缓存破坏参数
    const timestamp = new Date().getTime();
    const randomStr = Math.random().toString(36).substring(7);
    const url = `/api/prompt-versions/${encodeURIComponent(analysisType)}?t=${timestamp}&r=${randomStr}`;
    
    console.log('请求URL:', url);
    
    fetch(url, {
        method: 'GET',
        headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        }
    })
    .then(response => {
        console.log('API响应状态:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API返回的原始数据:', data);
        
        // 隐藏加载状态
        document.getElementById('loadingOverlay').style.display = 'none';
        
        if (data.success && data.versions) {
            // 确保allVersions是数组
            allVersions = Array.isArray(data.versions) ? data.versions : [];
            console.log('处理后的版本列表:', allVersions);
            
            // 确保每个版本对象都有正确的属性
            allVersions = allVersions.map(version => ({
                id: version.id || 0,
                version: version.version || 'v1.0',
                name: version.name || version.description || '默认版本',
                description: version.description || version.name || '默认版本',
                prompt: version.prompt || version.prompt_content || '',
                is_active: Boolean(version.is_active),
                is_current: Boolean(version.is_current),
                created_at: version.created_at || new Date().toISOString()
            }));
            
            console.log('最终版本列表:', allVersions);
            
            // 更新版本选择器
            updateVersionSelector();
            
            // 加载当前版本内容
            loadCurrentVersionContent();
            
            // 显示调试信息
            showDebugInfo();
        } else {
            console.error('API返回错误:', data);
            showMessage('加载版本配置失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('请求失败:', error);
        document.getElementById('loadingOverlay').style.display = 'none';
        showMessage('加载版本配置失败: ' + error.message, 'error');
    });
}

// 覆盖updateVersionSelector函数
function updateVersionSelector() {
    console.log('使用覆盖的updateVersionSelector函数');
    
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect) {
        console.error('找不到版本选择器元素');
        return;
    }
    
    // 清空现有选项
    versionSelect.innerHTML = '';
    
    if (!allVersions || allVersions.length === 0) {
        console.log('没有可用的版本');
        const option = document.createElement('option');
        option.value = '';
        option.textContent = '暂无可用版本';
        versionSelect.appendChild(option);
        return;
    }
    
    console.log('开始渲染版本选项，总数:', allVersions.length);
    
    // 按激活状态和创建时间排序
    const sortedVersions = [...allVersions].sort((a, b) => {
        // 激活版本优先
        if (a.is_active && !b.is_active) return -1;
        if (!a.is_active && b.is_active) return 1;
        // 然后按创建时间倒序
        return new Date(b.created_at) - new Date(a.created_at);
    });
    
    sortedVersions.forEach((version, index) => {
        const option = document.createElement('option');
        option.value = version.id;
        
        // 构建显示文本 - 使用版本号和版本名称拼接
        let displayText = `${version.version} - ${version.name}`;
        if (version.is_active) {
            displayText = `⭐ ${displayText}`;
        }
        if (version.version === 'v1.0') {
            displayText += ' (默认)';
        }
        
        option.textContent = displayText;
        
        // 添加调试属性
        option.setAttribute('data-version-id', version.id);
        option.setAttribute('data-version-number', version.version);
        option.setAttribute('data-version-name', version.name);
        option.setAttribute('data-is-active', version.is_active);
        
        versionSelect.appendChild(option);
        
        console.log(`添加选项 ${index + 1}:`, {
            id: version.id,
            version: version.version,
            name: version.name,
            is_active: version.is_active,
            display_text: displayText
        });
    });
    
    console.log('版本选择器更新完成，选项数量:', versionSelect.options.length);
}

// 覆盖loadCurrentVersionContent函数
function loadCurrentVersionContent() {
    console.log('使用覆盖的loadCurrentVersionContent函数');
    
    const versionSelect = document.getElementById('versionSelect');
    if (!versionSelect || versionSelect.value === '') {
        console.log('没有选中的版本');
        return;
    }
    
    const selectedVersionId = parseInt(versionSelect.value);
    console.log('选中的版本ID:', selectedVersionId);
    
    // 在内存中查找版本
    const selectedVersion = allVersions.find(v => v.id === selectedVersionId);
    if (selectedVersion) {
        console.log('在内存中找到版本:', selectedVersion);
        document.getElementById('systemPrompt').value = selectedVersion.prompt || '';
        return;
    }
    
    console.log('在内存中未找到版本，从API获取');
    
    // 从API获取版本内容
    const analysisType = document.getElementById('promptTypeSelect').value;
    const url = `/api/prompt-versions/${encodeURIComponent(analysisType)}/${selectedVersionId}`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.version) {
                console.log('API返回的版本内容:', data.version);
                document.getElementById('systemPrompt').value = data.version.prompt || '';
            } else {
                console.error('获取版本内容失败:', data);
            }
        })
        .catch(error => {
            console.error('获取版本内容失败:', error);
        });
}

// 显示调试信息
function showDebugInfo() {
    console.log('显示调试信息');
    
    // 创建或更新调试面板
    let debugPanel = document.getElementById('debugPanel');
    if (!debugPanel) {
        debugPanel = document.createElement('div');
        debugPanel.id = 'debugPanel';
        debugPanel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 400px;
            max-height: 300px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 9999;
            overflow-y: auto;
        `;
        document.body.appendChild(debugPanel);
    }
    
    // 显示版本信息
    let debugContent = '<h4>调试信息</h4>';
    debugContent += '<p><strong>版本总数:</strong> ' + (allVersions ? allVersions.length : 0) + '</p>';
    
    if (allVersions && allVersions.length > 0) {
        debugContent += '<h5>版本列表:</h5>';
        allVersions.forEach((version, index) => {
            debugContent += `<div style="margin: 5px 0; padding: 5px; border: 1px solid #ccc;">
                <strong>${index + 1}.</strong> ID: ${version.id}<br>
                版本号: ${version.version}<br>
                名称: ${version.name}<br>
                激活: ${version.is_active}<br>
                显示文本: ${version.version} - ${version.name}
            </div>`;
        });
    }
    
    debugPanel.innerHTML = debugContent;
}

// 强制覆盖原始函数
console.log('强制覆盖函数已加载');
window.loadPromptConfig = loadPromptConfig;
window.updateVersionSelector = updateVersionSelector;
window.loadCurrentVersionContent = loadCurrentVersionContent;
window.showDebugInfo = showDebugInfo;

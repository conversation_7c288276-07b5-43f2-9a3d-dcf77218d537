/**
 * 三列结果对比功能
 */
class ResultComparison {
    constructor() {
        this.currentRecord = null;
        this.editingExpected = false;
        this.fieldStructures = this.getFieldStructures();
        // 新增：未匹配字段、自动切换
        this.mismatchedFields = [];
        this.autoSwitch = false;
        this.init();
    }

    init() {
        this.bindEvents();
    }

    bindEvents() {
        // 编辑预期结果按钮
        $('#edit-expected-btn').on('click', () => {
            this.toggleEditMode();
        });

        // 保存预期结果按钮
        $('#save-expected-result-btn').on('click', () => {
            this.saveExpectedResult();
        });

        // 重新计算准确率按钮
        $('#recalculate-accuracy-btn').on('click', () => {
            this.recalculateAccuracy();
        });
        // 字段编辑保存按钮
        $('#save-field-btn').on('click', () => {
            this.saveFieldValue();
        });

        // 新增：审核按钮及开关
        $('#approve-btn').on('click', () => {
            this.submitReview('approved');
        });

        $('#reject-btn').on('click', () => {
            this.submitReview('rejected');
        });

        $('#auto-switch-toggle').on('change', (e) => {
            this.autoSwitch = e.target.checked;
        });
    }

    /**
     * 获取各分析类型的字段结构定义
     */
    getFieldStructures() {
        return {
            'futures_account': [
                { key: 'product_name', label: '产品名称', path: '产品名称' },
                { key: 'fund_account', label: '资金账号', path: '资金账号' },
                { key: 'member_codes', label: '会员号', path: '会员号' },
                { key: 'trading_codes', label: '交易编码', path: '交易编码' },
                { key: 'start_time', label: '开始时间', path: '开始时间' },
                { key: 'end_time', label: '结束时间', path: '结束时间' }
            ],
            'broker_interest': [
                { key: 'product_name', label: '产品名称', path: '产品名称' },
                { key: 'product_category', label: '产品类别', path: '产品类别' },
                { key: 'interest_rate', label: '利率(年化)', path: '利率(年化).all' },
                { key: 'start_time', label: '开始时间', path: '开始时间' },
                { key: 'end_time', label: '截止时间', path: '截止时间' },
                { key: 'interest_days', label: '计息天数', path: '计息天数' },
                { key: 'remarks', label: '备注', path: '备注' }
            ],
            'account_opening': [
                { key: 'customer_name', label: '客户姓名', path: '客户姓名' },
                { key: 'account_type', label: '账户类型', path: '账户类型' },
                { key: 'account_number', label: '账户号码', path: '账户号码' },
                { key: 'opening_date', label: '开户日期', path: '开户日期' },
                { key: 'branch_name', label: '开户网点', path: '开户网点' },
                { key: 'id_number', label: '身份证号', path: '身份证号' }
            ],
            'financial': [
                { key: 'product_name', label: '产品名称', path: '产品信息.产品名称' },
                { key: 'product_code', label: '产品代码', path: '产品信息.产品代码' },
                { key: 'product_type', label: '产品类型', path: '产品信息.产品类型' },
                { key: 'manager', label: '管理人', path: '产品信息.管理人' },
                { key: 'custodian', label: '托管人', path: '产品信息.托管人' },
                { key: 'establishment_date', label: '成立日期', path: '产品信息.成立日期' },
                { key: 'subscription_fee', label: '认购费', path: '费率结构.认购费' },
                { key: 'management_fee', label: '固定管理费', path: '费率结构.固定管理费' },
                { key: 'custodian_fee', label: '托管费', path: '费率结构.托管费' }
            ],
            'ningyin_fee': [
                { key: 'product_name', label: '产品名称', path: '产品信息.产品名称' },
                { key: 'product_code', label: '产品代码', path: '产品信息.产品代码' },
                { key: 'change_type', label: '变更类型', path: '费用变更信息.变更类型' },
                { key: 'original_rate', label: '原费率', path: '费用变更信息.原费率' },
                { key: 'new_rate', label: '新费率', path: '费用变更信息.新费率' },
                { key: 'effective_date', label: '生效日期', path: '费用变更信息.生效日期' }
            ],
            'non_standard_trade': [
                { key: 'trade_date', label: '交易日期', path: '交易日期' },
                { key: 'product_name', label: '产品名称', path: '产品名称' },
                { key: 'trade_amount', label: '交易金额', path: '交易金额' },
                { key: 'trade_type', label: '交易类型', path: '交易类型' },
                { key: 'counterparty', label: '交易对手', path: '交易对手' },
                { key: 'confirmation_number', label: '确认单号', path: '确认单号' }
            ]
        };
    }

    /**
     * 提交审核结果
     */
    submitReview(status) {
        if (!this.currentRecord) return;

        const recordId = this.currentRecord.id;
        const comment = $('#review-reason').val() || '';

        // 使用正确的审核API端点
        API.post(`/api/files/${recordId}/audit`, {
            audit_status: status,
            audit_comment: comment
        }).then(response => {
            if (response.success) {
                this.showMessage('审核提交成功', 'success');
                // 关闭当前弹窗
                $('#resultComparisonModal').modal('hide');

                if (this.autoSwitch) {
                    this.switchToNextPending();
                }

                // 刷新文件列表
                if (typeof refreshFileList === 'function') {
                    refreshFileList();
                }
            } else {
                this.showMessage('审核提交失败: ' + response.message, 'error');
            }
        }).catch(err => {
            console.error('提交审核失败', err);
            this.showMessage('提交审核失败', 'error');
        });
    }

    /**
     * 切换到下一个待审核记录
     */
    switchToNextPending() {
        // 使用正确的API端点获取下一个待审核文件
        API.get('/api/files/next-pending').then(res => {
            if (res.success && res.data) {
                const nextRecord = res.data;
                // 延迟一点点再打开，避免模态框冲突
                setTimeout(() => {
                    this.showModal(nextRecord.id);
                }, 300);
            } else {
                this.showMessage('没有更多待审核记录', 'info');
            }
        }).catch(err => {
            console.error('获取下一条待审核记录失败', err);
            this.showMessage('获取下一条待审核记录失败', 'error');
        });
    }

    /**
     * 显示结果对比弹窗
     */
    showModal(recordId) {
        // 获取HTTP客户端
        const httpClient = window.axios || {
            get: (url) => fetch(url).then(r => r.json().then(data => ({data})))
        };

        // 获取记录详情
        httpClient.get(`/api/records/${recordId}`)
            .then(response => {
                if (response.data.success) {
                    this.currentRecord = response.data.data;
                    this.renderModal();
                    $('#resultComparisonModal').modal('show');
                    // 重置输入
                    $('#review-reason').val('');
                } else {
                    this.showMessage('获取记录详情失败: ' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('获取记录详情失败:', error);
                this.showMessage('获取记录详情失败', 'error');
            });
    }

    /**
     * 渲染弹窗内容
     */
    renderModal() {
        if (!this.currentRecord) return;

        // 更新基本信息
        $('#modal-filename').text(this.currentRecord.filename || '-');
        $('#modal-analysis-type').text(this.getAnalysisTypeName(this.currentRecord.analysis_type) || '-');
        $('#modal-status').text(this.getStatusText(this.currentRecord.status)).attr('class', `status-badge ${this.currentRecord.status}`);
        $('#modal-field-accuracy').text(this.formatAccuracy(this.currentRecord.field_accuracy));
        $('#modal-accuracy-score').text(this.formatAccuracy(this.currentRecord.accuracy_score));
        $('#modalRecordInfo').text(`文件: ${this.currentRecord.filename} | 类型: ${this.getAnalysisTypeName(this.currentRecord.analysis_type)}`);

        // 渲染三列内容
        this.renderThreeColumns();

        // 更新准确率条
        this.updateAccuracyBars();
    }

    /**
     * 渲染三列对比内容
     */
    renderThreeColumns() {
        const analysisType = this.currentRecord.analysis_type;
        const fields = this.fieldStructures[analysisType] || [];

        const aiResult = this.parseResult(this.currentRecord.ai_result);
        const expectedResult = this.parseResult(this.currentRecord.expected_result);

        // 清空现有内容
        $('#ai-result-content').empty();
        $('#expected-result-content').empty();
        $('#comparison-result-content').empty();

        // 重置未匹配字段列表
        this.mismatchedFields = [];

        if (fields.length === 0) {
            // 如果没有定义字段结构，显示原始JSON
            this.renderRawJson();
            return;
        }

        // 渲染每个字段
        fields.forEach(field => {
            const aiValue = this.getValueByPath(aiResult, field.path);
            const expectedValue = this.getValueByPath(expectedResult, field.path);
            const isMatch = this.compareValues(aiValue, expectedValue);

            if (!isMatch) {
                this.mismatchedFields.push(field.label);
            }

            this.renderFieldRow(field, aiValue, expectedValue, isMatch);
        });

        // 渲染未匹配字段区域
        this.renderUnmatchedFields();
    }

    /**
     * 渲染单个字段行
     */
    renderFieldRow(field, aiValue, expectedValue, isMatch) {
        // AI识别内容列
        const aiHtml = `
            <div class="field-row">
                <div class="field-label">${field.label}</div>
                <div class="field-value">${this.formatValue(aiValue)}</div>
            </div>
        `;
        $('#ai-result-content').append(aiHtml);

        // 预期结果列
        const expectedHtml = `
            <div class="field-row">
                <div class="field-label">${field.label}</div>
                <div class="field-value" data-field="${field.key}">
                    ${this.editingExpected ?
                        `<input type="text" class="field-edit-input" value="${this.formatValue(expectedValue)}" data-field="${field.key}">` :
                        this.formatValue(expectedValue)
                    }
                </div>
            </div>
        `;
        $('#expected-result-content').append(expectedHtml);

        // 对比结果列
        const comparisonHtml = `
            <div class="comparison-icon ${isMatch ? 'comparison-match' : 'comparison-mismatch'}">
                ${isMatch ? '✓' : '✗'}
            </div>
        `;
        $('#comparison-result-content').append(comparisonHtml);
    }

    /**
     * 渲染预期正确结果
     */
    renderExpectedResult(expectedResult) {
        const container = $('#expected-result-content');
        if (this.editingExpected) {
            container.html(this.renderEditableJsonContent(expectedResult));
        } else {
            container.html(this.renderJsonContent(expectedResult, 'expected'));
        }
    }

    /**
     * 渲染对比结果
     */
    renderComparisonResult(fieldAccuracy) {
        const container = $('#comparison-result-content');
        if (!fieldAccuracy || !fieldAccuracy.comparison) {
            container.html('<p class="text-muted">暂无对比结果</p>');
            return;
        }

        const html = this.renderComparisonTree(fieldAccuracy.comparison);
        container.html(html);
    }

    /**
     * 渲染字段准确率统计
     */
    renderFieldAccuracyStats(fieldAccuracy) {
        const container = $('#field-accuracy-stats');
        if (!fieldAccuracy) {
            container.html('<p class="text-muted">暂无准确率统计</p>');
            return;
        }

        const overallAccuracy = fieldAccuracy.overall_accuracy || 0;
        const accuracyPercent = (overallAccuracy * 100).toFixed(2);

        let html = `
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <span class="accuracy-label me-2">总体准确率:</span>
                        <div class="progress flex-grow-1 me-2">
                            <div class="progress-bar ${this.getAccuracyColor(overallAccuracy)}" 
                                 style="width: ${accuracyPercent}%"></div>
                        </div>
                        <span class="badge ${this.getAccuracyBadgeColor(overallAccuracy)}">${accuracyPercent}%</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        计算时间: ${fieldAccuracy.calculated_at ? new Date(fieldAccuracy.calculated_at).toLocaleString() : '未知'}
                    </small>
                </div>
            </div>
        `;

        container.html(html);
    }

    /**
     * 渲染JSON内容
     */
    renderJsonContent(data, type) {
        if (!data) {
            return '<p class="text-muted">暂无数据</p>';
        }

        return this.renderJsonTree(data, '', type);
    }

    /**
     * 渲染可编辑的JSON内容
     */
    renderEditableJsonContent(data) {
        if (!data) {
            return '<p class="text-muted">暂无数据</p>';
        }

        return this.renderEditableJsonTree(data, '');
    }

    /**
     * 递归渲染JSON树
     */
    renderJsonTree(obj, path, type) {
        if (typeof obj !== 'object' || obj === null) {
            return `<div class="field-item">
                <div class="field-path">${path || 'value'}</div>
                <div class="field-value ${type}-value">${this.escapeHtml(String(obj))}</div>
            </div>`;
        }

        let html = '';
        if (Array.isArray(obj)) {
            obj.forEach((item, index) => {
                const itemPath = path ? `${path}[${index}]` : `[${index}]`;
                html += this.renderJsonTree(item, itemPath, type);
            });
        } else {
            Object.keys(obj).forEach(key => {
                const keyPath = path ? `${path}.${key}` : key;
                html += this.renderJsonTree(obj[key], keyPath, type);
            });
        }

        return html;
    }

    /**
     * 递归渲染可编辑的JSON树
     */
    renderEditableJsonTree(obj, path) {
        if (typeof obj !== 'object' || obj === null) {
            return `<div class="field-item">
                <div class="field-path">${path || 'value'}</div>
                <div class="field-value expected-value">
                    ${this.escapeHtml(String(obj))}
                    <button class="btn btn-sm btn-outline-primary field-edit-btn" 
                            onclick="resultComparison.editField('${path}', '${this.escapeHtml(String(obj))}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                </div>
            </div>`;
        }

        let html = '';
        if (Array.isArray(obj)) {
            obj.forEach((item, index) => {
                const itemPath = path ? `${path}[${index}]` : `[${index}]`;
                html += this.renderEditableJsonTree(item, itemPath);
            });
        } else {
            Object.keys(obj).forEach(key => {
                const keyPath = path ? `${path}.${key}` : key;
                html += this.renderEditableJsonTree(obj[key], keyPath);
            });
        }

        return html;
    }

    /**
     * 渲染对比树
     */
    renderComparisonTree(comparison, path = '') {
        if (!comparison) return '';

        const isMatch = comparison.match;
        const matchClass = isMatch ? 'match' : 'mismatch';
        const icon = isMatch ? 'bi-check-circle text-success' : 'bi-x-circle text-danger';

        let html = `<div class="field-item ${matchClass}">
            <div class="field-path">
                <i class="bi ${icon} me-1"></i>
                ${path || 'root'}
            </div>`;

        if (comparison.children) {
            if (typeof comparison.children === 'object' && !Array.isArray(comparison.children)) {
                Object.keys(comparison.children).forEach(key => {
                    const childPath = path ? `${path}.${key}` : key;
                    html += this.renderComparisonTree(comparison.children[key], childPath);
                });
            } else if (Array.isArray(comparison.children)) {
                comparison.children.forEach((child, index) => {
                    const childPath = path ? `${path}[${index}]` : `[${index}]`;
                    html += this.renderComparisonTree(child, childPath);
                });
            }
        } else {
            html += `<div class="field-value">
                <div><strong>AI:</strong> ${this.escapeHtml(String(comparison.ai_value || ''))}</div>
                <div><strong>预期:</strong> ${this.escapeHtml(String(comparison.expected_value || ''))}</div>
            </div>`;
        }

        html += '</div>';
        return html;
    }

    /**
     * 切换编辑模式
     */
    toggleEditMode() {
        this.editingExpected = !this.editingExpected;
        
        if (this.editingExpected) {
            $('#edit-expected-btn').html('<i class="bi bi-eye"></i>').attr('title', '查看模式');
            $('#save-expected-result-btn').show();
        } else {
            $('#edit-expected-btn').html('<i class="bi bi-pencil"></i>').attr('title', '编辑预期结果');
            $('#save-expected-result-btn').hide();
        }

        this.renderExpectedResult(this.currentRecord.expected_result);
    }

    /**
     * 编辑字段
     */
    editField(path, value) {
        this.currentFieldPath = path;
        $('#field-path').val(path);
        $('#field-value').val(value);
        $('#fieldEditModal').modal('show');
    }

    /**
     * 保存字段值
     */
    saveFieldValue() {
        const path = this.currentFieldPath;
        const value = $('#field-value').val();

        // 更新当前记录的预期结果
        this.updateFieldInObject(this.currentRecord.expected_result, path, value);
        
        // 重新渲染预期结果
        this.renderExpectedResult(this.currentRecord.expected_result);
        
        $('#fieldEditModal').modal('hide');
    }

    /**
     * 在对象中更新字段值
     */
    updateFieldInObject(obj, path, value) {
        const keys = path.split(/[\.\[\]]/).filter(key => key !== '');
        let current = obj;

        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!(key in current)) {
                current[key] = {};
            }
            current = current[key];
        }

        const lastKey = keys[keys.length - 1];
        current[lastKey] = value;
    }

    /**
     * 保存预期结果
     */
    saveExpectedResult() {
        const recordId = this.currentRecord.id;
        const updatedData = {};

        // 收集所有编辑的字段值
        $('.field-edit-input').each((index, input) => {
            const fieldKey = $(input).data('field');
            const fieldValue = $(input).val();
            updatedData[fieldKey] = fieldValue;
        });

        // 获取HTTP客户端
        const httpClient = window.axios || {
            put: (url, data) => fetch(url, {
                method: 'PUT',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(data)
            }).then(r => r.json().then(data => ({data})))
        };

        // 发送更新请求 - 使用正确的API端点
        httpClient.put(`/api/files/${recordId}/expected-result`, {expected_result: updatedData})
            .then(response => {
                if (response.data.success) {
                    this.showMessage('预期结果保存成功', 'success');
                    this.toggleEditMode();
                    // 重新获取记录数据
                    this.showModal(recordId);
                } else {
                    this.showMessage('保存失败: ' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存预期结果失败:', error);
                this.showMessage('保存失败', 'error');
            });
    }

    /**
     * 重新计算准确率
     */
    recalculateAccuracy() {
        const recordId = this.currentRecord.id;

        // 获取HTTP客户端
        const httpClient = window.axios || {
            post: (url) => fetch(url, {method: 'POST'}).then(r => r.json().then(data => ({data})))
        };

        httpClient.post(`/api/records/${recordId}/recalculate-accuracy`)
            .then(response => {
                if (response.data.success) {
                    this.showMessage('准确率重新计算完成', 'success');
                    // 重新获取记录数据
                    this.showModal(recordId);
                } else {
                    this.showMessage('重新计算失败: ' + response.data.message, 'error');
                }
            })
            .catch(error => {
                console.error('重新计算准确率失败:', error);
                this.showMessage('重新计算失败', 'error');
            });
    }

    /**
     * 获取分析类型名称
     */
    getAnalysisTypeName(type) {
        const typeNames = {
            'futures_account': '期货账户',
            'future': '期货开户文件解析',
            'financial': '理财产品说明书',
            'wealth_management': '理财产品',
            'broker_interest': '券商账户计息变更',
            'account_opening': '账户开户场景',
            'ningyin_fee': '宁银理财费用变更',
            'ningxia_bank_fee': '宁银费用',
            'non_standard_trade': '非标交易确认单解析'
        };
        return typeNames[type] || type;
    }

    /**
     * 获取准确率颜色
     */
    getAccuracyColor(accuracy) {
        if (accuracy >= 0.9) return 'bg-success';
        if (accuracy >= 0.7) return 'bg-warning';
        return 'bg-danger';
    }

    /**
     * 获取准确率徽章颜色
     */
    getAccuracyBadgeColor(accuracy) {
        if (accuracy >= 0.9) return 'bg-success';
        if (accuracy >= 0.7) return 'bg-warning';
        return 'bg-danger';
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 解析结果数据
     */
    parseResult(result) {
        if (!result) return {};
        if (typeof result === 'string') {
            try {
                return JSON.parse(result);
            } catch (e) {
                return {};
            }
        }
        return result;
    }

    /**
     * 根据路径获取值
     */
    getValueByPath(obj, path) {
        if (!obj || !path) return '';

        const keys = path.split('.');
        let value = obj;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return '';
            }
        }

        return value;
    }

    /**
     * 比较两个值是否相等
     */
    compareValues(value1, value2) {
        // 处理空值
        if (!value1 && !value2) return true;
        if (!value1 || !value2) return false;

        // 转换为字符串进行比较
        const str1 = String(value1).trim();
        const str2 = String(value2).trim();

        return str1 === str2;
    }

    /**
     * 格式化显示值
     */
    formatValue(value) {
        if (value === null || value === undefined || value === '') {
            return '<span class="text-muted">-</span>';
        }

        if (typeof value === 'object') {
            return JSON.stringify(value, null, 2);
        }

        return String(value);
    }

    /**
     * 格式化准确率
     */
    formatAccuracy(accuracy) {
        if (!accuracy && accuracy !== 0) return '-';
        return (accuracy * 100).toFixed(1) + '%';
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'failed': '失败',
            'processing': '处理中',
            'pending': '待处理'
        };
        return statusMap[status] || status;
    }

    /**
     * 更新准确率条
     */
    updateAccuracyBars() {
        const fieldAccuracy = this.currentRecord.field_accuracy || 0;
        const overallAccuracy = this.currentRecord.accuracy_score || 0;

        // 更新字段准确率
        $('#field-accuracy-bar').css('width', (fieldAccuracy * 100) + '%');
        $('#field-accuracy-text').text(this.formatAccuracy(fieldAccuracy));

        // 更新整体准确率
        $('#overall-accuracy-bar').css('width', (overallAccuracy * 100) + '%');
        $('#overall-accuracy-text').text(this.formatAccuracy(overallAccuracy));
    }

    /**
     * 显示原始JSON（当没有字段结构定义时）
     */
    renderRawJson() {
        const aiResult = this.currentRecord.ai_result;
        const expectedResult = this.currentRecord.expected_result;

        $('#ai-result-content').html(`<pre class="json-content">${JSON.stringify(aiResult, null, 2)}</pre>`);
        $('#expected-result-content').html(`<pre class="json-content">${JSON.stringify(expectedResult, null, 2)}</pre>`);
        $('#comparison-result-content').html('<div class="text-center text-muted">原始数据对比</div>');
    }

    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 简单的消息显示，可以后续优化为toast
        alert(message);
    }

    /**
     * 渲染未匹配字段列表
     */
    renderUnmatchedFields() {
        if (this.mismatchedFields.length === 0) {
            $('#unmatched-fields-card').hide();
            return;
        }

        const listEl = $('#unmatched-fields-list');
        listEl.empty();
        this.mismatchedFields.forEach(name => {
            listEl.append(`<li class="list-group-item list-group-item-danger">${name}</li>`);
        });
        $('#unmatched-fields-card').show();
    }
}

// 全局实例
const resultComparison = new ResultComparison();

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - {{ SYSTEM_NAME }}</title>
    
    <!-- CSS样式 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e5e7eb;
            padding: 12px 16px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
        }
        
        .input-group-text {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-right: none;
        }
        
        .input-group .form-control {
            border-left: none;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="login-card">
        <div class="login-header">
            <h3 class="mb-0">
                <i class="bi bi-bank me-2"></i>
                {{ SYSTEM_NAME }}
            </h3>
            <p class="mb-0 mt-2 opacity-75">用户登录</p>
        </div>
        
        <div class="login-body">
            <!-- 消息提示 -->
            <div id="messageContainer"></div>
            
            <form id="loginForm">
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-person"></i>
                        </span>
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="请输入用户名" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-lock"></i>
                        </span>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="请输入密码" required>
                    </div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        记住我
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100" id="loginBtn">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    登录
                </button>
            </form>
            
            <div class="text-center mt-3">
                <p class="text-muted">还没有账号？ 
                    <a href="{{ url_for('auth.register') }}" class="text-decoration-none">立即注册</a>
                </p>
            </div>
            
            <!-- 默认账号提示 -->
            <div class="mt-4">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="bi bi-info-circle me-2"></i>
                        默认测试账号
                    </h6>
                    <hr>
                    <p class="mb-1"><strong>管理员:</strong> admin / admin123</p>
                    <p class="mb-1"><strong>分析师:</strong> analyst / analyst123</p>
                    <p class="mb-0"><strong>普通用户:</strong> user / user123</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    
    <script>
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            container.innerHTML = alertHtml;
        }
        
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const loginBtn = document.getElementById('loginBtn');
            const originalText = loginBtn.innerHTML;
            
            // 显示加载状态
            loginBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>登录中...';
            loginBtn.disabled = true;
            
            // 发送登录请求
            axios.post('/auth/login', {
                username: formData.get('username'),
                password: formData.get('password'),
                remember: formData.get('remember') === 'on'
            })
            .then(response => {
                if (response.data.success) {
                    showMessage('登录成功，正在跳转...', 'success');
                    setTimeout(() => {
                        window.location.href = response.data.redirect || '/';
                    }, 1000);
                } else {
                    showMessage(response.data.message, 'error');
                    loginBtn.innerHTML = originalText;
                    loginBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('登录失败:', error);
                let message = '登录失败，请稍后重试';
                if (error.response && error.response.data && error.response.data.message) {
                    message = error.response.data.message;
                }
                showMessage(message, 'error');
                loginBtn.innerHTML = originalText;
                loginBtn.disabled = false;
            });
        });
        
        // 回车键登录
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - {{ SYSTEM_NAME }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 500px;
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-header h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .register-header p {
            color: #666;
            margin: 0;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1rem 0.75rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-register:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }
        
        .login-link {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .login-link a:hover {
            color: #764ba2;
            text-decoration: underline;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1rem;
        }
        
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background-color: #e9ecef;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background-color: #dc3545; width: 25%; }
        .strength-fair { background-color: #fd7e14; width: 50%; }
        .strength-good { background-color: #ffc107; width: 75%; }
        .strength-strong { background-color: #198754; width: 100%; }
        
        .form-check {
            margin: 1rem 0;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .system-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .system-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <div class="system-logo">
                <img src="{{ url_for('static', filename='img/80_80.png') }}" alt="系统Logo">
            </div>
            <h2>{{ SYSTEM_NAME }}</h2>
            <p>创建您的账户</p>
        </div>
        
        <!-- 消息提示 -->
        <div id="messageContainer"></div>
        
        <form id="registerForm">
            <div class="form-floating">
                <input type="text" class="form-control" id="username" name="username" 
                       placeholder="用户名" required minlength="3" maxlength="20">
                <label for="username">
                    <i class="bi bi-person me-2"></i>用户名
                </label>
                <div class="form-text">3-20个字符，只能包含字母、数字和下划线</div>
            </div>
            
            <div class="form-floating">
                <input type="email" class="form-control" id="email" name="email" 
                       placeholder="邮箱地址" required>
                <label for="email">
                    <i class="bi bi-envelope me-2"></i>邮箱地址
                </label>
            </div>
            
            <div class="form-floating">
                <input type="tel" class="form-control" id="phone" name="phone" 
                       placeholder="手机号码">
                <label for="phone">
                    <i class="bi bi-phone me-2"></i>手机号码（可选）
                </label>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="密码" required minlength="8">
                <label for="password">
                    <i class="bi bi-lock me-2"></i>密码
                </label>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <small class="text-muted" id="strengthText">请输入至少8位密码</small>
                </div>
            </div>
            
            <div class="form-floating">
                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" 
                       placeholder="确认密码" required>
                <label for="confirmPassword">
                    <i class="bi bi-lock-fill me-2"></i>确认密码
                </label>
            </div>
            
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                <label class="form-check-label" for="agreeTerms">
                    我已阅读并同意 <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">用户协议</a> 和 <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">隐私政策</a>
                </label>
            </div>
            
            <button type="submit" class="btn btn-register" id="registerBtn">
                <i class="bi bi-person-plus me-2"></i>
                注册账户
            </button>
        </form>
        
        <div class="login-link">
            <p>已有账户？<a href="{{ url_for('auth.login') }}">立即登录</a></p>
        </div>
    </div>
    
    <!-- 用户协议模态框 -->
    <div class="modal fade" id="termsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">用户协议</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. 服务条款</h6>
                    <p>欢迎使用{{ SYSTEM_NAME }}。通过注册和使用本系统，您同意遵守以下条款和条件。</p>
                    
                    <h6>2. 用户责任</h6>
                    <p>您承诺提供真实、准确的注册信息，并保证账户安全。您对使用账户进行的所有活动负责。</p>
                    
                    <h6>3. 使用规范</h6>
                    <p>您同意不会使用本系统进行任何非法、有害或违反道德的活动。</p>
                    
                    <h6>4. 知识产权</h6>
                    <p>本系统的所有内容和功能均受知识产权法保护。未经授权，不得复制、修改或分发。</p>
                    
                    <h6>5. 免责声明</h6>
                    <p>本系统按"现状"提供服务，不对服务的准确性、可靠性或适用性作任何保证。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 隐私政策模态框 -->
    <div class="modal fade" id="privacyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">隐私政策</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>1. 信息收集</h6>
                    <p>我们收集您提供的注册信息，包括用户名、邮箱地址和手机号码等。</p>
                    
                    <h6>2. 信息使用</h6>
                    <p>我们使用您的信息来提供服务、改进用户体验，以及与您沟通重要信息。</p>
                    
                    <h6>3. 信息保护</h6>
                    <p>我们采用行业标准的安全措施来保护您的个人信息，防止未经授权的访问、使用或披露。</p>
                    
                    <h6>4. 信息共享</h6>
                    <p>除法律要求外，我们不会与第三方共享您的个人信息。</p>
                    
                    <h6>5. Cookie使用</h6>
                    <p>我们使用Cookie来改善用户体验和分析网站使用情况。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    
    <script>
        // 显示消息
        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const alertClass = type === 'error' ? 'alert-danger' : 
                              type === 'success' ? 'alert-success' : 
                              type === 'warning' ? 'alert-warning' : 'alert-info';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            container.innerHTML = alertHtml;
            
            // 3秒后自动消失
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 3000);
        }
        
        // 密码强度检测
        function checkPasswordStrength(password) {
            let strength = 0;
            let text = '';
            let className = '';
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            switch (strength) {
                case 0:
                case 1:
                    text = '密码强度：弱';
                    className = 'strength-weak';
                    break;
                case 2:
                    text = '密码强度：一般';
                    className = 'strength-fair';
                    break;
                case 3:
                    text = '密码强度：良好';
                    className = 'strength-good';
                    break;
                case 4:
                case 5:
                    text = '密码强度：强';
                    className = 'strength-strong';
                    break;
            }
            
            return { strength, text, className };
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('registerForm');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            // 密码强度实时检测
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const result = checkPasswordStrength(password);
                
                strengthFill.className = `strength-fill ${result.className}`;
                strengthText.textContent = result.text;
            });
            
            // 确认密码验证
            confirmPasswordInput.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirmPassword = this.value;
                
                if (confirmPassword && password !== confirmPassword) {
                    this.setCustomValidity('密码不匹配');
                } else {
                    this.setCustomValidity('');
                }
            });
            
            // 表单提交
            registerForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(this);
                const data = {
                    username: formData.get('username'),
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    password: formData.get('password'),
                    confirm_password: formData.get('confirm_password')
                };
                
                // 验证密码匹配
                if (data.password !== data.confirm_password) {
                    showMessage('两次输入的密码不匹配', 'error');
                    return;
                }
                
                // 验证用户协议
                if (!document.getElementById('agreeTerms').checked) {
                    showMessage('请先同意用户协议和隐私政策', 'warning');
                    return;
                }
                
                const registerBtn = document.getElementById('registerBtn');
                const originalText = registerBtn.innerHTML;
                registerBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>注册中...';
                registerBtn.disabled = true;
                
                // 发送注册请求
                axios.post('/auth/register', data)
                    .then(response => {
                        if (response.data.success) {
                            showMessage('注册成功！正在跳转到登录页面...', 'success');
                            setTimeout(() => {
                                window.location.href = '/auth/login';
                            }, 2000);
                        } else {
                            showMessage(response.data.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('注册失败:', error);
                        if (error.response && error.response.data && error.response.data.message) {
                            showMessage(error.response.data.message, 'error');
                        } else {
                            showMessage('注册失败，请稍后重试', 'error');
                        }
                    })
                    .finally(() => {
                        registerBtn.innerHTML = originalText;
                        registerBtn.disabled = false;
                    });
            });
        });
    </script>
</body>
</html>

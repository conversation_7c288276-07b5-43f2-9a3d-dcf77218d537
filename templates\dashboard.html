{% extends "base.html" %}

{% block title %}首页 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}首页{% endblock %}

{% block extra_css %}
<style>
    /* 统计卡片样式 */
    .stats-card {
        background: white;
        border-radius: var(--border-radius-lg);
        padding: 2rem;
        box-shadow: var(--shadow);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    }
    
    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }
    
    .stats-icon {
        width: 64px;
        height: 64px;
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.75rem;
        color: white;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
        line-height: 1;
    }
    
    .stats-label {
        color: var(--gray-600);
        font-size: 0.875rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }
    
    .stats-change {
        font-size: 0.8125rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }
    
    .stats-change.positive {
        color: var(--success-color);
    }
    
    .stats-change.negative {
        color: var(--danger-color);
    }
    
    /* 图表区域样式 */
    .chart-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        height: 100%; /* 确保图表卡片高度一致 */
        display: flex;
        flex-direction: column;
    }

    .chart-header {
        background: var(--gray-50);
        padding: 1.5rem 2rem;
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-body {
        padding: 2rem;
        flex: 1; /* 让图表内容区域填充剩余空间 */
    }

    #trendChart {
        width: 100% !important;
        height: 100% !important;
    }

    .chart-section-title {
        color: var(--gray-700);
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid var(--primary-color);
        display: inline-block;
    }
    
    /* 快速操作样式 */
    .quick-actions {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        height: 100%; /* 使快速操作区块高度与左侧图表区块一致 */
        display: flex;
        flex-direction: column;
    }
    
    .quick-actions-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 1.5rem 2rem;
    }
    
    .quick-actions-body {
        padding: 2rem;
        flex: 1; /* 让内容区域填充剩余空间 */
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }
    
    .quick-action-btn {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius);
        text-decoration: none;
        color: var(--gray-700);
        transition: var(--transition);
        margin-bottom: 1rem;
    }
    
    .quick-action-btn:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        color: var(--primary-color);
        text-decoration: none;
        transform: translateX(4px);
    }
    
    .quick-action-btn:last-child {
        margin-bottom: 0;
    }
    
    .quick-action-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        background: var(--gray-100);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.125rem;
        transition: var(--transition);
    }
    
    .quick-action-btn:hover .quick-action-icon {
        background: var(--primary-color);
        color: white;
    }
    
    /* 活动列表样式 */
    .activity-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        border-radius: var(--border-radius);
        background: white;
        border: 1px solid var(--gray-200);
        transition: var(--transition);
    }
    
    .activity-item:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-sm);
        border-color: var(--gray-300);
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
        min-width: 0;
    }
    
    .activity-title {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
    }
    
    .activity-desc {
        color: var(--gray-700);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    
    .activity-time {
        font-size: 0.75rem;
        color: var(--gray-500);
    }
    
    /* 等高列样式 */
    .equal-height-row {
        display: flex;
        flex-wrap: wrap;
    }

    .equal-height-row > [class*="col-"] {
        display: flex;
        flex-direction: column;
    }

    /* 最近活动样式 */
    .activity-item {
        padding: 1rem 0;
        border-bottom: 1px solid var(--gray-100);
        display: flex;
        align-items: center;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 0.875rem;
        flex-shrink: 0;
    }
    
    .activity-content {
        flex: 1;
        min-width: 0;
    }
    
    .activity-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
    }
    
    .activity-desc {
        font-size: 0.8125rem;
        color: var(--gray-600);
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.75rem;
        color: var(--gray-400);
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
        .stats-number {
            font-size: 2rem;
        }
        
        .stats-icon {
            width: 48px;
            height: 48px;
            font-size: 1.25rem;
        }
        
        .quick-action-btn {
            padding: 0.75rem 1rem;
        }
        
        .quick-action-icon {
            width: 32px;
            height: 32px;
            font-size: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<!-- 欢迎区域 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-2 text-gray-900">
                    {% if current_user.is_authenticated %}
                        欢迎回来，{{ current_user.username }}！
                    {% else %}
                        欢迎使用智能文档分析系统
                    {% endif %}
                </h1>
                <p class="text-muted mb-0">这里是您的工作概览，快速了解系统运行状态</p>
            </div>
            <div class="text-end">
                <div class="text-muted small">
                    <i class="bi bi-calendar3 me-1"></i>
                    <span id="currentDate"></span>
                </div>
                <div class="text-muted small">
                    <i class="bi bi-clock me-1"></i>
                    <span id="currentTime"></span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);">
                <i class="bi bi-file-earmark-text"></i>
            </div>
            <div class="stats-number" id="totalFiles">-</div>
            <div class="stats-label">总文件数</div>
            <div class="stats-change positive" id="filesChange">
                <i class="bi bi-arrow-up me-1"></i>+12% 本月
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">
                <i class="bi bi-check-circle"></i>
            </div>
            <div class="stats-number" id="todayProcessed">-</div>
            <div class="stats-label">今日处理</div>
            <div class="stats-change positive" id="processedChange">
                <i class="bi bi-arrow-up me-1"></i>+8% 昨日
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">
                <i class="bi bi-clock-history"></i>
            </div>
            <div class="stats-number" id="pendingReviews">-</div>
            <div class="stats-label">待复核</div>
            <div class="stats-change" id="pendingChange">
                <i class="bi bi-dash me-1"></i>无变化
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="stats-card hover-lift">
            <div class="stats-icon" style="background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);">
                <i class="bi bi-graph-up"></i>
            </div>
            <div class="stats-number" id="accuracyRate">-</div>
            <div class="stats-label">识别准确率</div>
            <div class="stats-change positive" id="accuracyChange">
                <i class="bi bi-arrow-up me-1"></i>+2.5% 本月
            </div>
        </div>
    </div>
</div>

<div class="row equal-height-row">
    <!-- 业务类型分析图表 -->
    <div class="col-lg-8 mb-4">
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="mb-0">
                    <i class="bi bi-bar-chart me-2"></i>
                    业务类型分析
                </h5>
            </div>
            <div class="chart-body">
                <div class="row">
                    <!-- 类型分布 -->
                    <div class="col-md-6">
                        <h6 class="mb-3">
                            <i class="bi bi-pie-chart me-2"></i>
                            类型分布
                        </h6>
                        <div style="height: 300px; position: relative;">
                            <canvas id="typeChart"></canvas>
                            <div id="typeChartLoading" class="text-center py-4" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="text-muted mt-2 mb-0">加载类型分布...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 分析趋势 -->
                    <div class="col-md-6">
                        <h6 class="mb-3">
                            <i class="bi bi-graph-up me-2"></i>
                            分析趋势
                        </h6>
                        <div style="height: 300px; position: relative;">
                            <canvas id="trendChart"></canvas>
                            <div id="trendChartLoading" class="text-center py-4" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <p class="text-muted mt-2 mb-0">加载趋势图...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 快速操作 -->
    <div class="col-lg-4 mb-4">
        <div class="quick-actions">
            <div class="quick-actions-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    快速操作
                </h5>
            </div>
            <div class="quick-actions-body">
                <a href="{{ url_for('main.document_analysis') }}" class="quick-action-btn">
                    <div class="quick-action-icon">
                        <i class="bi bi-file-earmark-text"></i>
                    </div>
                    <div>
                        <div class="fw-semibold">文档分析</div>
                        <small class="text-muted">上传并分析文档</small>
                    </div>
                </a>
                <a href="{{ url_for('main.records') }}" class="quick-action-btn">
                    <div class="quick-action-icon">
                        <i class="bi bi-database"></i>
                    </div>
                    <div>
                        <div class="fw-semibold">记录管理</div>
                        <small class="text-muted">查看所有分析记录</small>
                    </div>
                </a>
                <a href="{{ url_for('main.review') }}" class="quick-action-btn">
                    <div class="quick-action-icon">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                    <div>
                        <div class="fw-semibold">复核管理</div>
                        <small class="text-muted">处理待复核记录</small>
                    </div>
                </a>
                {% if current_user.role == 'admin' %}
                <a href="{{ url_for('main.model_config') }}" class="quick-action-btn">
                    <div class="quick-action-icon">
                        <i class="bi bi-cpu"></i>
                    </div>
                    <div>
                        <div class="fw-semibold">模型配置</div>
                        <small class="text-muted">配置AI模型参数</small>
                    </div>
                </a>
                {% endif %}
                
                <hr class="my-3">
                
                <h6 class="text-muted mb-3">
                    <i class="bi bi-file-earmark-check me-2"></i>
                    支持的分析类型
                </h6>
                
                <div class="row g-2" id="analysisTypes">
                    <!-- 将由JS动态生成分析类型链接 -->
                    <div class="text-center py-2">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <span class="text-muted ms-2 small">加载分析类型...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近活动 -->
<div class="row">
    <div class="col-12">
        <div class="chart-card">
            <div class="chart-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history me-2"></i>
                    最近活动
                </h5>
            </div>
            <div class="chart-body">
                <div id="recentActivities">
                    <div class="text-center py-4">
                        <div class="modern-loader mx-auto mb-3"></div>
                        <p class="text-muted mb-0">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js"></script>
<script>
    // 检查Chart.js是否加载成功
    window.addEventListener('load', function() {
        if (typeof Chart === 'undefined') {
            console.error('Chart.js加载失败，尝试备用CDN');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js';
            script.onload = function() {
                console.log('Chart.js备用CDN加载成功');
            };
            document.head.appendChild(script);
        } else {
            console.log('Chart.js加载成功');
        }
    });
</script>
<script>
    // 全局变量
    window.trendChart = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializePage();
        loadDashboardData();

        // 延迟加载图表，确保Chart.js已加载
        setTimeout(() => {
            loadTypeStats();
            loadTrendData();
        }, 500);

        loadRecentActivities();
        loadAnalysisTypes();
        updateDateTime();
        setInterval(updateDateTime, 1000); // 每秒更新时间
    });

    // 初始化页面
    function initializePage() {
        // 添加页面加载动画
        document.body.classList.add('fade-in');

        // 设置统计卡片动画
        const statsCards = document.querySelectorAll('.stats-card');
        statsCards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('slide-up');
            }, index * 100);
        });
    }

    // 更新日期时间
    function updateDateTime() {
        const now = new Date();
        const dateOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        };
        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        };

        const dateElement = document.getElementById('currentDate');
        const timeElement = document.getElementById('currentTime');

        if (dateElement) {
            dateElement.textContent = now.toLocaleDateString('zh-CN', dateOptions);
        }
        if (timeElement) {
            timeElement.textContent = now.toLocaleTimeString('zh-CN', timeOptions);
        }
    }

    // 加载仪表盘数据
    function loadDashboardData() {
        API.get('/api/dashboard/stats')
            .then(response => {
                if (response.success) {
                    updateStatsCards(response.data);
                } else {
                    console.error('加载统计数据失败:', response.message);
                }
            })
            .catch(error => {
                console.error('加载统计数据失败:', error);
                // 显示模拟数据
                updateStatsCards({
                    total_files: 1248,
                    today_processed: 56,
                    pending_reviews: 12,
                    accuracy_rate: 94.5
                });
            });
    }

    // 更新统计卡片
    function updateStatsCards(data) {
        const elements = {
            totalFiles: document.getElementById('totalFiles'),
            todayProcessed: document.getElementById('todayProcessed'),
            pendingReviews: document.getElementById('pendingReviews'),
            accuracyRate: document.getElementById('accuracyRate')
        };

        // 使用动画更新数字
        if (elements.totalFiles) {
            animateNumber(elements.totalFiles, 0, data.total_files || 0, 1000);
        }
        if (elements.todayProcessed) {
            animateNumber(elements.todayProcessed, 0, data.today_processed || 0, 800);
        }
        if (elements.pendingReviews) {
            animateNumber(elements.pendingReviews, 0, data.pending_reviews || 0, 600);
        }
        if (elements.accuracyRate) {
            animateNumber(elements.accuracyRate, 0, data.accuracy_rate || 0, 1200, '%');
        }
    }

    // 数字动画效果
    function animateNumber(element, start, end, duration, suffix = '') {
        const startTime = performance.now();
        const difference = end - start;

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = start + (difference * easeOutQuart);

            if (suffix === '%') {
                element.textContent = current.toFixed(1) + suffix;
            } else {
                element.textContent = Math.floor(current).toLocaleString() + suffix;
            }

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        requestAnimationFrame(updateNumber);
    }



    // 加载类型统计
    function loadTypeStats() {
        const canvas = document.getElementById('typeChart');
        if (!canvas) return;

        API.get('/api/dashboard/type-stats')
            .then(response => {
                if (response.success) {
                    renderTypeStats(response.data);
                } else {
                    console.error('加载类型统计失败:', response.message);
                    // 显示模拟数据
                    renderTypeStats([
                        { type: '期货账户', count: 245, percentage: 35.2 },
                        { type: '理财产品', count: 189, percentage: 27.1 },
                        { type: '券商计息', count: 134, percentage: 19.2 },
                        { type: '期货会员', count: 78, percentage: 11.2 },
                        { type: '宁银费用', count: 32, percentage: 4.6 },
                        { type: '非标交易', count: 18, percentage: 2.7 }
                    ]);
                }
            })
            .catch(error => {
                console.error('加载类型统计失败:', error);
                // 显示模拟数据
                renderTypeStats([
                    { type: '期货账户', count: 245, percentage: 35.2 },
                    { type: '理财产品', count: 189, percentage: 27.1 },
                    { type: '券商计息', count: 134, percentage: 19.2 },
                    { type: '期货会员', count: 78, percentage: 11.2 },
                    { type: '宁银费用', count: 32, percentage: 4.6 },
                    { type: '非标交易', count: 18, percentage: 2.7 }
                ]);
            });
    }

    // 渲染类型统计饼状图
    function renderTypeStats(stats) {
        // 隐藏加载动画
        const loadingElement = document.getElementById('typeChartLoading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        const canvas = document.getElementById('typeChart');
        if (!canvas) return;

        // 如果已存在图表，先销毁
        if (window.typeChart && typeof window.typeChart.destroy === 'function') {
            window.typeChart.destroy();
        }

        const ctx = canvas.getContext('2d');

        // 准备数据
        const labels = stats.map(stat => stat.type);
        const data = stats.map(stat => stat.count);
        const percentages = stats.map(stat => stat.percentage);

        // 饼状图颜色
        const colors = [
            '#6366f1', // 紫蓝色
            '#10b981', // 绿色
            '#f59e0b', // 橙色
            '#ef4444', // 红色
            '#06b6d4', // 青色
            '#8b5cf6', // 紫色
            '#f97316', // 橙红色
            '#84cc16'  // 黄绿色
        ];

        // 创建饼状图
        window.typeChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, stats.length),
                    borderColor: '#ffffff',
                    borderWidth: 2,
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const percentage = percentages[context.dataIndex];
                                return `${label}: ${value} 个文件 (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });
    }

    // 加载趋势数据
    function loadTrendData() {
        API.get('/api/dashboard/trend-stats')
            .then(response => {
                if (response.success) {
                    window.trendData = response.data;
                    // 延迟初始化，确保Chart.js加载完成
                    setTimeout(initTrendChart, 500);
                } else {
                    console.error('加载趋势数据失败:', response.message);
                    // 使用模拟数据
                    generateMockTrendData();
                    setTimeout(initTrendChart, 500);
                }
            })
            .catch(error => {
                console.error('加载趋势数据失败:', error);
                generateMockTrendData();
                setTimeout(initTrendChart, 500);
            });
    }

    // 生成模拟趋势数据
    function generateMockTrendData() {
        const types = ['期货账户', '理财产品', '券商计息', '期货会员', '宁银费用', '非标交易'];
        const months = [];
        const data = {};

        // 生成最近6个月的标签（简化显示）
        for (let i = 5; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            months.push(date.toLocaleDateString('zh-CN', { month: 'short' }));
        }

        // 为每个类型生成数据
        types.forEach(type => {
            data[type] = [];
            let baseValue = Math.floor(Math.random() * 30) + 5;

            for (let i = 0; i < 6; i++) {
                // 添加一些随机波动
                const variation = (Math.random() - 0.5) * 10;
                const value = Math.max(0, baseValue + variation);
                data[type].push(Math.floor(value));
                baseValue = value * (0.8 + Math.random() * 0.4); // 使趋势更连续
            }
        });

        window.trendData = {
            labels: months,
            datasets: data
        };

        console.log('生成模拟趋势数据:', window.trendData);
    }

    // 初始化趋势图表
    function initTrendChart() {
        const ctx = document.getElementById('trendChart');
        if (!ctx) {
            console.error('找不到趋势图canvas元素');
            return;
        }
        if (!window.trendData) {
            console.error('趋势数据未加载');
            return;
        }

        console.log('初始化趋势图表，数据:', window.trendData);

        if (typeof Chart === 'undefined') {
            console.error('Chart.js未加载');
            return;
        }

        const colors = [
            '#6366f1', // 紫色
            '#10b981', // 绿色
            '#f59e0b', // 黄色
            '#ef4444', // 红色
            '#06b6d4', // 青色
            '#8b5cf6'  // 紫罗兰色
        ];

        const datasets = Object.keys(window.trendData.datasets).map((type, index) => ({
            label: type,
            data: window.trendData.datasets[type],
            borderColor: colors[index % colors.length],
            backgroundColor: colors[index % colors.length] + '20',
            borderWidth: 2,
            fill: false,
            tension: 0.3,
            pointBackgroundColor: colors[index % colors.length],
            pointBorderColor: '#ffffff',
            pointBorderWidth: 1,
            pointRadius: 3,
            pointHoverRadius: 5
        }));

        console.log('图表数据集:', datasets);

        // 隐藏加载指示器
        const loadingElement = document.getElementById('trendChartLoading');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        try {
            // 如果已存在图表，先销毁
            if (window.trendChart && typeof window.trendChart.destroy === 'function') {
                window.trendChart.destroy();
            }

            window.trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: window.trendData.labels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false
                    },
                    legend: {
                        display: true,
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 15,
                            font: {
                                size: 10
                            },
                            boxWidth: 12,
                            boxHeight: 12
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#6366f1',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        titleFont: {
                            size: 12
                        },
                        bodyFont: {
                            size: 11
                        },
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y} 个`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: false
                        },
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            maxRotation: 45,
                            minRotation: 45
                        }
                    },
                    y: {
                        display: true,
                        title: {
                            display: false
                        },
                        beginAtZero: true,
                        grid: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            callback: function(value) {
                                return value;
                            }
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                },
                animation: {
                    duration: 1000,
                    easing: 'easeInOutQuart'
                },
                elements: {
                    point: {
                        radius: 3,
                        hoverRadius: 5
                    },
                    line: {
                        borderWidth: 2
                    }
                }
            }
            });

            console.log('趋势图表初始化成功');
        } catch (error) {
            console.error('趋势图表初始化失败:', error);
            // 显示错误消息
            const container = ctx.parentElement;
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-exclamation-triangle display-4 mb-3"></i>
                    <p class="mb-0">趋势图加载失败</p>
                    <small>请刷新页面重试</small>
                </div>
            `;
        }
    }

    // 加载最近活动
    function loadRecentActivities() {
        const activitiesContainer = document.getElementById('recentActivities');
        if (!activitiesContainer) return;

        API.get('/api/dashboard/recent-activities')
            .then(response => {
                if (response.success) {
                    renderRecentActivities(response.data);
                } else {
                    console.error('加载最近活动失败:', response.message);
                    // 显示模拟数据
                    renderRecentActivities(generateMockActivities());
                }
            })
            .catch(error => {
                console.error('加载最近活动失败:', error);
                // 显示模拟数据
                renderRecentActivities(generateMockActivities());
            });
    }

    // 加载分析类型
    function loadAnalysisTypes() {
        const container = document.getElementById('analysisTypes');
        if (!container) return;

        // 获取分析类型配置
        API.get('/api/system/analysis-types')
            .then(response => {
                if (response.success) {
                    renderAnalysisTypes(response.data);
                } else {
                    console.error('加载分析类型失败:', response.message);
                    // 显示模拟数据
                    renderAnalysisTypes({
                        'futures_account': '期货账户',
                        'wealth_management': '理财产品说明书',
                        'broker_interest': '券商计息变更',
                        'account_opening': '账户开户场景',
                        'ningxia_bank_fee': '宁银理财费用变更',
                        'non_standard_trade': '非标交易确认单'
                    });
                }
            })
            .catch(error => {
                console.error('加载分析类型失败:', error);
                // 显示模拟数据
                renderAnalysisTypes({
                    'futures_account': '期货账户',
                    'wealth_management': '理财产品说明书',
                    'broker_interest': '券商计息变更',
                    'account_opening': '账户开户场景',
                    'ningxia_bank_fee': '宁银理财费用变更',
                    'non_standard_trade': '非标交易确认单'
                });
            });
    }

    // 渲染分析类型卡片
    function renderAnalysisTypes(types) {
        const container = document.getElementById('analysisTypes');
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        // 为每种分析类型创建链接
        Object.entries(types).forEach(([key, label]) => {
            // 设置不同的图标
            let iconClass = 'bi-file-earmark-text';
            let colorStyle = '';

            switch (key) {
                case 'futures_account':
                    iconClass = 'bi-safe';
                    colorStyle = 'background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);';
                    break;
                case 'wealth_management':
                    iconClass = 'bi-cash-coin';
                    colorStyle = 'background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);';
                    break;
                case 'broker_interest':
                    iconClass = 'bi-percent';
                    colorStyle = 'background: linear-gradient(135deg, #10b981 0%, #059669 100%);';
                    break;
                case 'account_opening':
                    iconClass = 'bi-person-plus';
                    colorStyle = 'background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);';
                    break;
                case 'ningxia_bank_fee':
                    iconClass = 'bi-bank';
                    colorStyle = 'background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);';
                    break;
                case 'non_standard_trade':
                    iconClass = 'bi-file-earmark-check';
                    colorStyle = 'background: linear-gradient(135deg, #ec4899 0%, #be185d 100%);';
                    break;
            }

            // 创建分析类型链接
            const typeItem = document.createElement('div');
            typeItem.className = 'col-6';
            
            typeItem.innerHTML = `
                <a href="/document_analysis?type=${key}" class="quick-action-btn p-2 text-center d-flex flex-column align-items-center">
                    <div class="quick-action-icon mb-2" style="${colorStyle} color: white;">
                        <i class="bi ${iconClass}"></i>
                    </div>
                    <div class="small fw-medium">${label}</div>
                </a>
            `;

            container.appendChild(typeItem);
        });
    }

    // 生成模拟的最近活动数据
    function generateMockActivities() {
        const mockActivities = [
            {
                type: 'upload',
                title: '文档上传',
                description: '用户 analyst 上传了期货账户文档',
                time: '2分钟前',
                icon: 'bi-cloud-upload',
                color: '#6366f1'
            },
            {
                type: 'analysis',
                title: '分析完成',
                description: '理财产品文档分析完成，准确率 96.5%',
                time: '5分钟前',
                icon: 'bi-check-circle',
                color: '#10b981'
            },
            {
                type: 'review',
                title: '复核提交',
                description: '券商计息文档复核已提交',
                time: '10分钟前',
                icon: 'bi-clipboard-check',
                color: '#f59e0b'
            },
            {
                type: 'error',
                title: '分析失败',
                description: '非标交易文档分析失败，请检查文件格式',
                time: '15分钟前',
                icon: 'bi-exclamation-triangle',
                color: '#ef4444'
            },
            {
                type: 'config',
                title: '配置更新',
                description: '管理员更新了模型配置参数',
                time: '30分钟前',
                icon: 'bi-gear',
                color: '#06b6d4'
            }
        ];
        return mockActivities;
    }

    // 渲染最近活动
    function renderRecentActivities(activities) {
        const container = document.getElementById('recentActivities');
        if (!container) return;

        // 清空容器
        container.innerHTML = '';

        if (!activities || activities.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4 text-muted">
                    <i class="bi bi-inbox display-4 mb-3"></i>
                    <p class="mb-0">暂无最近活动</p>
                </div>
            `;
            return;
        }

        // 创建活动列表
        const list = document.createElement('div');
        list.className = 'activity-list';

        // 添加活动项
        activities.forEach(activity => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            
            activityItem.innerHTML = `
                <div class="activity-icon" style="background-color: ${activity.color || '#6366f1'}">
                    <i class="bi ${activity.icon || 'bi-circle'}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-desc">${activity.description}</div>
                    <div class="activity-time">${activity.time}</div>
                </div>
            `;
            
            list.appendChild(activityItem);
        });

        container.appendChild(list);
    }
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}文档分析 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}文档分析{% endblock %}

{% block extra_css %}
<style>
    /* 上传区域样式 */
    .upload-section {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .upload-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .upload-header h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }
    
    .upload-header p {
        font-size: 1.125rem;
        opacity: 0.9;
        margin: 0;
    }
    
    .analysis-types {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 2rem;
        background: var(--gray-50);
        max-width: 1200px;
        margin: 0 auto;
        width: 100%;
        box-sizing: border-box;
    }
    
    .analysis-type-card {
        border: 2px solid var(--gray-200);
        border-radius: var(--border-radius-lg);
        padding: 1.5rem;
        cursor: pointer;
        transition: var(--transition);
        text-align: center;
        background: white;
        position: relative;
        overflow: hidden;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .analysis-type-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gray-200);
        transition: var(--transition);
    }
    
    .analysis-type-card:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }
    
    .analysis-type-card:hover::before {
        background: var(--primary-color);
    }
    
    .analysis-type-card.selected {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        box-shadow: var(--shadow-md);
    }
    
    .analysis-type-card.selected::before {
        background: var(--primary-color);
    }
    
    .analysis-type-icon {
        width: 64px;
        height: 64px;
        background: var(--gray-100);
        border-radius: var(--border-radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        font-size: 1.5rem;
        color: var(--gray-600);
        transition: var(--transition);
    }
    
    .analysis-type-card.selected .analysis-type-icon,
    .analysis-type-card:hover .analysis-type-icon {
        background: var(--primary-color);
        color: white;
    }
    
    .analysis-type-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.5rem;
    }
    
    .analysis-type-desc {
        font-size: 0.875rem;
        color: var(--gray-600);
        line-height: 1.5;
    }
    
    .upload-area {
        border: 3px dashed var(--gray-300);
        border-radius: var(--border-radius-lg);
        padding: 4rem 2rem;
        text-align: center;
        transition: var(--transition);
        cursor: pointer;
        background: white;
        margin: 2rem;
        position: relative;
    }
    
    .upload-area:hover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
    }
    
    .upload-area.dragover {
        border-color: var(--primary-color);
        background: var(--primary-bg);
        transform: scale(1.02);
        box-shadow: var(--shadow-lg);
    }
    
    .upload-icon {
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        color: white;
        font-size: 2.5rem;
        box-shadow: var(--shadow-lg);
    }
    
    .upload-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 1rem;
    }
    
    .upload-desc {
        font-size: 1rem;
        color: var(--gray-600);
        margin-bottom: 0.5rem;
    }
    
    .upload-info {
        font-size: 0.875rem;
        color: var(--gray-500);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    
    .file-preview {
        background: white;
        border-radius: var(--border-radius);
        padding: 1rem;
        margin: 2rem;
        border: 1px solid var(--gray-200);
    }
    
    /* 文件列表样式 */
    .file-list-container {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        margin: 2rem 0;
    }

    .file-list-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    }

    .file-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
        position: relative;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item:hover {
        background: var(--gray-50);
    }

    .file-item.analyzed {
        background: #f0f9ff;
        border-left: 4px solid var(--success-color);
    }

    .file-item.deprecated {
        background: #fef2f2;
        opacity: 0.7;
        border-left: 4px solid var(--warning-color);
    }

    .file-item.analyzing {
        background: #fffbeb;
        border-left: 4px solid var(--primary-color);
    }
    
    .file-icon {
        width: 48px;
        height: 48px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        position: relative;
    }

    .file-status-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
        border: 2px solid white;
    }

    .file-status-badge.analyzed {
        background: var(--success-color);
    }

    .file-status-badge.analyzing {
        background: var(--primary-color);
        animation: pulse 2s infinite;
    }

    .file-status-badge.deprecated {
        background: var(--warning-color);
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    .file-info {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }

    .file-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: var(--gray-600);
    }

    /* 文件操作按钮组 - 与文件管理页面一致 */
    .file-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .file-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
        border-radius: 0.25rem;
        min-width: 70px;
        text-align: center;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        white-space: nowrap;
    }

    .file-checkbox {
        margin-right: 1rem;
        transform: scale(1.2);
    }

    .file-checkbox input[type="checkbox"] {
        cursor: pointer;
    }

    /* 废弃文件样式 - 与文件管理页面一致 */
    .file-deprecated {
        background-color: #f8f9fa !important;
    }

    .file-deprecated:hover {
        background-color: #e9ecef !important;
    }

    /* 废弃文件的按钮禁用样式 */
    .file-deprecated .btn:not(.btn-view-result):not(.btn-restore) {
        opacity: 0.5;
        pointer-events: none;
        cursor: not-allowed;
        background: #9ca3af !important;
        border-color: #9ca3af !important;
    }

    /* 废弃文件的复选框禁用样式 */
    .file-deprecated .tech-checkbox {
        opacity: 0.6;
        pointer-events: none;
        cursor: not-allowed;
    }

    /* 按钮颜色样式 - 与文件管理页面一致 */

    /* 分析按钮 - 绿色 */
    .btn-analyze {
        background: #059669;
        border: 1px solid #059669;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-analyze:hover {
        background: #047857;
        border-color: #047857;
        color: white;
    }

    /* 重分析按钮 - 深蓝色 */
    .btn-reanalyze {
        background: #1e40af;
        border: 1px solid #1e40af;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-reanalyze:hover {
        background: #1d4ed8;
        border-color: #1d4ed8;
        color: white;
    }

    /* 查看结果按钮 - 青色 */
    .btn-view-result {
        background: #0891b2;
        border: 1px solid #0891b2;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-view-result:hover {
        background: #0e7490;
        border-color: #0e7490;
        color: white;
    }

    /* 审核按钮 - 紫色 */
    .btn-audit {
        background: #7c3aed;
        border: 1px solid #7c3aed;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-audit:hover {
        background: #6d28d9;
        border-color: #6d28d9;
        color: white;
    }

    /* 废弃按钮 - 橙红色 */
    .btn-deprecate {
        background: #dc2626;
        border: 1px solid #dc2626;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-deprecate:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        color: white;
    }

    /* 恢复按钮 - 天蓝色 */
    .btn-restore {
        background: #0ea5e9;
        border: 1px solid #0ea5e9;
        color: white;
        transition: background-color 0.2s ease;
    }

    .btn-restore:hover {
        background: #0284c7;
        border-color: #0284c7;
        color: white;
    }

    /* 禁用状态的按钮 */
    .btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none !important;
        box-shadow: none !important;
        pointer-events: none;
    }

    /* 禁用状态的查看结果按钮 - 保持原色但变浅 */
    .btn-view-result:disabled {
        background: #0891b2;
        border-color: #0891b2;
        color: white;
        opacity: 0.4;
    }

    /* 禁用状态的分析按钮 - 保持原色但变浅 */
    .btn-analyze:disabled {
        background: #059669;
        border-color: #059669;
        color: white;
        opacity: 0.4;
    }

    /* 禁用状态的重分析按钮 - 保持原色但变浅 */
    .btn-reanalyze:disabled {
        background: #1e40af !important;
        border-color: #1e40af !important;
        color: white !important;
        opacity: 0.4;
    }

    /* 确保重分析按钮的颜色优先级 */
    .file-actions .btn-reanalyze {
        background: #1e40af !important;
        border-color: #1e40af !important;
        color: white !important;
    }

    .file-actions .btn-reanalyze:hover {
        background: #1d4ed8 !important;
        border-color: #1d4ed8 !important;
        color: white !important;
    }

    /* 分析类型标签样式 - 与文件管理页面一致 */
    .analysis-type-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-block;
        text-align: center;
        min-width: 80px;
        white-space: nowrap;
    }

    /* 期货账户 - 深蓝色 */
    .analysis-type-futures_account {
        background: rgba(30, 64, 175, 0.1);
        color: #1e40af;
        border-color: rgba(30, 64, 175, 0.2);
    }

    /* 理财产品 - 青色 */
    .analysis-type-wealth_management {
        background: rgba(6, 182, 212, 0.1);
        color: #0891b2;
        border-color: rgba(6, 182, 212, 0.2);
    }

    /* 券商计息 - 紫色 */
    .analysis-type-broker_interest {
        background: rgba(124, 58, 237, 0.1);
        color: #7c3aed;
        border-color: rgba(124, 58, 237, 0.2);
    }

    /* 账户开户场景 - 绿色 */
    .analysis-type-account_opening {
        background: rgba(5, 150, 105, 0.1);
        color: #059669;
        border-color: rgba(5, 150, 105, 0.2);
    }

    /* 宁银费用 - 橙色 */
    .analysis-type-ningyin_fee {
        background: rgba(234, 88, 12, 0.1);
        color: #ea580c;
        border-color: rgba(234, 88, 12, 0.2);
    }

    /* 非标交易 - 灰色 */
    .analysis-type-non_standard_trade {
        background: rgba(75, 85, 99, 0.1);
        color: #4b5563;
        border-color: rgba(75, 85, 99, 0.2);
    }

    /* 准确率指示器样式 - 与文件管理页面一致 */
    .accuracy-indicator {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .accuracy-bar {
        width: 60px;
        height: 6px;
        background: var(--gray-200);
        border-radius: 3px;
        overflow: hidden;
    }

    .accuracy-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    .accuracy-fill.high {
        background: linear-gradient(90deg, var(--success-color), #10b981);
    }

    .accuracy-fill.medium {
        background: linear-gradient(90deg, var(--warning-color), #f59e0b);
    }

    .accuracy-fill.low {
        background: linear-gradient(90deg, var(--danger-color), #ef4444);
    }

    /* 状态样式 - 与文件管理页面一致 */
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-size: 0.75rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        white-space: nowrap;
    }

    .status-pending {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
    }

    .status-processing {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .status-completed {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
    }

    .status-failed {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .status-uploaded {
        background: rgba(168, 85, 247, 0.1);
        color: #a855f7;
    }

    .status-pending-audit {
        background: rgba(245, 158, 11, 0.1);
        color: #f59e0b;
    }

    .status-pending-review {
        background: rgba(139, 92, 246, 0.1);
        color: #8b5cf6;
    }

    .btn-remove:hover {
        background: #dc2626;
        border-color: #dc2626;
        color: white;
    }

    .btn-reanalyze {
        background: var(--secondary-color);
        color: white;
        border-color: var(--secondary-color);
    }

    .btn-reanalyze:hover {
        background: #4b5563;
        border-color: #4b5563;
        color: white;
    }
    
    .file-pdf {
        background: #fee2e2;
        color: #dc2626;
    }
    
    .file-image {
        background: #dbeafe;
        color: #2563eb;
    }

    /* 分页样式 */
    .file-pagination {
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--gray-200);
        background: var(--gray-50);
        border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pagination-info {
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .pagination-btn {
        padding: 0.375rem 0.75rem;
        border: 1px solid var(--gray-300);
        background: white;
        color: var(--gray-700);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: var(--transition);
        font-size: 0.875rem;
    }

    .pagination-btn:hover:not(:disabled) {
        background: var(--gray-50);
        border-color: var(--gray-400);
    }

    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* 空状态样式 */
    .file-list-empty {
        padding: 3rem 1.5rem;
        text-align: center;
        color: var(--gray-500);
    }

    .file-list-empty i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }
    
    .file-info {
        flex: 1;
        min-width: 0;
    }
    
    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }
    
    .file-size {
        font-size: 0.875rem;
        color: var(--gray-600);
    }
    
    .file-remove {
        width: 32px;
        height: 32px;
        border: none;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition);
    }
    
    .file-remove:hover {
        background: #dc2626;
        transform: scale(1.1);
    }
    
    .progress-container {
        display: none;
        margin: 2rem;
        padding: 2rem;
        background: var(--gray-50);
        border-radius: var(--border-radius-lg);
        border: 1px solid var(--gray-200);
    }
    
    .progress-container.show {
        display: block;
    }
    
    .progress-header {
        display: flex;
        justify-content: between;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .progress-title {
        font-weight: 600;
        color: var(--gray-900);
    }
    
    .progress-percent {
        font-weight: 600;
        color: var(--primary-color);
    }
    
    .action-buttons {
        padding: 2rem;
        text-align: center;
        background: white;
        border-top: 1px solid var(--gray-200);
    }
    
    .btn-analyze {
        background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        border: none;
        border-radius: var(--border-radius-lg);
        padding: 1rem 3rem;
        font-weight: 600;
        font-size: 1.125rem;
        color: white;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }
    
    .btn-analyze::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }
    
    .btn-analyze:hover::before {
        left: 100%;
    }
    
    .btn-analyze:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 24px rgba(16, 185, 129, 0.4);
        color: white;
    }
    
    .btn-analyze:disabled {
        opacity: 0.6;
        transform: none;
        box-shadow: none;
        cursor: not-allowed;
    }

    /* 平板设备适配 */
    @media (max-width: 1024px) and (min-width: 769px) {
        .analysis-types {
            max-width: 900px;
            gap: 1.25rem;
            padding: 1.5rem;
        }

        .analysis-type-card {
            min-height: 160px;
            padding: 1.25rem;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .upload-header {
            padding: 1.5rem;
        }
        
        .upload-header h2 {
            font-size: 1.5rem;
        }
        
        .analysis-types {
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: repeat(3, 1fr);
            padding: 1rem;
            gap: 1rem;
        }

        .analysis-type-card {
            min-height: 140px;
            padding: 1rem;
        }

        .analysis-type-icon {
            width: 48px;
            height: 48px;
            font-size: 1.25rem;
        }

        .analysis-type-title {
            font-size: 1rem;
        }

        .analysis-type-desc {
            font-size: 0.75rem;
        }
    }

    /* 统一文件管理器样式 */
    .unified-file-manager {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .unified-file-list-container {
        border-top: 1px solid var(--gray-200);
    }

    .file-filters-container {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .file-list-content {
        min-height: 300px;
    }

    .file-list-pagination {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-top: 1px solid var(--gray-200);
        display: none; /* 默认隐藏，只在历史记录视图且有多页时显示 */
        justify-content: space-between;
    }

    /* 高亮提醒效果 */
    .highlight-section {
        animation: highlightPulse 2s ease-in-out;
        border-radius: 12px;
    }

    @keyframes highlightPulse {
        0% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
            background-color: rgba(255, 193, 7, 0.1);
        }
        50% {
            box-shadow: 0 0 0 20px rgba(255, 193, 7, 0);
            background-color: rgba(255, 193, 7, 0.2);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
            background-color: transparent;
        }
        
    }

    /* 视图切换按钮样式 */
    .btn-group .btn.active {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
    }

    /* 文件项样式增强 */
    .file-item {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--gray-100);
        transition: var(--transition);
        background: white;
    }

    .file-item:last-child {
        border-bottom: none;
    }

    .file-item:hover {
        background: var(--gray-50);
    }

    .file-item.selected {
        background: var(--primary-bg);
        border-left: 4px solid var(--primary-color);
    }

    .file-checkbox {
        margin-right: 1rem;
    }

    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--border-radius);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.25rem;
        background: var(--gray-100);
        color: var(--gray-600);
    }

    .file-icon.file-pdf {
        background: #fee2e2;
        color: #dc2626;
    }

    .file-icon.file-image {
        background: #dbeafe;
        color: #2563eb;
    }

    .file-info {
        flex: 1;
        min-width: 0;
    }

    .file-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
        word-break: break-all;
    }

    .file-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.875rem;
        color: var(--gray-600);
        flex-wrap: wrap;
    }

    .file-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-left: 1rem;
    }

    /* 状态徽章样式 */
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;
    }

    .status-badge.uploaded {
        background: #e0f2fe;
        color: #0277bd;
    }

    .status-badge.pending {
        background: #fef3c7;
        color: #92400e;
    }

    .status-badge.processing {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-badge.completed {
        background: #d1fae5;
        color: #065f46;
    }

    .status-badge.failed {
        background: #fee2e2;
        color: #991b1b;
    }

    .status-badge.analyzed {
        background: #f3e8ff;
        color: #7c3aed;
    }

    .status-badge.deprecated {
        background: #f3f4f6;
        color: #6b7280;
    }

    /* 文件状态图标样式 */
    .file-status-badge {
        position: absolute;
        top: -5px;
        right: -5px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        color: white;
        z-index: 1;
    }

    .file-status-badge.analyzing {
        background: var(--primary-color);
        animation: spin 1s linear infinite;
    }

    .file-status-badge.analyzed {
        background: var(--success-color);
    }

    .file-status-badge.failed {
        background: var(--danger-color);
    }

    .file-status-badge.deprecated {
        background: var(--gray-500);
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* 文件项状态样式 */
    .file-item.analyzing {
        border-left: 4px solid var(--primary-color);
        background: rgba(var(--primary-rgb), 0.05);
    }

    .file-item.analyzed {
        border-left: 4px solid var(--success-color);
    }

    .file-item.failed {
        border-left: 4px solid var(--danger-color);
        background: rgba(var(--danger-rgb), 0.05);
    }

    .file-item.deprecated {
        opacity: 0.6;
        background: var(--gray-50);
    }

    /* 分析结果弹窗样式 */
    .modal-xl {
        max-width: 95%;
    }

    .modal-header-actions {
        display: flex;
        align-items: center;
    }

    #analysisResultContent {
        min-height: 400px;
    }

    .result-content {
        padding: 1.5rem;
    }

    .result-section {
        margin-bottom: 2rem;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        overflow: hidden;
    }

    .result-section-header {
        background: var(--gray-50);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
        font-weight: 600;
        color: var(--text-color);
    }

    .result-section-body {
        padding: 1.5rem;
    }

    .result-field {
        display: flex;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--gray-200);
    }

    .result-field:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        border-bottom: none;
    }

    .result-field-label {
        font-weight: 600;
        color: var(--text-secondary);
        min-width: 120px;
        margin-right: 1rem;
    }

    .result-field-value {
        flex: 1;
        color: var(--text-color);
        word-break: break-word;
    }

    .result-status {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .result-status.success {
        background: var(--success-light);
        color: var(--success-color);
    }

    .result-status.error {
        background: var(--danger-light);
        color: var(--danger-color);
    }

    .result-status.warning {
        background: var(--warning-light);
        color: var(--warning-color);
    }

    .result-json {
        background: var(--gray-50);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-word;
    }

    .result-error {
        background: var(--danger-light);
        border: 1px solid var(--danger-color);
        border-radius: 6px;
        padding: 1rem;
        color: var(--danger-color);
        text-align: center;
    }

    .result-loading {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    /* 预期结果编辑样式 */
    .editable-expected-result {
        background: var(--gray-50);
        border: 1px solid var(--border-color);
        border-radius: 6px;
        padding: 1rem;
    }

    .editable-input {
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .editable-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
        outline: 0;
    }

    .editable-field {
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: background-color 0.15s ease-in-out;
    }

    .editable-field:hover {
        background: var(--gray-100);
    }

    .result-section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .result-section-header button {
        margin-left: auto;
    }

    /* 文件管理栏分析按钮样式 */
    .file-list-header .btn-group + .d-flex {
        margin-left: 1rem;
    }

    .file-list-header .vr {
        height: 1.5rem;
        opacity: 0.3;
    }

    #analyzeBtn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    #analyzeBtn.btn-success {
        background: linear-gradient(135deg, #28a745, #20c997);
        border: none;
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        transition: all 0.2s ease;
    }

    #analyzeBtn.btn-success:hover:not(:disabled) {
        background: linear-gradient(135deg, #218838, #1ea085);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
        transform: translateY(-1px);
    }

    #analyzeBtn.btn-success:active:not(:disabled) {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
    }

    /* 平滑滚动效果 */
    html {
        scroll-behavior: smooth;
    }

    /* 上传区域高亮动画 */
    .upload-area.highlight {
        animation: uploadHighlight 0.6s ease-out;
    }

    @keyframes uploadHighlight {
        0% {
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        50% {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
    }

    /* 自动分析开关样式 */
    #autoAnalysisSwitch {
        cursor: pointer;
    }

    #autoAnalysisSwitch:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    #autoAnalysisSwitch + label {
        cursor: pointer;
        transition: color 0.2s ease;
        user-select: none;
    }

    #autoAnalysisSwitch:checked + label {
        color: #28a745 !important;
        font-weight: 500;
    }

    /* 全选复选框样式 */
    #selectAllCheckbox {
        transform: scale(1.2);
        margin-right: 0.5rem;
    }

    #selectAllCheckbox + label {
        cursor: pointer;
        user-select: none;
        margin-bottom: 0;
    }

    /* 文件项复选框对齐 */
    .file-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
    }

    .file-item .file-checkbox {
        transform: scale(1.2);
        margin-right: 1rem;
        flex-shrink: 0;
    }

    /* 自动分析开关样式 */
    #autoAnalysisSwitch {
        cursor: pointer;
    }

    #autoAnalysisSwitch:checked {
        background-color: #28a745;
        border-color: #28a745;
    }

    #autoAnalysisSwitch + label {
        cursor: pointer;
        transition: color 0.2s ease;
        user-select: none;
    }

    #autoAnalysisSwitch:checked + label {
        color: #28a745 !important;
        font-weight: 500;
    }

    /* 自动分析消息样式 */
    .auto-analysis-message {
        background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
        border-left: 4px solid #2196f3;
        padding: 12px 16px;
        border-radius: 8px;
        margin: 8px 0;
    }

    .auto-analysis-message .bi {
        color: #2196f3;
    }

    /* 超小屏幕 */
    @media (max-width: 480px) {
        .analysis-types {
            grid-template-columns: 1fr;
            grid-template-rows: repeat(6, 1fr);
            padding: 0.5rem;
            gap: 0.75rem;
        }

        .analysis-type-card {
            min-height: 120px;
            padding: 0.75rem;
        }

        .upload-area {
            padding: 2rem 1rem;
            margin: 1rem;
        }

        .upload-icon {
            width: 80px;
            height: 80px;
            font-size: 2rem;
        }

        .upload-title {
            font-size: 1.25rem;
        }

        .action-buttons {
            padding: 1.5rem;
        }

        .btn-analyze {
            padding: 0.875rem 2rem;
            font-size: 1rem;
        }

        .history-record-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .history-record-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.25rem;
        }

        .history-record-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<!-- 文档分析主区域 -->
<div class="upload-section">
    <!-- 页面头部 -->
    <div class="upload-header">
        <h2>智能文档分析</h2>
        <p>选择分析类型，上传您的文档，让AI为您智能识别和分析</p>
    </div>
    
    <!-- 分析类型选择 -->
    <div class="analysis-types" id="analysisTypes">
        <!-- 动态生成分析类型选项 -->
    </div>

    <!-- 统一文件管理区域 -->
    <div class="unified-file-manager" id="fileManagementArea">
        <!-- 文件上传区域 -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">
                <i class="bi bi-cloud-upload"></i>
            </div>
            <h3 class="upload-title">拖拽文件到此处或点击选择文件</h3>
            <p class="upload-desc">支持 PDF、PNG、JPG、JPEG、GIF、BMP、TIFF 格式</p>
            <p class="upload-info">
                <i class="bi bi-info-circle"></i>
                最大文件大小：50MB，支持批量上传
            </p>
            <input type="file" id="fileInput" class="d-none" accept=".pdf,.png,.jpg,.jpeg,.gif,.bmp,.tiff" multiple>
        </div>

        <!-- 统一文件列表区域 -->
        <div class="unified-file-list-container">
            <!-- 文件列表头部 -->
            <div class="file-list-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-files me-2"></i>
                        文件管理列表
                        <span class="badge bg-primary ms-2" id="totalFileCountBadge">0</span>
                    </h5>
                    <div class="d-flex align-items-center gap-2">
                        <!-- 视图切换按钮 -->
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-secondary active" id="currentFilesTab" onclick="switchFileView('current')">
                                <i class="bi bi-upload me-1"></i>当前上传 (<span id="currentFileCount">0</span>)
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="historyFilesTab" onclick="switchFileView('history')">
                                <i class="bi bi-clock-history me-1"></i>历史记录 (<span id="historyFileCount">0</span>)
                            </button>
                        </div>
                        <!-- 操作按钮 -->
                        <div class="d-flex align-items-center gap-1">
                            <!-- 自动分析开关 -->
                            <div class="form-check form-switch me-2">
                                <input class="form-check-input" type="checkbox" id="autoAnalysisSwitch" checked title="开启后，单文件上传时自动开始分析">
                                <label class="form-check-label text-muted" for="autoAnalysisSwitch" style="font-size: 0.875rem;">
                                    自动分析
                                </label>
                            </div>
                            <div class="vr mx-1"></div>
                            <!-- 开始智能分析按钮 -->
                            <button class="btn btn-sm btn-success" id="analyzeBtn" onclick="startAnalysis()" disabled title="开始智能分析">
                                <i class="bi bi-cpu me-1"></i>开始分析
                            </button>
                            <div class="vr mx-1"></div>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshFileList()" title="刷新列表">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>

                            <!-- 全选复选框 -->
                            <div class="form-check me-2">
                                <input type="checkbox" class="form-check-input" id="selectAllCheckbox" onchange="toggleSelectAll()" title="全选/取消全选">
                                <label class="form-check-label text-muted" for="selectAllCheckbox" style="font-size: 0.875rem;">
                                    全选
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选控件区域 -->
            <div class="file-filters-container" id="fileFiltersContainer">
                <div class="row g-2">
                    <div class="col-md-3">
                        <select class="form-select form-select-sm" id="fileTypeFilter" onchange="filterFiles()">
                            <option value="">全部类型</option>
                            <option value="futures_account">期货账户</option>
                            <option value="wealth_management">理财产品</option>
                            <option value="broker_interest">券商计息</option>
                            <option value="account_opening">账户开户场景</option>
                            <option value="ningyin_fee">宁银费用</option>
                            <option value="non_standard_trade">非标交易</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select form-select-sm" id="fileStatusFilter" onchange="filterFiles()">
                            <option value="">全部状态</option>
                            <option value="pending">待处理</option>
                            <option value="processing">处理中</option>
                            <option value="analyzing">分析中</option>
                            <option value="completed">已完成</option>
                            <option value="failed">失败</option>
                            <option value="uploaded">已上传</option>
                            <option value="pending_audit">待审核</option>
                            <option value="pending_review">待复核</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control form-control-sm" id="fileSearchInput"
                               placeholder="搜索文件名..." onkeyup="searchFiles()">
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearFileFilters()">
                            <i class="bi bi-x-circle me-1"></i>清除
                        </button>
                    </div>
                </div>
            </div>

            <!-- 文件列表内容 -->
            <div class="file-list-content">
                <!-- 当前上传文件列表 -->
                <div id="currentFilesList" class="file-list">
                    <div class="table-responsive">
                        <table class="table table-hover file-table">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="tech-checkbox" id="selectAllCurrent" onchange="toggleSelectAllCurrent(this.checked)">
                                    </th>
                                    <th>文件名</th>
                                    <th width="120">分析类型</th>
                                    <th width="100">状态</th>
                                    <th width="100">准确率</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="currentFilesTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <i class="bi bi-inbox display-4 text-muted"></i>
                                        <div class="mt-3 text-muted">暂无上传文件</div>
                                        <div class="text-muted">请先选择分析类型并上传文件</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 历史记录文件列表 -->
                <div id="historyFilesList" class="file-list" style="display: none;">
                    <div class="table-responsive">
                        <table class="table table-hover file-table">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="tech-checkbox" id="selectAllHistory" onchange="toggleSelectAllHistory(this.checked)">
                                    </th>
                                    <th>文件名</th>
                                    <th width="120">分析类型</th>
                                    <th width="100">状态</th>
                                    <th width="100">准确率</th>
                                    <th width="150">创建时间</th>
                                    <th width="200">操作</th>
                                </tr>
                            </thead>
                            <tbody id="historyFilesTableBody">
                                <tr>
                                    <td colspan="7" class="text-center py-5">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                        <div class="mt-2 text-muted">正在加载历史记录...</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 分页控件 -->
            <div class="file-list-pagination" id="filePagination">
                <!-- 动态生成分页控件 -->
            </div>
        </div>
    </div>

        <!-- 进度条 -->
        <div class="progress-container" id="progressContainer" style="display: none;">
            <div class="progress-header">
                <span class="progress-title">上传进度</span>
                <span class="progress-percent" id="progressPercent">0%</span>
            </div>
            <div class="progress mb-3">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
            </div>
            <div class="text-center">
                <small class="text-muted" id="progressText">准备上传...</small>
            </div>
        </div>


    </div>
</div>


<!-- 使用说明 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-question-circle me-2"></i>
                    使用说明
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>支持的文档类型</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success me-2"></i>期货账户相关文档</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>理财产品说明书</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>券商计息变更文档</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>期货交易会员文档</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>宁夏银行费用变更</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>非标交易确认单</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>使用步骤</h6>
                        <ol class="list-unstyled">
                            <li><span class="badge bg-primary me-2">1</span>选择对应的分析类型</li>
                            <li><span class="badge bg-primary me-2">2</span>上传需要分析的文档</li>
                            <li><span class="badge bg-primary me-2">3</span>在文件管理栏点击"开始分析"</li>
                            <li><span class="badge bg-primary me-2">4</span>等待AI分析完成</li>
                            <li><span class="badge bg-primary me-2">5</span>查看分析结果和准确率</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let selectedAnalysisType = '';
    let fileIdCounter = 0;

    // 按分析类型分组的文件管理
    let filesByType = {}; // 按分析类型分组存储文件 { 'futures_account': [], 'wealth_management': [], ... }
    let fileRecordsByType = {}; // 按分析类型分组存储文件记录 { 'futures_account': [], 'wealth_management': [], ... }

    // 统一文件管理相关变量
    let currentFileView = 'current'; // 'current' 或 'history'
    let historyFiles = []; // 历史记录文件
    let filteredFiles = []; // 筛选后的文件
    let fileFilters = {
        type: '',
        status: '',
        search: ''
    };
    let filePagination = {
        currentPage: 1,
        itemsPerPage: 20,
        totalItems: 0,
        totalPages: 0
    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initializePage();
        loadAnalysisTypes();
        setupEventListeners();

        // 从URL参数中获取预选的分析类型
        const urlParams = new URLSearchParams(window.location.search);
        const typeFromUrl = urlParams.get('type');
        
        // 如果URL中有分析类型参数，自动选择该类型
        if (typeFromUrl) {
            setTimeout(() => {
                const typeCard = document.querySelector(`.analysis-type-card[data-type="${typeFromUrl}"]`);
                if (typeCard) {
                    selectAnalysisType(typeFromUrl, typeCard);
                }
            }, 300); // 延迟一点时间确保页面加载完成
        }
    });

    // 初始化页面
    function initializePage() {
        // 添加页面加载动画
        document.body.classList.add('fade-in');

        // 初始化统一文件管理
        initializeUnifiedFileManager();
    }



    // ==================== 统一文件管理功能 ====================

    // 初始化统一文件管理器
    function initializeUnifiedFileManager() {
        // 初始化各分析类型的文件存储
        initializeFileStorageByType();

        // 显示统一文件列表容器
        const container = document.querySelector('.unified-file-list-container');
        if (container) {
            container.style.display = 'block';
        }

        // 初始化分页控件状态
        hidePagination();

        // 更新文件计数
        updateFileCountBadges();

        // 默认显示当前上传视图
        switchFileView('current');

        // 初始化分析按钮状态
        checkAnalyzeButton();
    }

    // 初始化各分析类型的文件存储
    function initializeFileStorageByType() {
        const analysisTypes = [
            'futures_account',
            'wealth_management',
            'broker_interest',
            'account_opening',
            'ningyin_fee',
            'non_standard_trade'
        ];

        analysisTypes.forEach(type => {
            if (!filesByType[type]) {
                filesByType[type] = [];
            }
            if (!fileRecordsByType[type]) {
                fileRecordsByType[type] = [];
            }
        });
    }

    // 切换文件视图（当前上传/历史记录）
    function switchFileView(view) {
        currentFileView = view;

        // 更新标签页状态
        const currentTab = document.getElementById('currentFilesTab');
        const historyTab = document.getElementById('historyFilesTab');
        const currentList = document.getElementById('currentFilesList');
        const historyList = document.getElementById('historyFilesList');

        if (view === 'current') {
            currentTab.classList.add('active');
            historyTab.classList.remove('active');
            currentList.style.display = 'block';
            historyList.style.display = 'none';

            // 显示当前上传的文件
            displayCurrentFiles();
            // 当前文件视图不需要分页
            hidePagination();
        } else {
            currentTab.classList.remove('active');
            historyTab.classList.add('active');
            currentList.style.display = 'none';
            historyList.style.display = 'block';

            // 加载并显示历史记录
            loadHistoryFiles().then(() => {
                // 显示分页
                showPagination();
            }).catch(error => {
                // 历史记录加载失败时的处理
            });
        }

        // 更新筛选器可见性
        updateFiltersVisibility();
    }

    // 更新筛选器可见性
    function updateFiltersVisibility() {
        const filtersContainer = document.getElementById('fileFiltersContainer');
        if (filtersContainer) {
            // 历史记录视图显示筛选器，当前上传视图隐藏筛选器
            filtersContainer.style.display = currentFileView === 'history' ? 'block' : 'none';
        }
    }

    // 更新文件计数徽章
    function updateFileCountBadges() {
        const totalBadge = document.getElementById('totalFileCountBadge');
        const currentBadge = document.getElementById('currentFileCount');
        const historyBadge = document.getElementById('historyFileCount');

        // 计算当前选择类型的文件数量
        const currentCount = getCurrentTypeFiles().length;
        const historyCount = historyFiles.length;
        const totalCount = currentCount + historyCount;

        if (totalBadge) totalBadge.textContent = totalCount;
        if (currentBadge) currentBadge.textContent = currentCount;
        if (historyBadge) historyBadge.textContent = historyCount;
    }

    // 显示当前上传的文件
    function displayCurrentFiles() {
        const tbody = document.getElementById('currentFilesTableBody');
        if (!tbody) return;

        // 获取当前选择类型的文件
        const currentTypeFiles = getCurrentTypeFiles();

        // 筛选文件
        filteredFiles = currentTypeFiles.filter(file => {
            let matches = true;

            // 按状态筛选
            if (fileFilters.status && file.status !== fileFilters.status) {
                matches = false;
            }

            // 按搜索关键词筛选
            if (fileFilters.search && !file.name.toLowerCase().includes(fileFilters.search.toLowerCase())) {
                matches = false;
            }

            return matches;
        });

        if (filteredFiles.length === 0) {
            const emptyMessage = selectedAnalysisType ?
                `暂无 ${getTypeDisplayName(selectedAnalysisType)} 类型的文件` :
                '暂无上传文件';
            const emptyHint = selectedAnalysisType ?
                '请上传对应类型的文件' :
                '请先选择分析类型并上传文件';

            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <div class="mt-3 text-muted">${emptyMessage}</div>
                        <div class="text-muted">${emptyHint}</div>
                    </td>
                </tr>
            `;

            // 更新全选复选框状态
            updateSelectAllCheckbox();
            // 隐藏分页控件
            hidePagination();
            return;
        }

        tbody.innerHTML = '';
        filteredFiles.forEach(file => {
            const row = createFileItemHTML(file, 'current');
            tbody.appendChild(row);
        });

        // 更新全选复选框状态
        updateSelectAllCheckbox();
        // 隐藏分页控件（当前文件不需要分页）
        hidePagination();
    }

    // 获取当前选择类型的文件
    function getCurrentTypeFiles() {
        if (!selectedAnalysisType || !filesByType[selectedAnalysisType]) {
            return [];
        }
        return filesByType[selectedAnalysisType];
    }

    // 显示分析类型选择提醒模态框
    function showAnalysisTypeRequiredModal(files) {
        const fileCountText = document.getElementById('fileCountText');
        if (fileCountText) {
            fileCountText.textContent = files.length;
        }

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('analysisTypeRequiredModal'));
        modal.show();

        // 临时存储文件，等用户选择分析类型后再处理
        window.pendingFiles = files;
    }

    // 滚动到分析类型选择区域
    function scrollToAnalysisTypes() {
        const analysisTypesSection = document.getElementById('analysisTypes');
        if (analysisTypesSection) {
            analysisTypesSection.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 添加高亮效果
            analysisTypesSection.classList.add('highlight-section');
            setTimeout(() => {
                analysisTypesSection.classList.remove('highlight-section');
            }, 3000);
        }
    }

    // 获取类型显示名称
    function getTypeDisplayName(typeId) {
        const typeNames = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '账户开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeNames[typeId] || typeId;
    }

    // 加载历史记录文件
    function loadHistoryFiles() {
        const container = document.getElementById('historyFilesList');
        if (!container) return Promise.resolve();

        // 显示加载状态 - 只更新表格体，不替换整个容器
        const tbody = document.getElementById('historyFilesTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2 text-muted">正在加载历史记录...</div>
                    </td>
                </tr>
            `;
        }

        // 构建查询参数
        const params = {
            page: filePagination.currentPage,
            per_page: filePagination.itemsPerPage
        };

        // 如果选择了分析类型，则只显示该类型的历史记录
        // 如果未选择分析类型，则显示所有类型的历史记录
        if (selectedAnalysisType && selectedAnalysisType !== '') {
            params.analysis_type = selectedAnalysisType;
        }

        if (fileFilters.status) {
            params.status = fileFilters.status;
        }
        if (fileFilters.search) {
            params.search = fileFilters.search;
        }

        // 调用API - 历史记录使用records端点
        return API.get('/api/records', params)
            .then(response => {
                if (response && response.success && response.data) {
                    historyFiles = response.data.records || [];
                    filePagination.totalItems = response.data.pagination?.total || 0;
                    filePagination.totalPages = response.data.pagination?.pages || 0;

                    displayHistoryFiles();
                    updateFilePagination(response.data.pagination);
                    updateFileCountBadges();




                    return historyFiles;
                } else {
                    showFileError('获取历史记录失败: ' + (response?.message || '服务器响应异常'));
                    return [];
                }
            })
            .catch(error => {
                console.error('加载历史记录失败:', error);
                let errorMessage = '网络错误，请重试';

                if (error.response) {
                    const status = error.response.status;
                    const data = error.response.data;

                    if (status === 401) {
                        errorMessage = '登录已过期，请重新登录';
                        // 可以考虑重定向到登录页面
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                    } else if (status === 403) {
                        errorMessage = '权限不足，无法查看历史记录';
                    } else if (status === 500) {
                        errorMessage = '服务器内部错误，请稍后重试';
                    } else {
                        errorMessage = `请求失败 (${status}): ${data?.message || error.message}`;
                    }
                }

                showFileError(errorMessage);
                return [];
            });
    }

    // 显示历史记录文件
    function displayHistoryFiles() {
        const tbody = document.getElementById('historyFilesTableBody');

        if (!tbody) {
            return;
        }

        if (historyFiles.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center py-5">
                        <i class="bi bi-folder2-open display-4 text-muted"></i>
                        <div class="mt-3 text-muted">暂无历史记录</div>
                        <div class="text-muted">上传文件后将在这里显示记录</div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = '';
        historyFiles.forEach((file, index) => {
            const row = createFileItemHTML(file, 'history');
            tbody.appendChild(row);
        });
    }

    // 创建文件项HTML - 与文件管理页面完全一致
    function createFileItemHTML(file, source) {
        const isHistory = source === 'history';
        const fileId = isHistory ? file.id : file.fileId;
        const fileName = isHistory ? file.filename : file.name;
        const fileSize = isHistory ? (file.file_size || 0) : file.size;
        const createdAt = isHistory ? new Date(file.created_at).toLocaleString('zh-CN') : new Date(file.uploadTime).toLocaleString('zh-CN');
        const analysisType = isHistory ? file.analysis_type : selectedAnalysisType;
        const status = isHistory ? file.status : (file.status || 'uploaded');
        const fileStatus = isHistory ? file.file_status : (file.file_status || 'active'); // 文件状态（active/deprecated）

        const typeText = getAnalysisTypeText(analysisType);
        const statusBadge = getCurrentFileStatusBadge(status);
        const iconClass = getFileIconClass(fileName);
        const formattedSize = formatFileSize(fileSize);

        // 文件操作按钮逻辑 - 修复逻辑
        const isDeprecated = fileStatus === 'deprecated';
        // 可以分析的条件：文件未废弃 且 状态允许分析
        const canAnalyze = !isDeprecated && !['pending_audit', 'pending_review'].includes(status);
        // 有结果的条件：状态表示已完成分析
        const hasResult = ['completed', 'analyzed', 'pending_audit', 'pending_review'].includes(status);

        // 审核按钮逻辑
        const canAudit = status === 'pending_audit' && !isDeprecated;
        const canReview = status === 'pending_review' && !isDeprecated;

        // 操作按钮 - 修复变量作用域问题
        const actionButtons = `
            <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                    onclick="analyzeFile('${fileId}')"
                    ${canAnalyze ? '' : 'disabled'}>
                <i class="bi bi-cpu"></i>
                ${hasResult ? '重分析' : '分析'}
            </button>
            <button class="btn btn-view-result btn-sm"
                    onclick="viewResult('${fileId}')"
                    ${hasResult ? '' : 'disabled'}>
                <i class="bi bi-eye"></i>
                查看结果
            </button>
            ${canAudit ? `
                <button class="btn btn-audit btn-sm"
                        onclick="auditFile('${fileId}', 'pass')">
                    <i class="bi bi-check-circle"></i>
                    审核
                </button>
            ` : ''}
            ${!isDeprecated ? `
                <button class="btn btn-deprecate btn-sm"
                        onclick="deprecateFile('${fileId}')">
                    <i class="bi bi-archive"></i>
                    废弃
                </button>
            ` : `
                <button class="btn btn-restore btn-sm"
                        onclick="restoreFile('${fileId}')">
                    <i class="bi bi-arrow-counterclockwise"></i>
                    恢复
                </button>
            `}
        `;

        // 创建表格行 - 与文件管理页面完全一致
        const tr = document.createElement('tr');
        tr.dataset.fileId = fileId;
        tr.dataset.source = source;

        // 如果文件已废弃，添加样式
        if (fileStatus === 'deprecated') {
            tr.classList.add('file-deprecated');
        }

        const statusClass = getStatusClass(status);
        const statusText = getStatusText(status);
        const analysisTypeName = getAnalysisTypeText(analysisType);
        const accuracyScore = isHistory && file.accuracy_score ? (file.accuracy_score * 100).toFixed(1) : '0.0';
        const accuracyClass = getAccuracyClass(isHistory ? file.accuracy_score : 0);

        tr.innerHTML = `
            <td>
                <input type="checkbox" class="tech-checkbox file-checkbox"
                       ${canAnalyze ? '' : 'disabled'}
                       data-file-id="${fileId}"
                       ${isHistory ? '' : (file.selected ? 'checked' : '')}
                       onchange="handleFileSelection(this)">
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="bi bi-file-earmark-text me-2 text-warning"></i>
                    <span title="${fileName}">${truncateText(fileName, 30)}</span>
                </div>
            </td>
            <td>
                <span class="analysis-type-badge analysis-type-${analysisType}">
                    ${analysisTypeName}
                </span>
            </td>
            <td>
                <span class="status-badge ${statusClass}">
                    ${getStatusIcon(status)}
                    ${statusText}
                </span>
            </td>
            <td>
                <div class="accuracy-indicator">
                    <span class="small">${accuracyScore}%</span>
                    <div class="accuracy-bar">
                        <div class="accuracy-fill ${accuracyClass}"
                             style="width: ${accuracyScore}%"></div>
                    </div>
                </div>
            </td>
            <td>
                <span class="small text-muted">${formatDateTime(createdAt)}</span>
            </td>
            <td>
                <div class="file-actions">
                    ${actionButtons}
                </div>
            </td>
        `;

        return tr;
    }



    // 获取状态徽章（历史记录用）
    function getStatusBadge(status) {
        const badges = {
            'uploaded': '<span class="status-badge uploaded">已上传</span>',
            'pending': '<span class="status-badge pending">待处理</span>',
            'processing': '<span class="status-badge processing">处理中</span>',
            'completed': '<span class="status-badge completed">已完成</span>',
            'failed': '<span class="status-badge failed">失败</span>',
            'analyzed': '<span class="status-badge analyzed">已分析</span>'
        };
        return badges[status] || `<span class="status-badge">${status}</span>`;
    }

    // 获取当前文件状态徽章 - 与文件管理页面一致
    function getCurrentFileStatusBadge(status) {
        const badges = {
            'uploaded': '<span class="status-badge uploaded">已上传</span>',
            'pending': '<span class="status-badge pending">待处理</span>',
            'processing': '<span class="status-badge processing">处理中</span>',
            'analyzing': '<span class="status-badge processing">分析中</span>',
            'completed': '<span class="status-badge completed">已完成</span>',
            'failed': '<span class="status-badge failed">失败</span>',
            'pending_audit': '<span class="status-badge pending-audit">待审核</span>',
            'pending_review': '<span class="status-badge pending-review">待复核</span>',
            'deprecated': '<span class="status-badge deprecated">已废弃</span>'
        };
        return badges[status] || `<span class="status-badge">${status}</span>`;
    }

    // 获取当前文件状态图标
    function getCurrentFileStatusIcon(status) {
        const icons = {
            'pending': '',
            'analyzing': '<div class="file-status-badge analyzing"><i class="bi bi-arrow-clockwise"></i></div>',
            'analyzed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'completed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'failed': '<div class="file-status-badge failed"><i class="bi bi-x"></i></div>',
            'deprecated': '<div class="file-status-badge deprecated"><i class="bi bi-archive"></i></div>'
        };
        return icons[status] || '';
    }

    // 获取当前文件状态文本
    function getCurrentFileStatusText(status) {
        const texts = {
            'uploaded': '已上传',
            'pending': '待分析',
            'analyzing': '分析中...',
            'analyzed': '已分析',
            'completed': '已完成',
            'failed': '分析失败',
            'deprecated': '已废弃'
        };
        return texts[status] || '未知状态';
    }

    // 获取文件图标类
    function getFileIconClass(filename) {
        if (!filename) return 'file-image';
        const ext = filename.toLowerCase().split('.').pop();
        if (ext === 'pdf') {
            return 'file-pdf';
        }
        return 'file-image';
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (!bytes || bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 截断文件名
    function truncateFilename(filename, maxLength) {
        if (!filename || filename.length <= maxLength) return filename || '';
        const ext = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - ext.length - 4) + '...';
        return truncatedName + '.' + ext;
    }

    // 刷新文件列表
    function refreshFileList() {
        if (currentFileView === 'current') {
            displayCurrentFiles();
        } else {
            loadHistoryFiles();
        }
    }



    // 筛选文件
    function filterFiles() {
        const typeFilter = document.getElementById('fileTypeFilter');
        const statusFilter = document.getElementById('fileStatusFilter');

        if (currentFileView === 'history') {
            if (typeFilter) {
                const newType = typeFilter.value;
                fileFilters.type = newType;

                // 反向同步到分析类型卡片选择
                if (newType && newType !== selectedAnalysisType) {
                    syncAnalysisTypeFromFilter(newType);
                }
            }
            if (statusFilter) {
                fileFilters.status = statusFilter.value;
            }
            filePagination.currentPage = 1; // 重置到第一页
            loadHistoryFiles();
        } else if (currentFileView === 'current') {
            // 当前上传文件也支持类型筛选
            if (typeFilter) {
                const newType = typeFilter.value;
                fileFilters.type = newType;

                // 反向同步到分析类型卡片选择
                if (newType && newType !== selectedAnalysisType) {
                    syncAnalysisTypeFromFilter(newType);
                }
            }
            displayCurrentFiles();
        }
    }

    // 从文件过滤器同步分析类型选择到分析类型卡片
    function syncAnalysisTypeFromFilter(typeId) {
        if (!typeId) return;



        // 移除其他选中状态
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 选中对应的分析类型卡片
        const typeCard = document.querySelector(`.analysis-type-card[data-type="${typeId}"]`);
        if (typeCard) {
            typeCard.classList.add('selected');
            selectedAnalysisType = typeId;

            // 更新隐藏的input
            let analysisTypeInput = document.getElementById('analysisType');
            if (!analysisTypeInput) {
                analysisTypeInput = document.createElement('input');
                analysisTypeInput.type = 'hidden';
                analysisTypeInput.id = 'analysisType';
                document.body.appendChild(analysisTypeInput);
            }
            analysisTypeInput.value = typeId;


        }
    }

    // 搜索文件
    function searchFiles() {
        if (currentFileView === 'history') {
            fileFilters.search = document.getElementById('fileSearchInput').value.trim();
            filePagination.currentPage = 1; // 重置到第一页
            // 防抖处理
            clearTimeout(window.fileSearchTimeout);
            window.fileSearchTimeout = setTimeout(() => {
                loadHistoryFiles();
            }, 500);
        }
    }

    // 清除文件筛选
    function clearFileFilters() {
        document.getElementById('fileTypeFilter').value = '';
        document.getElementById('fileStatusFilter').value = '';
        document.getElementById('fileSearchInput').value = '';
        fileFilters = { type: '', status: '', search: '' };
        filePagination.currentPage = 1;
        if (currentFileView === 'history') {
            loadHistoryFiles();
        }
    }

    // 文件操作函数
    function toggleFileSelection(fileId) {
        const checkbox = document.querySelector(`[data-file-id="${fileId}"] .file-checkbox`);
        const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);

        if (checkbox && fileItem) {
            if (checkbox.checked) {
                fileItem.classList.add('selected');
                if (!selectedFiles.includes(fileId)) {
                    selectedFiles.push(fileId);
                }
            } else {
                fileItem.classList.remove('selected');
                const index = selectedFiles.indexOf(fileId);
                if (index > -1) {
                    selectedFiles.splice(index, 1);
                }
            }
        }

        updateSelectionUI();
    }

    // 切换当前文件选择状态
    function toggleCurrentFileSelection(fileId, isSelected) {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const record = currentTypeFiles.find(f => f.fileId === fileId);
        if (record && record.status !== 'deprecated') {
            record.selected = isSelected;

            // 同步更新 fileRecords
            const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
            const fileRecord = fileRecords.find(r => r.fileId === fileId);
            if (fileRecord) {
                fileRecord.selected = isSelected;
            }

            updateSelectionUI();
            updateSelectAllCheckbox();
            checkAnalyzeButton();
        }
    }

    // 分析当前文件
    function analyzeCurrentFile(fileId) {
        if (!selectedAnalysisType) {
            Utils.showMessage('请先选择分析类型', 'warning');
            return;
        }

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        const record = currentTypeFiles.find(f => f.fileId === fileId);
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (!record || !fileRecord) {
            Utils.showMessage('文件不存在', 'warning');
            return;
        }

        // 更新状态为分析中
        record.status = 'analyzing';
        fileRecord.status = 'analyzing';

        // 刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('analysis_type', selectedAnalysisType);
        formData.append('files', fileRecord.file);

        // 发送分析请求
        fetch('/api/analysis/start', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                record.status = 'analyzed';
                fileRecord.status = 'analyzed';
                fileRecord.recordId = data.record_ids[0];
                showMessage(`文件 ${record.name} 分析完成`, 'success');
            } else {
                record.status = 'failed';
                fileRecord.status = 'failed';
                showMessage(`文件 ${record.name} 分析失败: ${data.message}`, 'error');
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        })
        .catch(error => {
            record.status = 'failed';
            fileRecord.status = 'failed';
            showMessage(`文件 ${record.name} 分析失败`, 'error');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        });
    }

    // 重新分析当前文件
    function reanalyzeCurrentFile(fileId) {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = filesByType[selectedAnalysisType] || [];
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        const record = currentTypeFiles.find(f => f.fileId === fileId);
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (record && fileRecord) {
            record.status = 'pending';
            fileRecord.status = 'pending';
            fileRecord.analysisResult = null;
            fileRecord.recordId = null;

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            // 开始分析
            analyzeCurrentFile(fileId);
        }
    }

    // 查看当前文件结果
    function viewCurrentFileResult(fileId) {
        if (!selectedAnalysisType) return;

        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);
        if (fileRecord && fileRecord.recordId) {
            showAnalysisResultModal(fileRecord.recordId);
        } else {
            showMessage('暂无分析结果', 'warning');
        }
    }

    // 废弃当前文件
    function deprecateCurrentFile(fileId) {
        if (confirm('确定要废弃这个文件吗？')) {
            const record = currentFiles.find(f => f.fileId === fileId);
            const fileRecord = fileRecords.find(r => r.fileId === fileId);

            if (record && fileRecord) {
                record.status = 'deprecated';
                fileRecord.status = 'deprecated';
                record.selected = false;
                fileRecord.selected = false;

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                updateSelectionUI();
                checkAnalyzeButton();
                showMessage(`文件 ${record.name} 已废弃`, 'info');
            }
        }
    }

    // 恢复当前文件
    function restoreCurrentFile(fileId) {
        const record = currentFiles.find(f => f.fileId === fileId);
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (record && fileRecord) {
            record.status = 'uploaded';
            fileRecord.status = 'pending';

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            showMessage(`文件 ${record.name} 已恢复`, 'success');
        }
    }

    // 移除当前文件
    function removeCurrentFile(fileId) {
        if (confirm('确定要移除这个文件吗？')) {
            // 从当前文件列表中移除
            currentFiles = currentFiles.filter(file => file.fileId !== fileId);

            // 从文件记录中移除
            fileRecords = fileRecords.filter(record => record.fileId !== fileId);

            // 从选择列表中移除
            const index = selectedFiles.indexOf(fileId);
            if (index > -1) {
                selectedFiles.splice(index, 1);
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            updateFileCountBadges();
            updateSelectionUI();
            checkAnalyzeButton();
        }
    }

    function toggleSelectAll() {
        if (currentFileView === 'current') {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('#currentFilesList .file-checkbox');
            const shouldSelectAll = selectAllCheckbox.checked;

            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = shouldSelectAll;
                    const fileId = checkbox.closest('.file-item').dataset.fileId;
                    toggleCurrentFileSelection(fileId, checkbox.checked);
                }
            });
        }
        // 历史记录视图不支持全选功能
    }

    // 更新全选复选框状态
    function updateSelectAllCheckbox() {
        if (currentFileView === 'current') {
            const selectAllCheckbox = document.getElementById('selectAllCheckbox');
            const checkboxes = document.querySelectorAll('#currentFilesList .file-checkbox');
            const enabledCheckboxes = Array.from(checkboxes).filter(cb => !cb.disabled);

            if (enabledCheckboxes.length === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else {
                const checkedCount = enabledCheckboxes.filter(cb => cb.checked).length;

                if (checkedCount === 0) {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = false;
                } else if (checkedCount === enabledCheckboxes.length) {
                    selectAllCheckbox.checked = true;
                    selectAllCheckbox.indeterminate = false;
                } else {
                    selectAllCheckbox.checked = false;
                    selectAllCheckbox.indeterminate = true;
                }
            }
        }
    }

    function clearSelectedFiles() {
        if (currentFileView === 'current') {
            // 获取选中的文件ID
            const selectedFileIds = currentFiles
                .filter(file => file.selected && file.status !== 'deprecated')
                .map(file => file.fileId);

            if (selectedFileIds.length === 0) {
                showMessage('没有选中的文件', 'warning');
                return;
            }

            if (confirm(`确定要移除 ${selectedFileIds.length} 个选中的文件吗？`)) {
                selectedFileIds.forEach(fileId => {
                    removeCurrentFile(fileId);
                });
            }
        }
        // 历史记录视图不支持批量操作
    }

    function updateSelectionUI() {
        if (currentFileView === 'current') {
            // 更新当前文件的选择状态
            const selectedCount = currentFiles.filter(file => file.selected && file.status !== 'deprecated').length;

            // 更新文件项的选择状态显示
            currentFiles.forEach(file => {
                const fileItem = document.querySelector(`[data-file-id="${file.fileId}"]`);
                if (fileItem) {
                    if (file.selected) {
                        fileItem.classList.add('selected');
                    } else {
                        fileItem.classList.remove('selected');
                    }
                }
            });

            // 可以在这里添加选择计数显示
        }
    }

    // 文件操作
    function removeFile(fileId) {
        if (!selectedAnalysisType) return;

        // 从对应类型的文件列表中移除
        if (filesByType[selectedAnalysisType]) {
            filesByType[selectedAnalysisType] = filesByType[selectedAnalysisType].filter(file => file.fileId !== fileId);
        }

        // 从对应类型的文件记录中移除
        if (fileRecordsByType[selectedAnalysisType]) {
            fileRecordsByType[selectedAnalysisType] = fileRecordsByType[selectedAnalysisType].filter(record => record.fileId !== fileId);
        }

        // 更新显示
        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
    }

    function viewAnalysisResult(recordId) {
        showAnalysisResultModal(recordId);
    }

    function viewFileDetail(recordId) {
        window.open(`/records/${recordId}`, '_blank');
    }

    function reanalyzeFile(recordId) {
        if (confirm('确定要重新分析这个文件吗？')) {
            // 调用重新分析API
            API.post(`/api/files/${recordId}/reanalyze`)
                .then(response => {
                    if (response.success) {
                        showMessage('文件已提交重新分析', 'success');
                        refreshFileList();
                    } else {
                        showMessage(response.message || '重新分析失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('重新分析失败:', error);
                    showMessage('重新分析失败，请重试', 'error');
                });
        }
    }

    // 重新分析历史文件
    function reanalyzeHistoryFile(recordId) {
        if (confirm('确定要重新分析这个文件吗？')) {
            // 调用重新分析API
            API.post(`/api/files/${recordId}/reanalyze`)
                .then(response => {
                    if (response.success) {
                        showMessage('文件已提交重新分析', 'success');
                        if (currentFileView === 'history') {
                            loadHistoryFiles();
                        }
                    } else {
                        showMessage(response.message || '重新分析失败', 'error');
                    }
                })
                .catch(error => {
                    console.error('重新分析失败:', error);
                    showMessage('重新分析失败，请重试', 'error');
                });
        }
    }

    // 隐藏分页控件
    function hidePagination() {
        const paginationElement = document.getElementById('filePagination');
        if (paginationElement) {
            paginationElement.innerHTML = '';
            paginationElement.style.display = 'none';
        }
    }

    // 显示分页控件
    function showPagination() {
        const paginationElement = document.getElementById('filePagination');
        if (paginationElement) {
            paginationElement.style.display = 'flex';
        }
    }

    // 更新分页
    function updateFilePagination(pagination) {
        const paginationElement = document.getElementById('filePagination');
        if (!paginationElement || !pagination || currentFileView !== 'history') {
            hidePagination();
            return;
        }

        const { page, pages, total } = pagination;

        if (pages <= 1) {
            hidePagination();
            return;
        }

        showPagination();

        let paginationHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div class="text-muted">
                    第 ${page} 页，共 ${pages} 页，总计 ${total} 条记录
                </div>
                <div class="d-flex gap-1">
        `;

        // 上一页按钮
        if (page > 1) {
            paginationHTML += `
                <button class="btn btn-sm btn-outline-secondary" onclick="goToFilePage(${page - 1})">
                    <i class="bi bi-chevron-left"></i>
                </button>
            `;
        }

        // 页码按钮
        const startPage = Math.max(1, page - 2);
        const endPage = Math.min(pages, page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === page ? 'btn-primary' : 'btn-outline-secondary';
            paginationHTML += `
                <button class="btn btn-sm ${activeClass}" onclick="goToFilePage(${i})">
                    ${i}
                </button>
            `;
        }

        // 下一页按钮
        if (page < pages) {
            paginationHTML += `
                <button class="btn btn-sm btn-outline-secondary" onclick="goToFilePage(${page + 1})">
                    <i class="bi bi-chevron-right"></i>
                </button>
            `;
        }

        paginationHTML += `
                </div>
            </div>
        `;

        paginationElement.innerHTML = paginationHTML;
    }

    function goToFilePage(page) {
        filePagination.currentPage = page;
        if (currentFileView === 'history') {
            loadHistoryFiles();
        }
    }

    // 显示文件错误
    function showFileError(message) {
        if (currentFileView === 'current') {
            const container = document.getElementById('currentFilesList');
            if (container) {
                container.innerHTML = `
                    <div class="file-list-empty">
                        <i class="bi bi-exclamation-triangle text-danger"></i>
                        <div class="text-danger">${message}</div>
                        <button class="btn btn-sm btn-outline-primary mt-2" onclick="refreshFileList()">
                            <i class="bi bi-arrow-clockwise me-1"></i>重试
                        </button>
                    </div>
                `;
            }
        } else {
            // 历史记录视图 - 只更新表格体
            const tbody = document.getElementById('historyFilesTableBody');
            if (tbody) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" class="text-center py-5">
                            <i class="bi bi-exclamation-triangle text-danger display-4"></i>
                            <div class="mt-3 text-danger">${message}</div>
                            <button class="btn btn-sm btn-outline-primary mt-2" onclick="refreshFileList()">
                                <i class="bi bi-arrow-clockwise me-1"></i>重试
                            </button>
                        </td>
                    </tr>
                `;
            }
        }
    }

    // 加载分析类型
    function loadAnalysisTypes() {
        const analysisTypes = [
            {
                id: 'futures_account',
                name: '期货账户',
                icon: 'bi-graph-up',
                description: '分析期货账户开户、变更等相关文档，提取账户信息和交易记录'
            },
            {
                id: 'wealth_management',
                name: '理财产品',
                icon: 'bi-piggy-bank',
                description: '分析理财产品说明书，提取产品信息、收益率、风险等级等要素'
            },
            {
                id: 'broker_interest',
                name: '券商计息',
                icon: 'bi-calculator',
                description: '处理券商计息相关变更文档，识别利率调整和计息规则变化'
            },
            {
                id: 'account_opening',
                name: '账户开户场景',
                icon: 'bi-person-plus',
                description: '分析账户开户相关文档，提取开户信息、账户类型、客户资料等数据'
            },
            {
                id: 'ningyin_fee',
                name: '宁银费用',
                icon: 'bi-receipt',
                description: '处理宁银理财费用相关变更文档，识别费用标准和收费项目'
            },
            {
                id: 'non_standard_trade',
                name: '非标交易',
                icon: 'bi-file-text',
                description: '分析非标准化交易确认文档，提取交易详情和确认信息'
            }
        ];

        const container = document.getElementById('analysisTypes');
        if (!container) return;

        container.innerHTML = '';

        analysisTypes.forEach(type => {
            const typeCard = document.createElement('div');
            typeCard.className = 'analysis-type-card';
            typeCard.dataset.type = type.id;

            typeCard.innerHTML = `
                <div class="analysis-type-icon">
                    <i class="bi ${type.icon}"></i>
                </div>
                <h4 class="analysis-type-title">${type.name}</h4>
                <p class="analysis-type-desc">${type.description}</p>
            `;

            typeCard.addEventListener('click', () => selectAnalysisType(type.id, typeCard));
            container.appendChild(typeCard);
        });
    }

    // 选择分析类型
    function selectAnalysisType(typeId, element) {
        // 移除其他选中状态
        document.querySelectorAll('.analysis-type-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 设置当前选中
        element.classList.add('selected');
        selectedAnalysisType = typeId;

        // 创建或更新隐藏的input来存储选择的类型
        let analysisTypeInput = document.getElementById('analysisType');
        if (!analysisTypeInput) {
            analysisTypeInput = document.createElement('input');
            analysisTypeInput.type = 'hidden';
            analysisTypeInput.id = 'analysisType';
            document.body.appendChild(analysisTypeInput);
        }
        analysisTypeInput.value = typeId;

        // 初始化该类型的文件存储（如果还没有）
        if (!filesByType[typeId]) {
            filesByType[typeId] = [];
        }
        if (!fileRecordsByType[typeId]) {
            fileRecordsByType[typeId] = [];
        }

        // 刷新文件列表显示（显示当前类型的文件）
        displayCurrentFiles();
        updateFileCountBadges();

        // 同步文件管理的类型过滤器
        const fileTypeFilter = document.getElementById('fileTypeFilter');
        if (fileTypeFilter) {
            fileTypeFilter.value = typeId;


            // 更新文件过滤器状态
            if (typeof fileFilters !== 'undefined') {
                fileFilters.type = typeId;
            }
        }

        // 同步历史记录区域的类型过滤器
        const historyTypeFilter = document.getElementById('historyAnalysisType');
        if (historyTypeFilter) {
            historyTypeFilter.value = typeId;

        }

        // 如果当前在历史记录视图，刷新历史文件列表
        if (currentFileView === 'history') {

            loadHistoryFiles();



        } else {
            // 如果在当前文件视图，也要应用过滤器
            if (typeof filterFiles === 'function') {
                filterFiles();
            }
        }

        // 清除上传区域的提醒信息
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            const reminder = uploadArea.querySelector('.analysis-type-reminder');
            if (reminder) {
                reminder.remove();
            }
        }

        // 检查是否可以启用分析按钮
        checkAnalyzeButton();

        // 显示成功消息
        const titleElement = element.querySelector('.analysis-type-title') || element.querySelector('h6');
        const title = titleElement ? titleElement.textContent : '未知类型';

        // 自动滚动到文件上传区域（延迟执行确保DOM更新完成）
        setTimeout(() => {
            scrollToUploadArea();
        }, 100);

        // 处理待处理的文件（如果有的话）
        if (window.pendingFiles && window.pendingFiles.length > 0) {
            const pendingFiles = window.pendingFiles;
            window.pendingFiles = null; // 清除待处理文件

            // 显示提示消息
            showMessage(`已选择 ${title} 类型，正在处理 ${pendingFiles.length} 个文件...`, 'info');

            // 处理文件
            setTimeout(() => {
                processSelectedFiles(pendingFiles);
            }, 500);
            return;
        }

        // 检查是否开启自动分析且有单个文件
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        const isAutoAnalysisEnabled = autoAnalysisSwitch ? autoAnalysisSwitch.checked : false;

        // 获取当前已选择的文件数量
        const selectedCurrentFiles = getCurrentTypeFiles().filter(file => file.selected && file.status !== 'deprecated');

        if (selectedCurrentFiles.length === 1 && isAutoAnalysisEnabled) {
            showAutoAnalysisMessage(title, selectedCurrentFiles[0].name);
            setTimeout(() => {
                startAnalysis();
            }, 1500);
        } else {
            showMessage(`已选择分析类型：${title}`, 'success');
            if (selectedCurrentFiles.length === 1 && !isAutoAnalysisEnabled) {
                setTimeout(() => {
                    showMessage('💡 提示：您可以开启"自动分析"开关，单文件上传时自动开始分析', 'info', 3000);
                }, 1000);
            }
        }
    }

    // 滚动到文件上传区域
    function scrollToUploadArea() {
        // 尝试多个可能的上传区域元素
        const uploadTargets = [
            'uploadArea',           // 主要目标：上传区域
            'fileManagementArea',   // 文件管理区域
            'unified-file-manager', // 统一文件管理器
            'currentFilesList',     // 当前文件列表
            'fileUploadSection'     // 文件上传区域
        ];

        let uploadArea = null;
        for (const targetId of uploadTargets) {
            uploadArea = document.getElementById(targetId);
            if (uploadArea) {

                break;
            }
        }

        if (!uploadArea) {
            console.warn('No upload area found, trying alternative approach');
            // 尝试通过类名查找
            uploadArea = document.querySelector('.file-management-card, .upload-area, [data-upload-area]');
        }

        if (!uploadArea) {
            console.error('Upload area not found');
            return;
        }

        try {
            // 获取导航栏高度（考虑sticky定位）
            const navbar = document.querySelector('.top-navbar, .navbar, .fixed-top, .sticky-top');
            const navbarHeight = navbar ? navbar.offsetHeight : 70; // 默认70px

            // 获取上传区域的位置
            const uploadAreaRect = uploadArea.getBoundingClientRect();
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

            // 计算目标滚动位置（减去导航栏高度和一些额外的间距）
            const extraPadding = 20; // 额外的间距
            const targetScrollTop = Math.max(0, currentScrollTop + uploadAreaRect.top - navbarHeight - extraPadding);



            // 检查是否需要滚动
            if (Math.abs(uploadAreaRect.top - navbarHeight) < 50) {
            } else {
                // 使用平滑滚动
                window.scrollTo({
                    top: targetScrollTop,
                    behavior: 'smooth'
                });
            }

            // 添加高亮动画效果
            uploadArea.classList.add('highlight');

            // 动画结束后移除类
            setTimeout(() => {
                uploadArea.classList.remove('highlight');
            }, 600);

        } catch (error) {
            console.error('Scroll to upload area failed:', error);
            // 降级方案1：使用简单的scrollIntoView
            try {
                uploadArea.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'nearest'
                });
            } catch (fallbackError) {
                console.error('Fallback scroll also failed:', fallbackError);
                // 降级方案2：直接滚动到元素位置
                const rect = uploadArea.getBoundingClientRect();
                const scrollTop = window.pageYOffset + rect.top - 100;
                window.scrollTo(0, scrollTop);
            }
        }
    }

    // 显示自动分析消息提醒
    function showAutoAnalysisMessage(analysisType, fileName) {
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        const switchLabel = autoAnalysisSwitch ? autoAnalysisSwitch.nextElementSibling : null;

        // 临时高亮开关
        if (switchLabel) {
            switchLabel.style.color = '#28a745';
            switchLabel.style.fontWeight = 'bold';
            setTimeout(() => {
                switchLabel.style.color = '';
                switchLabel.style.fontWeight = '';
            }, 2000);
        }

        // 显示详细的自动分析消息
        showMessage(
            `🤖 自动分析已启动<br>` +
            `📄 文件：${fileName}<br>` +
            `🎯 类型：${analysisType}<br>` +
            `⏱️ 预计用时：30-60秒`,
            'info',
            2000
        );
    }

    // 获取分析类型的显示名称
    function getAnalysisTypeName(typeId) {
        const typeNames = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '账户开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeNames[typeId] || typeId;
    }

    // 根据分析类型筛选文件列表
    function filterFilesByType(typeId) {
        // 如果当前在历史记录视图，切换到当前上传视图以便用户上传新文件
        if (currentFileView === 'history') {
            switchFileView('current');
        }

        // 不自动设置筛选器，避免干扰用户的筛选选择
        // 只是确保用户在正确的视图中进行文件上传
    }

    // 设置事件监听器
    function setupEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');

        if (uploadArea && fileInput) {
            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);
        }

        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', startAnalysis);
        }

        // 自动分析开关事件监听
        const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
        if (autoAnalysisSwitch) {
            autoAnalysisSwitch.addEventListener('change', function() {
                const isEnabled = this.checked;
                const message = isEnabled ?
                    '✅ 自动分析已开启：单文件上传时将自动开始分析' :
                    '⏸️ 自动分析已关闭：需要手动点击"开始分析"按钮';
                showMessage(message, isEnabled ? 'success' : 'info', 3000);

                // 保存用户偏好到localStorage
                localStorage.setItem('autoAnalysisEnabled', isEnabled);

                // 同步到服务器
                API.post('/api/settings/auto-analysis', { enabled: isEnabled })
                    .then(response => {
                        if (!response.success) {
                            console.warn('自动分析设置同步失败:', response.message);
                        }
                    })
                    .catch(error => {
                        console.warn('自动分析设置同步失败:', error);
                    });
            });

            // 从服务器获取自动分析设置
            API.get('/api/settings/auto-analysis')
                .then(response => {
                    if (response.success && response.data) {
                        autoAnalysisSwitch.checked = response.data.enabled;
                        localStorage.setItem('autoAnalysisEnabled', response.data.enabled);
                    } else {
                        // 如果服务器获取失败，从localStorage恢复用户偏好
                        const savedPreference = localStorage.getItem('autoAnalysisEnabled');
                        if (savedPreference !== null) {
                            autoAnalysisSwitch.checked = savedPreference === 'true';
                        }
                    }
                })
                .catch(error => {
                    console.warn('获取自动分析设置失败:', error);
                    // 从localStorage恢复用户偏好
                    const savedPreference = localStorage.getItem('autoAnalysisEnabled');
                    if (savedPreference !== null) {
                        autoAnalysisSwitch.checked = savedPreference === 'true';
                    }
                });
        }
    }

    // 处理文件选择
    function handleFileSelect(event) {
        const files = Array.from(event.target.files);
        processSelectedFiles(files);
    }

    // 处理拖拽悬停
    function handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');

        // 如果没有选择分析类型，显示提醒
        if (!selectedAnalysisType) {
            const uploadArea = event.currentTarget;
            if (!uploadArea.querySelector('.analysis-type-reminder')) {
                const reminder = document.createElement('div');
                reminder.className = 'analysis-type-reminder alert alert-warning mt-2';
                reminder.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    请先选择分析类型再上传文件
                `;
                uploadArea.appendChild(reminder);
            }
        }
    }

    // 处理拖拽离开
    function handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');

        // 移除提醒信息
        const reminder = event.currentTarget.querySelector('.analysis-type-reminder');
        if (reminder) {
            reminder.remove();
        }
    }

    // 处理文件拖拽
    function handleFileDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');

        const files = Array.from(event.dataTransfer.files);
        processSelectedFiles(files);
    }

    // 处理选中的文件
    function processSelectedFiles(files) {
        // 验证文件
        const validFiles = files.filter(file => {
            const validTypes = ['application/pdf', 'image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/tiff'];
            const maxSize = 50 * 1024 * 1024; // 50MB

            if (!validTypes.includes(file.type) && !file.name.match(/\.(pdf|png|jpg|jpeg|gif|bmp|tiff)$/i)) {
                showMessage(`文件 ${file.name} 格式不支持`, 'warning');
                return false;
            }

            if (file.size > maxSize) {
                showMessage(`文件 ${file.name} 超过50MB限制`, 'warning');
                return false;
            }

            return true;
        });

        if (validFiles.length > 0) {
            // 检查是否选择了分析类型
            if (!selectedAnalysisType) {
                showAnalysisTypeRequiredModal(validFiles);
                return;
            }

            // 上传每个文件到服务器
            validFiles.forEach(file => {
                uploadFileToServer(file, selectedAnalysisType);
            });

            // 更新统一文件管理器
            updateFileCountBadges();

            // 如果当前在当前上传视图，刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            // 分析按钮现在在文件管理栏中，无需单独显示

            checkAnalyzeButton();

            // 单文件自动分析逻辑
            const autoAnalysisSwitch = document.getElementById('autoAnalysisSwitch');
            const isAutoAnalysisEnabled = autoAnalysisSwitch ? autoAnalysisSwitch.checked : false;

            // 获取当前类型的文件数量
            const currentTypeFiles = getCurrentTypeFiles();

            if (validFiles.length === 1 && currentTypeFiles.length === 1 && selectedAnalysisType && isAutoAnalysisEnabled) {
                showAutoAnalysisMessage(getAnalysisTypeName(selectedAnalysisType), validFiles[0].name);
                // 延迟一点时间让用户看到消息，然后自动开始分析
                setTimeout(() => {
                    startAnalysis();
                }, 1500);
            } else {
                if (validFiles.length === 1) {
                    showMessage(`成功添加文件 ${validFiles[0].name}`, 'success');
                    // 如果是单文件且自动分析关闭，提醒用户可以手动分析
                    if (selectedAnalysisType && !isAutoAnalysisEnabled) {
                        setTimeout(() => {
                            showMessage('💡 提示：您可以点击"开始分析"按钮进行分析，或开启"自动分析"开关', 'info', 4000);
                        }, 1000);
                    }
                } else {
                    showMessage(`成功添加 ${validFiles.length} 个文件`, 'success');
                    if (selectedAnalysisType) {
                        setTimeout(() => {
                            showMessage('💡 提示：您可以点击"开始分析"按钮进行批量分析', 'info', 3000);
                        }, 1000);
                    }
                }
            }
        }
    }

    // 上传文件到服务器
    function uploadFileToServer(file, analysisType) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', analysisType);

        // 创建临时的本地文件记录
        const tempFileId = `temp_${++fileIdCounter}`;
        const fileRecord = {
            fileId: tempFileId,
            file: file,
            name: file.name,
            size: file.size,
            type: file.type,
            status: 'uploading', // uploading, uploaded, pending, analyzing, analyzed, failed
            uploadTime: new Date(),
            analysisResult: null,
            recordId: null,
            selected: true,
            analysisType: analysisType
        };

        // 添加到对应类型的文件记录中
        if (!fileRecordsByType[analysisType]) {
            fileRecordsByType[analysisType] = [];
        }
        fileRecordsByType[analysisType].push(fileRecord);

        // 添加到对应类型的文件列表中（用于显示）
        const fileItem = {
            fileId: tempFileId,
            name: file.name,
            size: file.size,
            type: file.type,
            uploadTime: fileRecord.uploadTime,
            status: 'uploading',
            file_status: 'active',
            file: file,
            selected: true,
            analysisType: analysisType
        };

        if (!filesByType[analysisType]) {
            filesByType[analysisType] = [];
        }
        filesByType[analysisType].push(fileItem);

        // 刷新显示
        updateFileCountBadges();
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 发送上传请求
        fetch('/api/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新文件记录为真实的记录ID
                fileRecord.recordId = data.data.id;
                fileRecord.fileId = data.data.id; // 使用真实的记录ID作为fileId
                fileRecord.status = data.data.status;

                // 同步更新文件列表项
                fileItem.fileId = data.data.id;
                fileItem.status = data.data.status;

                showMessage(`文件 ${file.name} 上传成功`, 'success');
            } else {
                fileRecord.status = 'failed';
                fileItem.status = 'failed';
                showMessage(`文件 ${file.name} 上传失败: ${data.message}`, 'error');
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
            checkAnalyzeButton();
        })
        .catch(error => {
            console.error('文件上传失败:', error);
            fileRecord.status = 'failed';
            fileItem.status = 'failed';
            showMessage(`文件 ${file.name} 上传失败`, 'error');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        });
    }

    // 更新文件列表显示（统一文件管理器）
    function updateFileList() {
        // 更新统一文件管理器
        updateFileCountBadges();

        // 如果当前在当前上传视图，刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 检查分析按钮状态
        checkAnalyzeButton();
    }

    // 创建文件项
    function createFileItem(record) {
        const fileItem = document.createElement('div');
        fileItem.className = `file-item ${record.status}`;
        fileItem.dataset.fileId = record.id;

        // 选择框
        const fileCheckbox = document.createElement('div');
        fileCheckbox.className = 'file-checkbox';
        fileCheckbox.innerHTML = `
            <input type="checkbox"
                   id="file-${record.id}"
                   ${record.selected ? 'checked' : ''}
                   ${record.status === 'deprecated' ? 'disabled' : ''}
                   onchange="toggleFileSelection(${record.id}, this.checked)">
        `;

        // 文件图标和状态徽章
        const fileIcon = document.createElement('div');
        fileIcon.className = 'file-icon';

        const iconClass = record.type.includes('pdf') ? 'bi-file-pdf' : 'bi-file-image';
        const iconBg = record.type.includes('pdf') ? 'file-pdf' : 'file-image';

        fileIcon.innerHTML = `
            <div class="${iconBg}">
                <i class="bi ${iconClass}"></i>
            </div>
            ${getStatusBadge(record.status)}
        `;

        // 文件信息
        const fileInfo = document.createElement('div');
        fileInfo.className = 'file-info';

        const uploadTime = record.uploadTime.toLocaleString('zh-CN');
        const fileSize = (record.size / 1024 / 1024).toFixed(2);

        fileInfo.innerHTML = `
            <div class="file-name">${record.name}</div>
            <div class="file-meta">
                <span><i class="bi bi-hdd me-1"></i>${fileSize} MB</span>
                <span><i class="bi bi-clock me-1"></i>${uploadTime}</span>
                <span class="status-text">${getStatusText(record.status)}</span>
            </div>
        `;

        // 文件操作按钮
        const fileActions = document.createElement('div');
        fileActions.className = 'file-actions';
        fileActions.innerHTML = getActionButtons(record);

        fileItem.appendChild(fileCheckbox);
        fileItem.appendChild(fileIcon);
        fileItem.appendChild(fileInfo);
        fileItem.appendChild(fileActions);

        return fileItem;
    }

    // 获取状态徽章
    function getStatusBadge(status) {
        const badges = {
            'pending': '',
            'analyzing': '<div class="file-status-badge analyzing"><i class="bi bi-arrow-clockwise"></i></div>',
            'analyzed': '<div class="file-status-badge analyzed"><i class="bi bi-check"></i></div>',
            'failed': '<div class="file-status-badge" style="background: var(--danger-color);"><i class="bi bi-x"></i></div>',
            'deprecated': '<div class="file-status-badge deprecated"><i class="bi bi-archive"></i></div>'
        };
        return badges[status] || '';
    }

    // 获取状态文本
    function getStatusText(status) {
        const texts = {
            'pending': '待分析',
            'analyzing': '分析中...',
            'analyzed': '已分析',
            'failed': '分析失败',
            'deprecated': '已废弃'
        };
        return texts[status] || '未知状态';
    }

    // 获取操作按钮
    function getActionButtons(record) {
        let buttons = '';

        if (record.status === 'pending') {
            buttons += `<button class="btn-file-action btn-analyze" onclick="analyzeFile(${record.id})">
                <i class="bi bi-cpu"></i>分析
            </button>`;
        }

        if (record.status === 'analyzed') {
            buttons += `<button class="btn-file-action btn-result" onclick="viewResult(${record.id})">
                <i class="bi bi-eye"></i>结果
            </button>`;
            buttons += `<button class="btn-file-action btn-reanalyze" onclick="reanalyzeFile(${record.id})">
                <i class="bi bi-arrow-clockwise"></i>重新分析
            </button>`;
        }

        if (record.status === 'failed') {
            buttons += `<button class="btn-file-action btn-reanalyze" onclick="reanalyzeFile(${record.id})">
                <i class="bi bi-arrow-clockwise"></i>重试
            </button>`;
        }

        if (record.status !== 'deprecated') {
            buttons += `<button class="btn-file-action btn-deprecate" onclick="deprecateFile(${record.id})">
                <i class="bi bi-archive"></i>废弃
            </button>`;
        } else {
            buttons += `<button class="btn-file-action btn-restore" onclick="restoreFile(${record.id})">
                <i class="bi bi-arrow-counterclockwise"></i>恢复
            </button>`;
        }

        return buttons;
    }

    // 切换文件选择状态
    function toggleFileSelection(fileId, isSelected) {
        const record = fileRecords.find(r => r.id === fileId);
        if (record && record.status !== 'deprecated') {
            record.selected = isSelected;
            updateSelectedFiles();
            updateSelectedCount();
            checkAnalyzeButton();
        }
    }

    // 更新选择计数显示
    function updateSelectedCount() {
        const selectedCount = document.getElementById('selectedCount');
        if (selectedCount) {
            const count = fileRecords.filter(r => r.selected && r.status !== 'deprecated').length;
            selectedCount.textContent = `已选择 ${count} 个文件`;
        }
    }



    // 重新分析文件
    function reanalyzeFile(fileId) {
        const record = fileRecords.find(r => r.id === fileId);
        if (record) {
            record.status = 'pending';
            record.analysisResult = null;
            record.recordId = null;
            updateFileList();
            analyzeFile(fileId);
        }
    }



    // 更新本地文件状态 - 用于废弃/恢复操作的即时反馈
    function updateLocalFileStatus(fileId, newStatus) {
        const targetStatus = newStatus === 'deprecated' ? 'deprecated' : 'active';

        // 更新历史文件列表中的状态
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            historyFile.file_status = targetStatus;
        }

        // 更新当前文件列表中的状态
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) {
                file.file_status = targetStatus;
                break;
            }
        }

        // 立即更新页面显示
        updateFileRowStatus(fileId, targetStatus);
    }

    // 立即更新文件行的显示状态
    function updateFileRowStatus(fileId, fileStatus) {
        const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
        if (fileRow) {
            if (fileStatus === 'deprecated') {
                fileRow.classList.add('file-deprecated');
            } else {
                fileRow.classList.remove('file-deprecated');
            }

            // 重新渲染该行的按钮
            const actionsCell = fileRow.querySelector('td:last-child .file-actions');
            if (actionsCell) {
                const isDeprecated = fileStatus === 'deprecated';
                // 简单判断是否有结果（这里可以根据实际需要调整）
                const hasResult = false; // 暂时设为false，可以根据文件状态判断

                actionsCell.innerHTML = `
                    <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                            onclick="analyzeFile('${fileId}')"
                            ${!isDeprecated ? '' : 'disabled'}>
                        <i class="bi bi-cpu"></i>
                        ${hasResult ? '重分析' : '分析'}
                    </button>
                    <button class="btn btn-view-result btn-sm"
                            onclick="viewResult('${fileId}')"
                            ${hasResult ? '' : 'disabled'}>
                        <i class="bi bi-eye"></i>
                        查看结果
                    </button>
                    ${!isDeprecated ? `
                        <button class="btn btn-deprecate btn-sm"
                                onclick="deprecateFile('${fileId}')">
                            <i class="bi bi-archive"></i>
                            废弃
                        </button>
                    ` : `
                        <button class="btn btn-restore btn-sm"
                                onclick="restoreFile('${fileId}')">
                            <i class="bi bi-arrow-counterclockwise"></i>
                            恢复
                        </button>
                    `}
                `;
            }
        }
    }

    // 更新选中的文件列表
    function updateSelectedFiles() {
        selectedFiles = fileRecords
            .filter(r => r.status !== 'deprecated' && r.selected)
            .map(r => r.file);
    }

    // 全选文件
    function selectAllFiles() {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = getCurrentTypeFiles();
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        currentTypeFiles.forEach(file => {
            if (file.status !== 'deprecated') {
                file.selected = true;
            }
        });

        fileRecords.forEach(record => {
            if (record.status !== 'deprecated') {
                record.selected = true;
            }
        });

        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
        showMessage('已全选所有可用文件', 'info');
    }

    // 取消全选文件
    function deselectAllFiles() {
        if (!selectedAnalysisType) return;

        const currentTypeFiles = getCurrentTypeFiles();
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];

        currentTypeFiles.forEach(file => {
            file.selected = false;
        });

        fileRecords.forEach(record => {
            record.selected = false;
        });

        displayCurrentFiles();
        updateFileCountBadges();
        checkAnalyzeButton();
        showMessage('已取消选择所有文件', 'info');
    }

    // 检查分析按钮状态
    function checkAnalyzeButton() {
        const analyzeBtn = document.getElementById('analyzeBtn');

        if (analyzeBtn) {
            // 获取当前类型的可用文件
            const currentTypeFiles = getCurrentTypeFiles();
            const availableFiles = currentTypeFiles.filter(f => f.status !== 'deprecated' && f.selected);
            const canAnalyze = selectedAnalysisType && availableFiles.length > 0;
            analyzeBtn.disabled = !canAnalyze;

            // 根据文件数量更新按钮文本和样式
            if (canAnalyze) {
                if (availableFiles.length === 1) {
                    analyzeBtn.innerHTML = '<i class="bi bi-cpu me-1"></i>开始分析';
                    analyzeBtn.title = '开始智能分析';
                } else {
                    analyzeBtn.innerHTML = `<i class="bi bi-cpu me-1"></i>批量分析 (${availableFiles.length})`;
                    analyzeBtn.title = `批量分析${availableFiles.length}个文件`;
                }
                analyzeBtn.className = 'btn btn-sm btn-success';
            } else {
                analyzeBtn.innerHTML = '<i class="bi bi-cpu me-1"></i>开始分析';
                analyzeBtn.title = '请先选择分析类型和上传文件';
                analyzeBtn.className = 'btn btn-sm btn-outline-secondary';
            }
        }
    }



    // 开始分析
    function startAnalysis() {
        if (!selectedAnalysisType) {
            showMessage('请选择分析类型', 'warning');
            return;
        }

        // 获取当前类型的选中文件
        const currentTypeFiles = getCurrentTypeFiles();
        const availableFiles = currentTypeFiles.filter(file => file.selected && file.status !== 'deprecated');
        if (availableFiles.length === 0) {
            showMessage('请先上传文件或选择可用文件', 'warning');
            return;
        }

        // 禁用分析按钮并更新状态
        const analyzeBtn = document.getElementById('analyzeButton');
        if (analyzeBtn) {
            analyzeBtn.disabled = true;

            const fileCount = availableFiles.length;
            if (fileCount === 1) {
                analyzeBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>正在分析...';
                showMessage(`正在分析文件：${availableFiles[0].name}`, 'info');
            } else {
                analyzeBtn.innerHTML = `<i class="bi bi-hourglass-split me-2"></i>正在批量分析 (${fileCount}个文件)...`;
                showMessage(`正在批量分析${fileCount}个文件`, 'info');
            }
        }

        // 更新文件状态为分析中
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        availableFiles.forEach(file => {
            file.status = 'analyzing';
            // 同时更新fileRecords中对应的记录
            const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
            if (fileRecord) {
                fileRecord.status = 'analyzing';
            }
        });

        // 刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        // 创建FormData
        const formData = new FormData();
        formData.append('analysis_type', selectedAnalysisType);

        availableFiles.forEach(file => {
            formData.append('files', file.file);
        });

        // 发送分析请求 - 使用正确的API端点
        let analysisPromise;

        if (availableFiles.length === 1) {
            // 单文件分析
            analysisPromise = API.post(`/api/files/${availableFiles[0].fileId}/analyze`, {
                analysis_type: selectedAnalysisType
            });
        } else {
            // 批量分析 - 修复API路由
            const fileIds = availableFiles.map(file => file.fileId);
            analysisPromise = API.post('/api/files/batch-analyze', {
                file_ids: fileIds,
                analysis_type: selectedAnalysisType
            });
        }

        analysisPromise.then(data => {
            const fileCount = availableFiles.length;

            if (data.success) {
                // 更新文件状态
                if (data.record_ids && data.record_ids.length > 0) {
                    data.record_ids.forEach((recordId, index) => {
                        if (availableFiles[index]) {
                            availableFiles[index].status = 'analyzed';
                            availableFiles[index].recordId = recordId;

                            // 同时更新fileRecords中对应的记录
                            const fileRecord = fileRecords.find(r => r.fileId === availableFiles[index].fileId);
                            if (fileRecord) {
                                fileRecord.status = 'analyzed';
                                fileRecord.recordId = recordId;
                            }
                        }
                    });
                } else {
                    availableFiles.forEach(file => {
                        file.status = 'analyzed';

                        // 同时更新fileRecords中对应的记录
                        const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                        if (fileRecord) {
                            fileRecord.status = 'analyzed';
                        }
                    });
                }

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                if (fileCount === 1) {
                    showMessage('文件分析完成', 'success');
                } else {
                    showMessage(`${fileCount}个文件批量分析完成`, 'success');
                }
            } else {
                // 更新文件状态为失败
                availableFiles.forEach(file => {
                    file.status = 'failed';

                    // 同时更新fileRecords中对应的记录
                    const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                    if (fileRecord) {
                        fileRecord.status = 'failed';
                    }
                });

                // 刷新显示
                if (currentFileView === 'current') {
                    displayCurrentFiles();
                }

                showMessage(data.message || '分析失败', 'error');
            }

            // 恢复按钮状态
            const analyzeBtn = document.getElementById('analyzeButton');
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<i class="bi bi-play-circle"></i> 开始分析 (<span id="selectedCount">0</span>)';
            }
            checkAnalyzeButton(); // 重新检查按钮状态
        })
        .catch(error => {
            console.error('分析请求失败:', error);

            // 更新文件状态为失败
            availableFiles.forEach(file => {
                file.status = 'failed';

                // 同时更新fileRecords中对应的记录
                const fileRecord = fileRecords.find(r => r.fileId === file.fileId);
                if (fileRecord) {
                    fileRecord.status = 'failed';
                }
            });

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }

            showMessage('分析请求失败，请重试', 'error');

            // 恢复按钮状态
            const analyzeBtn = document.getElementById('analyzeButton');
            if (analyzeBtn) {
                analyzeBtn.disabled = false;
                analyzeBtn.innerHTML = '<i class="bi bi-play-circle"></i> 开始分析 (<span id="selectedCount">0</span>)';
            }
            checkAnalyzeButton(); // 重新检查按钮状态
        });
    }

    // ==================== 历史记录功能 ====================












    // 获取当前分析类型
    function getCurrentAnalysisType() {
        // 从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const urlType = urlParams.get('analysis_type');
        if (urlType) return urlType;

        // 从页面元素获取
        const analysisTypeSelect = document.getElementById('analysisType');
        if (analysisTypeSelect && analysisTypeSelect.value) return analysisTypeSelect.value;

        // 从全局变量获取
        if (typeof selectedAnalysisType !== 'undefined' && selectedAnalysisType) {
            return selectedAnalysisType;
        }

        // 从页面标题或其他元素推断
        const pageTitle = document.title;
        if (pageTitle.includes('期货账户')) return 'futures_account';
        if (pageTitle.includes('全部状态')) return 'all_status';

        return '';
    }



    // 获取分析类型文本
    function getAnalysisTypeText(type) {
        const typeMap = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '账户开户场景',
            'ningyin_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return typeMap[type] || type;
    }



    // 获取文件图标类
    function getFileIconClass(filename) {
        if (filename.toLowerCase().includes('.pdf')) {
            return 'file-pdf';
        }
        return 'file-image';
    }

    // 截断文件名
    function truncateFilename(filename, maxLength) {
        if (filename.length <= maxLength) return filename;
        const ext = filename.split('.').pop();
        const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'));
        const truncatedName = nameWithoutExt.substring(0, maxLength - ext.length - 4) + '...';
        return truncatedName + '.' + ext;
    }









    // ==================== 分析结果弹窗功能 ====================

    let currentResultRecordId = null;

    // 显示分析结果弹窗
    function showAnalysisResultModal(recordId) {
        console.log('showAnalysisResultModal called with recordId:', recordId);
        currentResultRecordId = recordId;

        // 检查弹窗元素是否存在
        const modalElement = document.getElementById('analysisResultModal');
        if (!modalElement) {
            console.error('Modal element not found!');
            showMessage('弹窗元素未找到', 'error');
            return;
        }

        // 显示弹窗
        try {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
            console.log('Modal shown successfully');
        } catch (error) {
            console.error('Error showing modal:', error);
            showMessage('显示弹窗时出错: ' + error.message, 'error');
            return;
        }

        // 重置内容
        const contentElement = document.getElementById('analysisResultContent');
        contentElement.innerHTML = `
            <div class="result-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2 text-muted">正在加载分析结果...</div>
            </div>
        `;

        // 隐藏下载按钮
        document.getElementById('downloadResultBtn').style.display = 'none';

        // 加载结果数据
        loadAnalysisResult(recordId);
    }

    // 加载分析结果数据
    function loadAnalysisResult(recordId) {
        console.log('loadAnalysisResult called with recordId:', recordId);
        const apiUrl = `/api/records/${recordId}`;
        console.log('Fetching from URL:', apiUrl);

        fetch(apiUrl)
            .then(response => {
                console.log('API response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('API response data:', data);
                if (data.success && data.data) {
                    console.log('Calling displayAnalysisResult with data:', data.data);
                    displayAnalysisResult(data.data);
                } else {
                    console.error('API returned error:', data.message);
                    showResultError(data.message || '获取分析结果失败');
                }
            })
            .catch(error => {
                console.error('加载分析结果失败:', error);
                showResultError('网络错误，请重试');
            });
    }

    // 显示分析结果
    function displayAnalysisResult(record) {
        const contentElement = document.getElementById('analysisResultContent');

        // 更新弹窗标题
        document.getElementById('analysisResultModalLabel').innerHTML = `
            <i class="bi bi-eye me-2"></i>分析结果 - ${record.filename}
        `;

        // 显示下载按钮
        document.getElementById('downloadResultBtn').style.display = 'inline-block';

        let resultHtml = `
            <div class="result-content">
                <!-- 基本信息 -->
                <div class="result-section">
                    <div class="result-section-header">
                        <i class="bi bi-info-circle me-2"></i>基本信息
                    </div>
                    <div class="result-section-body">
                        <div class="result-field">
                            <div class="result-field-label">文件名称：</div>
                            <div class="result-field-value">${record.filename}</div>
                        </div>
                        <div class="result-field">
                            <div class="result-field-label">分析类型：</div>
                            <div class="result-field-value">${getAnalysisTypeText(record.analysis_type)}</div>
                        </div>
                        <div class="result-field">
                            <div class="result-field-label">分析状态：</div>
                            <div class="result-field-value">
                                <span class="result-status ${getStatusClass(record.status)}">${getStatusText(record.status)}</span>
                            </div>
                        </div>
                        <div class="result-field">
                            <div class="result-field-label">创建时间：</div>
                            <div class="result-field-value">${new Date(record.created_at).toLocaleString('zh-CN')}</div>
                        </div>
                        ${record.accuracy_score !== null ? `
                        <div class="result-field">
                            <div class="result-field-label">准确率：</div>
                            <div class="result-field-value">${(record.accuracy_score * 100).toFixed(1)}%</div>
                        </div>
                        ` : ''}
                    </div>
                </div>
        `;

        // AI分析结果
        if (record.ai_result) {
            try {
                const aiResult = typeof record.ai_result === 'string' ? JSON.parse(record.ai_result) : record.ai_result;
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-robot me-2"></i>AI分析结果
                        </div>
                        <div class="result-section-body">
                            ${formatAIResult(aiResult)}
                        </div>
                    </div>
                `;
            } catch (e) {
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-robot me-2"></i>AI分析结果
                        </div>
                        <div class="result-section-body">
                            <div class="result-json">${record.ai_result}</div>
                        </div>
                    </div>
                `;
            }
        }

        // 预期正确结果（可编辑）
        if (record.expected_result) {
            try {
                const expectedResult = typeof record.expected_result === 'string' ? JSON.parse(record.expected_result) : record.expected_result;
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-check-circle me-2"></i>预期正确结果
                            <button class="btn btn-sm btn-outline-primary ms-auto" onclick="editExpectedResult(${record.id})" title="编辑预期结果">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        </div>
                        <div class="result-section-body" id="expectedResultSection_${record.id}">
                            ${formatExpectedResult(expectedResult, record.id)}
                        </div>
                    </div>
                `;
            } catch (e) {
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-check-circle me-2"></i>预期正确结果
                            <button class="btn btn-sm btn-outline-primary ms-auto" onclick="editExpectedResult(${record.id})" title="编辑预期结果">
                                <i class="bi bi-pencil me-1"></i>编辑
                            </button>
                        </div>
                        <div class="result-section-body" id="expectedResultSection_${record.id}">
                            <div class="result-json">${record.expected_result}</div>
                        </div>
                    </div>
                `;
            }
        }

        // 对比结果
        if (record.comparison_result) {
            try {
                const comparisonResult = typeof record.comparison_result === 'string' ? JSON.parse(record.comparison_result) : record.comparison_result;
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-arrow-left-right me-2"></i>对比分析
                        </div>
                        <div class="result-section-body">
                            ${formatComparisonResult(comparisonResult)}
                        </div>
                    </div>
                `;
            } catch (e) {
                resultHtml += `
                    <div class="result-section">
                        <div class="result-section-header">
                            <i class="bi bi-arrow-left-right me-2"></i>对比分析
                        </div>
                        <div class="result-section-body">
                            <div class="result-json">${record.comparison_result}</div>
                        </div>
                    </div>
                `;
            }
        }

        resultHtml += '</div>';
        contentElement.innerHTML = resultHtml;
    }

    // 格式化AI分析结果
    function formatAIResult(aiResult) {
        if (aiResult.error) {
            return `<div class="result-error"><i class="bi bi-exclamation-triangle me-2"></i>${aiResult.error}</div>`;
        }

        if (aiResult.result && typeof aiResult.result === 'object') {
            let html = '';
            for (const [key, value] of Object.entries(aiResult.result)) {
                html += `
                    <div class="result-field">
                        <div class="result-field-label">${key}：</div>
                        <div class="result-field-value">${formatValue(value)}</div>
                    </div>
                `;
            }
            return html;
        }

        return `<div class="result-json">${JSON.stringify(aiResult, null, 2)}</div>`;
    }

    // 格式化预期正确结果
    function formatExpectedResult(expectedResult, recordId) {
        if (!expectedResult || typeof expectedResult !== 'object') {
            return '<div class="text-muted text-center p-3">暂无预期结果</div>';
        }

        let html = '';
        for (const [key, value] of Object.entries(expectedResult)) {
            html += `
                <div class="result-field">
                    <div class="result-field-label">${key}：</div>
                    <div class="result-field-value">
                        <span class="editable-field" data-field="${key}" data-record="${recordId}">
                            ${formatValue(value)}
                        </span>
                    </div>
                </div>
            `;
        }
        return html;
    }

    // 格式化对比结果
    function formatComparisonResult(comparisonResult) {
        if (comparisonResult.overall_accuracy !== undefined) {
            let html = `
                <div class="result-field">
                    <div class="result-field-label">整体准确率：</div>
                    <div class="result-field-value">
                        <span class="result-status ${comparisonResult.overall_accuracy > 0.8 ? 'success' : comparisonResult.overall_accuracy > 0.5 ? 'warning' : 'error'}">
                            ${(comparisonResult.overall_accuracy * 100).toFixed(1)}%
                        </span>
                    </div>
                </div>
            `;

            if (comparisonResult.comparison && comparisonResult.comparison.children) {
                html += '<div class="mt-3"><h6>字段对比详情：</h6>';
                html += formatComparisonChildren(comparisonResult.comparison.children);
                html += '</div>';
            }

            return html;
        }

        return `<div class="result-json">${JSON.stringify(comparisonResult, null, 2)}</div>`;
    }

    // 格式化对比子项
    function formatComparisonChildren(children) {
        let html = '';
        for (const [key, value] of Object.entries(children)) {
            if (value.match !== undefined) {
                html += `
                    <div class="result-field">
                        <div class="result-field-label">${key}：</div>
                        <div class="result-field-value">
                            <span class="result-status ${value.match ? 'success' : 'error'}">
                                ${value.match ? '匹配' : '不匹配'}
                            </span>
                            ${value.ai_value ? `<br><small>AI值: ${value.ai_value}</small>` : ''}
                            ${value.expected_value ? `<br><small>期望值: ${value.expected_value}</small>` : ''}
                        </div>
                    </div>
                `;
            }
        }
        return html;
    }

    // 格式化值
    function formatValue(value) {
        if (value === null || value === undefined) {
            return '<span class="text-muted">无</span>';
        }
        if (typeof value === 'object') {
            return `<div class="result-json">${JSON.stringify(value, null, 2)}</div>`;
        }
        return String(value);
    }

    // 获取状态类
    function getStatusClass(status) {
        const statusMap = {
            'completed': 'success',
            'failed': 'error',
            'processing': 'warning',
            'pending': 'warning'
        };
        return statusMap[status] || 'warning';
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'failed': '失败',
            'processing': '处理中',
            'pending': '待处理'
        };
        return statusMap[status] || status;
    }

    // 显示结果错误
    function showResultError(message) {
        const contentElement = document.getElementById('analysisResultContent');
        contentElement.innerHTML = `
            <div class="result-content">
                <div class="result-error">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            </div>
        `;
    }

    // 在新窗口打开结果
    function openResultInNewTab() {
        if (currentResultRecordId) {
            window.open(`/records/${currentResultRecordId}`, '_blank');
        }
    }

    // 下载结果
    function downloadResult() {
        if (currentResultRecordId) {
            // 这里可以实现下载功能
            showMessage('下载功能开发中...', 'info');
        }
    }

    // ==================== 预期结果编辑功能 ====================

    let isEditingExpectedResult = false;
    let originalExpectedResult = null;

    // 编辑预期结果
    function editExpectedResult(recordId) {
        if (isEditingExpectedResult) {
            showMessage('正在编辑中，请先保存或取消当前编辑', 'warning');
            return;
        }

        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        isEditingExpectedResult = true;

        // 获取当前数据
        fetch(`/api/records/${recordId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data && data.data.expected_result) {
                    const expectedResult = typeof data.data.expected_result === 'string' ?
                        JSON.parse(data.data.expected_result) : data.data.expected_result;

                    originalExpectedResult = JSON.parse(JSON.stringify(expectedResult)); // 深拷贝
                    showEditableExpectedResult(expectedResult, recordId);
                } else {
                    showMessage('获取预期结果失败', 'error');
                    isEditingExpectedResult = false;
                }
            })
            .catch(error => {
                console.error('获取预期结果失败:', error);
                showMessage('获取预期结果失败', 'error');
                isEditingExpectedResult = false;
            });
    }

    // 显示可编辑的预期结果
    function showEditableExpectedResult(expectedResult, recordId) {
        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        let html = '<div class="editable-expected-result">';

        for (const [key, value] of Object.entries(expectedResult)) {
            html += `
                <div class="result-field">
                    <div class="result-field-label">${key}：</div>
                    <div class="result-field-value">
                        <input type="text" class="form-control form-control-sm editable-input"
                               data-field="${key}"
                               value="${escapeHtml(String(value))}"
                               placeholder="请输入${key}">
                    </div>
                </div>
            `;
        }

        html += `
            <div class="mt-3 d-flex gap-2">
                <button class="btn btn-sm btn-success" onclick="saveExpectedResult(${recordId})">
                    <i class="bi bi-check me-1"></i>保存
                </button>
                <button class="btn btn-sm btn-secondary" onclick="cancelEditExpectedResult(${recordId})">
                    <i class="bi bi-x me-1"></i>取消
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="addExpectedField(${recordId})">
                    <i class="bi bi-plus me-1"></i>添加字段
                </button>
            </div>
        </div>`;

        section.innerHTML = html;

        // 添加实时保存功能
        section.querySelectorAll('.editable-input').forEach(input => {
            input.addEventListener('blur', () => autoSaveExpectedResult(recordId));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    autoSaveExpectedResult(recordId);
                }
            });
        });
    }

    // 保存预期结果
    function saveExpectedResult(recordId) {
        const section = document.getElementById(`expectedResultSection_${recordId}`);
        if (!section) return;

        const inputs = section.querySelectorAll('.editable-input');
        const newExpectedResult = {};

        inputs.forEach(input => {
            const field = input.dataset.field;
            const value = input.value.trim();
            if (field && value) {
                newExpectedResult[field] = value;
            }
        });

        // 发送保存请求
        fetch(`/api/records/${recordId}/expected-result`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                expected_result: newExpectedResult
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('预期结果保存成功', 'success');
                isEditingExpectedResult = false;
                originalExpectedResult = null;

                // 刷新显示
                section.innerHTML = formatExpectedResult(newExpectedResult, recordId);

                // 更新弹窗标题中的编辑按钮
                const header = section.closest('.result-section').querySelector('.result-section-header button');
                if (header) {
                    header.style.display = 'inline-block';
                }
            } else {
                showMessage(data.message || '保存失败', 'error');
            }
        })
        .catch(error => {
            console.error('保存预期结果失败:', error);
            showMessage('保存失败，请重试', 'error');
        });
    }

    // 自动保存预期结果
    function autoSaveExpectedResult(recordId) {
        if (!isEditingExpectedResult) return;

        // 防抖处理
        clearTimeout(window.autoSaveTimeout);
        window.autoSaveTimeout = setTimeout(() => {
            saveExpectedResult(recordId);
        }, 1000);
    }

    // 取消编辑预期结果
    function cancelEditExpectedResult(recordId) {
        if (originalExpectedResult) {
            const section = document.getElementById(`expectedResultSection_${recordId}`);
            if (section) {
                section.innerHTML = formatExpectedResult(originalExpectedResult, recordId);
            }
        }

        isEditingExpectedResult = false;
        originalExpectedResult = null;

        // 显示编辑按钮
        const header = document.querySelector(`#expectedResultSection_${recordId}`).closest('.result-section').querySelector('.result-section-header button');
        if (header) {
            header.style.display = 'inline-block';
        }
    }

    // 添加预期字段
    function addExpectedField(recordId) {
        const fieldName = prompt('请输入字段名称:');
        if (!fieldName || !fieldName.trim()) return;

        const fieldValue = prompt('请输入字段值:');
        if (fieldValue === null) return; // 用户取消

        const section = document.getElementById(`expectedResultSection_${recordId}`);
        const editableDiv = section.querySelector('.editable-expected-result');
        const buttonsDiv = editableDiv.querySelector('.mt-3');

        // 在按钮前添加新字段
        const newFieldHtml = `
            <div class="result-field">
                <div class="result-field-label">${escapeHtml(fieldName.trim())}：</div>
                <div class="result-field-value">
                    <input type="text" class="form-control form-control-sm editable-input"
                           data-field="${escapeHtml(fieldName.trim())}"
                           value="${escapeHtml(fieldValue)}"
                           placeholder="请输入${escapeHtml(fieldName.trim())}">
                </div>
            </div>
        `;

        buttonsDiv.insertAdjacentHTML('beforebegin', newFieldHtml);

        // 为新字段添加事件监听
        const newInput = editableDiv.querySelector(`input[data-field="${fieldName.trim()}"]`);
        if (newInput) {
            newInput.addEventListener('blur', () => autoSaveExpectedResult(recordId));
            newInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    autoSaveExpectedResult(recordId);
                }
            });
            newInput.focus();
        }
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 查看结果对比（在统一文件管理页面中调用）
    function viewResultComparison(recordId) {
        if (typeof resultComparison !== 'undefined') {
            resultComparison.showModal(recordId);
        } else {
            alert('结果对比功能未加载');
        }
    }



    // 更新文件分析状态
    function updateFileAnalysisStatus(fileId, newStatus) {
        // 更新历史文件列表中的状态
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            historyFile.status = newStatus;
        }

        // 更新当前文件列表中的状态
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) {
                file.status = newStatus;
                break;
            }
        }

        // 刷新文件列表显示
        refreshFileList();
    }

    // 检查是否为重分析
    function checkIfReanalyze(fileId) {
        // 在历史记录中查找
        const historyFile = historyFiles.find(f => f.id === fileId);
        if (historyFile) {
            return ['completed', 'pending_audit', 'pending_review'].includes(historyFile.status);
        }

        // 在当前文件中查找
        const currentFile = getCurrentFileById(fileId);
        if (currentFile) {
            return ['completed', 'pending_audit', 'pending_review'].includes(currentFile.status);
        }

        return false;
    }

    // 获取当前文件
    function getCurrentFileById(fileId) {
        for (const typeFiles of Object.values(filesByType)) {
            const file = typeFiles.find(f => f.fileId === fileId);
            if (file) return file;
        }
        return null;
    }



    // 与文件管理页面一致的辅助函数
    function getStatusClass(status) {
        const classes = {
            'pending': 'status-pending',
            'processing': 'status-processing',
            'analyzing': 'status-processing',
            'completed': 'status-completed',
            'failed': 'status-failed',
            'uploaded': 'status-uploaded',
            'pending_audit': 'status-pending-audit',
            'pending_review': 'status-pending-review'
        };
        return classes[status] || 'status-unknown';
    }

    function getStatusText(status) {
        const texts = {
            'pending': '待处理',
            'processing': '处理中',
            'analyzing': '分析中',
            'completed': '已完成',
            'failed': '失败',
            'uploaded': '已上传',
            'pending_audit': '待审核',
            'pending_review': '待复核'
        };
        return texts[status] || '未知';
    }

    function getStatusIcon(status) {
        const icons = {
            'pending': '<i class="bi bi-clock"></i>',
            'processing': '<i class="bi bi-arrow-repeat"></i>',
            'analyzing': '<i class="bi bi-cpu"></i>',
            'completed': '<i class="bi bi-check-circle"></i>',
            'failed': '<i class="bi bi-x-circle"></i>',
            'uploaded': '<i class="bi bi-upload"></i>',
            'pending_audit': '<i class="bi bi-eye-slash"></i>',
            'pending_review': '<i class="bi bi-search"></i>'
        };
        return icons[status] || '<i class="bi bi-question-circle"></i>';
    }

    function getAccuracyClass(accuracy) {
        if (accuracy >= 0.8) return 'high';
        if (accuracy >= 0.6) return 'medium';
        return 'low';
    }

    function truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    function formatDateTime(dateStr) {
        if (!dateStr) return '-';
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }



    function handleFileSelection(checkbox) {
        const fileId = checkbox.dataset.fileId;
        const isChecked = checkbox.checked;

        // 这里可以添加选择逻辑
    }


</script>

<!-- 分析结果弹窗 -->
<div class="modal fade" id="analysisResultModal" tabindex="-1" aria-labelledby="analysisResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="analysisResultModalLabel">
                    <i class="bi bi-eye me-2"></i>分析结果
                </h5>
                <div class="modal-header-actions">
                    <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="openResultInNewTab()" id="openInNewTabBtn">
                        <i class="bi bi-box-arrow-up-right me-1"></i>新窗口打开
                    </button>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
            </div>
            <div class="modal-body p-0" style="max-height: 70vh; overflow-y: auto;">
                <div id="analysisResultContent">
                    <div class="text-center p-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div class="mt-2 text-muted">正在加载分析结果...</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="downloadResult()" id="downloadResultBtn" style="display: none;">
                    <i class="bi bi-download me-1"></i>下载结果
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 分析类型选择提醒模态框 -->
<div class="modal fade" id="analysisTypeRequiredModal" tabindex="-1" aria-labelledby="analysisTypeRequiredModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="analysisTypeRequiredModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    请选择分析类型
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="bi bi-upload text-muted" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3">
                    您已选择了 <span id="fileCountText" class="fw-bold text-primary"></span> 个文件，但还未选择分析类型。
                </p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    请先选择一个分析类型，然后再上传文件。不同的分析类型会使用不同的AI模型进行处理。
                </div>
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="scrollToAnalysisTypes()" data-bs-dismiss="modal">
                        <i class="bi bi-arrow-down me-2"></i>
                        去选择分析类型
                    </button>
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入结果对比模板 -->
{% include 'result_comparison_modal.html' %}

<script>
    // 等待main.js加载完成后再执行
    document.addEventListener('DOMContentLoaded', function() {
        // 确保API和Utils可用
        if (typeof window.API === 'undefined') {
            console.error('API对象未定义，请检查main.js是否正确加载');
        }

        if (typeof window.Utils === 'undefined') {
            console.error('Utils对象未定义，请检查main.js是否正确加载');
        }

        // 创建showMessage别名，方便使用
        if (typeof window.showMessage === 'undefined') {
            window.showMessage = function(message, type, duration) {
                if (window.Utils && window.Utils.showMessage) {
                    return window.Utils.showMessage(message, type, duration);
                } else {
                    console.log(`Message: ${message} (Type: ${type})`);
                    alert(message);
                }
            };
        }

        console.log('Document analysis page initialized');
        console.log('API available:', typeof window.API !== 'undefined');
        console.log('Utils available:', typeof window.Utils !== 'undefined');
        console.log('jQuery available:', typeof $ !== 'undefined');
    });

    // 将关键函数定义在全局作用域，确保onclick事件可以访问
    window.analyzeFile = function(fileId) {
        console.log('analyzeFile called with fileId:', fileId);

        // 检查是否是历史记录中的文件（数字ID）
        const isHistoryFile = !isNaN(fileId) && Number.isInteger(Number(fileId));

        if (isHistoryFile) {
            // 历史记录文件，直接使用fileId作为recordId调用分析API
            console.log('Analyzing history file, recordId:', fileId);

            // 发送分析请求 - 参考back.html的实现
            API.post(`/api/files/${fileId}/analyze`, {
                analysis_type: selectedAnalysisType,
                use_mock: true // 使用模拟分析，与back.html保持一致
            })
            .then(data => {
                if (data.success) {
                    showMessage('文件分析已开始', 'success');
                    // 刷新历史记录列表
                    if (currentFileView === 'history') {
                        loadHistoryFiles();
                    }
                } else {
                    showMessage('分析失败: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('分析文件失败:', error);
                showMessage('分析文件失败', 'error');
            });
            return;
        }

        // 当前上传的文件处理逻辑
        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        // 查找对应的文件记录
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (!fileRecord) {
            showMessage('未找到文件记录', 'error');
            return;
        }

        // 检查是否为重分析
        const hasResult = fileRecord.recordId && fileRecord.status === 'analyzed';
        const actionText = hasResult ? '重分析' : '分析';

        if (hasResult && !confirm(`确定要重新分析这个文件吗？这将覆盖现有的分析结果。`)) {
            return;
        }

        // 更新文件状态为分析中
        fileRecord.status = 'analyzing';

        // 刷新显示
        if (currentFileView === 'current') {
            displayCurrentFiles();
        }

        showMessage(`正在${actionText}文件：${fileRecord.name}`, 'info');

        // 使用真实的记录ID调用分析API
        if (!fileRecord.recordId) {
            showMessage('文件尚未上传完成，请稍后再试', 'warning');
            return;
        }

        // 发送分析请求 - 参考back.html的实现
        API.post(`/api/files/${fileRecord.recordId}/analyze`, {
            analysis_type: selectedAnalysisType,
            use_mock: true // 使用模拟分析，与back.html保持一致
        })
        .then(data => {
            if (data.success) {
                fileRecord.status = 'analyzed';
                // recordId 已经在上传时设置了，不需要再次设置
                showMessage(`文件 ${fileRecord.name} ${actionText}完成`, 'success');
            } else {
                fileRecord.status = 'failed';
                showMessage(`文件 ${fileRecord.name} ${actionText}失败: ${data.message}`, 'error');
            }

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        })
        .catch(error => {
            console.error(`${actionText}文件失败:`, error);
            fileRecord.status = 'failed';
            showMessage(`${actionText}文件失败`, 'error');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        });
    };

    window.viewResult = function(fileId) {
        console.log('viewResult called with fileId:', fileId);

        // 检查是否是历史记录中的文件（数字ID）
        const isHistoryFile = !isNaN(fileId) && Number.isInteger(Number(fileId));

        if (isHistoryFile) {
            // 历史记录文件，直接使用fileId作为recordId调用弹窗
            console.log('Viewing history file result, recordId:', fileId);

            // 添加调试信息
            console.log('About to call showAnalysisResultModal with recordId:', fileId);

            // 先测试弹窗是否能正常显示
            try {
                showAnalysisResultModal(fileId);
            } catch (error) {
                console.error('Error calling showAnalysisResultModal:', error);
                showMessage('显示分析结果时出错: ' + error.message, 'error');
            }
            return;
        }

        // 当前上传的文件，需要查找recordId
        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        // 查找对应的文件记录
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (fileRecord && fileRecord.recordId) {
            // 调用现有的分析结果显示函数
            console.log('Viewing current file result, recordId:', fileRecord.recordId);
            showAnalysisResultModal(fileRecord.recordId);
        } else {
            // 参考back.html的备用方案：直接获取结果
            console.log('No recordId found, trying API fallback for fileId:', fileId);
            API.get(`/api/files/${fileId}/result`)
                .then(response => {
                    if (response.success && response.data) {
                        // 显示结果（简化版）
                        const result = response.data;
                        const resultText = JSON.stringify(result, null, 2);
                        alert('分析结果:\n' + resultText);
                    } else {
                        showMessage('获取结果失败: ' + response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('获取结果失败:', error);
                    showMessage('暂无分析结果，请先进行分析', 'warning');
                });
        }
    };

    window.deprecateFile = function(fileId) {
        console.log('deprecateFile called with fileId:', fileId);

        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        if (!confirm('确定要废弃这个文件吗？')) {
            return;
        }

        // 查找对应的文件记录
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (fileRecord) {
            fileRecord.status = 'deprecated';
            showMessage(`文件 ${fileRecord.name} 已废弃`, 'success');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        }
    };

    window.restoreFile = function(fileId) {
        console.log('restoreFile called with fileId:', fileId);

        if (!selectedAnalysisType) {
            showMessage('请先选择分析类型', 'warning');
            return;
        }

        // 查找对应的文件记录
        const fileRecords = fileRecordsByType[selectedAnalysisType] || [];
        const fileRecord = fileRecords.find(r => r.fileId === fileId);

        if (fileRecord) {
            // 恢复到之前的状态
            fileRecord.status = fileRecord.recordId ? 'analyzed' : 'pending';
            showMessage(`文件 ${fileRecord.name} 已恢复`, 'success');

            // 刷新显示
            if (currentFileView === 'current') {
                displayCurrentFiles();
            }
        }
    };

    window.deprecateFile = function(fileId) {
        console.log('deprecateFile called with fileId:', fileId);
        try {
            if (window.updateLocalFileStatus) {
                window.updateLocalFileStatus(fileId, 'deprecated');
            }
            if (window.showMessage) {
                window.showMessage('文件已废弃', 'success');
            } else {
                alert('文件已废弃');
            }
        } catch (error) {
            console.error('废弃文件失败:', error);
            if (window.showMessage) {
                window.showMessage('废弃文件失败', 'error');
            } else {
                alert('废弃文件失败: ' + error.message);
            }
        }
    };

    window.restoreFile = function(fileId) {
        console.log('restoreFile called with fileId:', fileId);
        try {
            if (window.updateLocalFileStatus) {
                window.updateLocalFileStatus(fileId, 'active');
            }
            if (window.showMessage) {
                window.showMessage('文件已恢复', 'success');
            } else {
                alert('文件已恢复');
            }
        } catch (error) {
            console.error('恢复文件失败:', error);
            if (window.showMessage) {
                window.showMessage('恢复文件失败', 'error');
            } else {
                alert('恢复文件失败: ' + error.message);
            }
        }
    };

    window.auditFile = function(fileId, action) {
        console.log('auditFile called with fileId:', fileId, 'action:', action);
        if (!confirm(`确定要${action === 'pass' ? '通过' : '拒绝'}这个文件的审核吗？`)) return;

        try {
            if (window.showMessage) {
                window.showMessage(`文件审核${action === 'pass' ? '通过' : '拒绝'}`, 'success');
            } else {
                alert(`文件审核${action === 'pass' ? '通过' : '拒绝'}`);
            }
        } catch (error) {
            console.error('审核文件失败:', error);
            if (window.showMessage) {
                window.showMessage('审核文件失败', 'error');
            } else {
                alert('审核文件失败: ' + error.message);
            }
        }
    };

    // 简化版本的辅助函数，直接在这里定义
    window.updateLocalFileStatus = function(fileId, newStatus) {
        console.log('updateLocalFileStatus called:', fileId, newStatus);
        try {
            const targetStatus = newStatus === 'deprecated' ? 'deprecated' : 'active';

            // 更新历史文件列表中的状态
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    historyFile.file_status = targetStatus;
                }
            }

            // 更新当前文件列表中的状态
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        file.file_status = targetStatus;
                        break;
                    }
                }
            }

            // 立即更新页面显示
            window.updateFileRowStatus(fileId, targetStatus);
        } catch (error) {
            console.error('updateLocalFileStatus error:', error);
        }
    };

    window.updateFileAnalysisStatus = function(fileId, newStatus) {
        console.log('updateFileAnalysisStatus called:', fileId, newStatus);
        try {
            // 更新历史文件列表中的状态
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    historyFile.status = newStatus;
                }
            }

            // 更新当前文件列表中的状态
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        file.status = newStatus;
                        break;
                    }
                }
            }

            // 刷新文件列表显示
            if (window.refreshFileList) {
                window.refreshFileList();
            }
        } catch (error) {
            console.error('updateFileAnalysisStatus error:', error);
        }
    };

    window.updateFileRowStatus = function(fileId, fileStatus) {
        console.log('updateFileRowStatus called:', fileId, fileStatus);
        try {
            const fileRow = document.querySelector(`tr[data-file-id="${fileId}"]`);
            if (fileRow) {
                if (fileStatus === 'deprecated') {
                    fileRow.classList.add('file-deprecated');
                } else {
                    fileRow.classList.remove('file-deprecated');
                }

                // 重新渲染该行的按钮
                const actionsCell = fileRow.querySelector('td:last-child .file-actions');
                if (actionsCell) {
                    const isDeprecated = fileStatus === 'deprecated';
                    const hasResult = false; // 暂时设为false

                    actionsCell.innerHTML = `
                        <button class="btn ${hasResult ? 'btn-reanalyze' : 'btn-analyze'} btn-sm"
                                onclick="analyzeFile(${fileId})"
                                ${!isDeprecated ? '' : 'disabled'}>
                            <i class="bi bi-cpu"></i>
                            ${hasResult ? '重分析' : '分析'}
                        </button>
                        <button class="btn btn-view-result btn-sm"
                                onclick="viewResult(${fileId})"
                                ${hasResult ? '' : 'disabled'}>
                            <i class="bi bi-eye"></i>
                            查看结果
                        </button>
                        ${!isDeprecated ? `
                            <button class="btn btn-deprecate btn-sm"
                                    onclick="deprecateFile(${fileId})">
                                <i class="bi bi-archive"></i>
                                废弃
                            </button>
                        ` : `
                            <button class="btn btn-restore btn-sm"
                                    onclick="restoreFile(${fileId})">
                                <i class="bi bi-arrow-counterclockwise"></i>
                                恢复
                            </button>
                        `}
                    `;
                }
            }
        } catch (error) {
            console.error('updateFileRowStatus error:', error);
        }
    };

    window.checkIfReanalyze = function(fileId) {
        console.log('checkIfReanalyze called:', fileId);
        try {
            // 在历史记录中查找
            if (window.historyFiles) {
                const historyFile = window.historyFiles.find(f => f.id === fileId);
                if (historyFile) {
                    return ['completed', 'pending_audit', 'pending_review'].includes(historyFile.status);
                }
            }

            // 在当前文件中查找
            if (window.filesByType) {
                for (const typeFiles of Object.values(window.filesByType)) {
                    const file = typeFiles.find(f => f.fileId === fileId);
                    if (file) {
                        return ['completed', 'pending_audit', 'pending_review'].includes(file.status);
                    }
                }
            }

            return false;
        } catch (error) {
            console.error('checkIfReanalyze error:', error);
            return false;
        }
    };









    // 动态加载result_comparison.js
    const script = document.createElement('script');
    script.src = "{{ url_for('static', filename='js/result_comparison.js') }}";
    script.onload = function() {
        // 确保resultComparison实例可用
        if (typeof ResultComparison !== 'undefined') {
            window.resultComparison = new ResultComparison();
        }
    };
    document.head.appendChild(script);
</script>
{% endblock %}

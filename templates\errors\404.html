<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面未找到 - {{ SYSTEM_NAME }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .error-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 100%;
            margin: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .error-message {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-2px);
        }
        
        .error-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .suggestions {
            background-color: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }
        
        .suggestions h6 {
            color: #333;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .suggestions ul {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .suggestions li {
            color: #666;
            margin-bottom: 0.5rem;
        }
        
        .back-link {
            position: absolute;
            top: 2rem;
            left: 2rem;
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: rgba(255, 255, 255, 0.8);
            transform: translateX(-5px);
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
            }
            
            .back-link {
                position: static;
                display: inline-block;
                margin-bottom: 2rem;
            }
        }
        
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰 -->
    <div class="floating-shapes">
        <div class="shape">
            <i class="bi bi-file-earmark-text" style="font-size: 3rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-search" style="font-size: 2.5rem;"></i>
        </div>
        <div class="shape">
            <i class="bi bi-question-circle" style="font-size: 3.5rem;"></i>
        </div>
    </div>
    
    <!-- 返回链接 -->
    <a href="javascript:history.back()" class="back-link d-none d-md-block">
        <i class="bi bi-arrow-left me-2"></i>
        返回上一页
    </a>
    
    <div class="error-container">
        <div class="error-icon">
            <i class="bi bi-exclamation-triangle"></i>
        </div>
        
        <div class="error-code">404</div>
        
        <h1 class="error-title">页面未找到</h1>
        
        <p class="error-message">
            抱歉，您访问的页面不存在或已被移动。<br>
            请检查网址是否正确，或尝试以下建议。
        </p>
        
        <div class="suggestions">
            <h6><i class="bi bi-lightbulb me-2"></i>建议您：</h6>
            <ul>
                <li>检查网址拼写是否正确</li>
                <li>返回首页重新导航</li>
                <li>使用搜索功能查找内容</li>
                <li>联系技术支持获取帮助</li>
            </ul>
        </div>
        
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                <i class="bi bi-house me-2"></i>
                返回首页
            </a>
            <button onclick="history.back()" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-2"></i>
                返回上一页
            </button>
            <a href="/help" class="btn btn-outline-primary">
                <i class="bi bi-question-circle me-2"></i>
                帮助中心
            </a>
        </div>
        
        <div class="mt-4">
            <small class="text-muted">
                如果问题持续存在，请联系技术支持：
                <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
            </small>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.error-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
        
        // 记录404错误（可选）
        if (typeof gtag !== 'undefined') {
            gtag('event', 'page_not_found', {
                'page_location': window.location.href,
                'page_title': document.title
            });
        }
        
        // 自动重定向逻辑（可选）
        // setTimeout(() => {
        //     if (confirm('页面未找到，是否返回首页？')) {
        //         window.location.href = '/';
        //     }
        // }, 10000);
    </script>
</body>
</html>

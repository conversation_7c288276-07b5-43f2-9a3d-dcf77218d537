{% extends "base.html" %}

{% block title %}帮助中心 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .help-sidebar {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        height: fit-content;
        position: sticky;
        top: 2rem;
    }
    
    .help-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .help-nav li {
        margin-bottom: 0.5rem;
    }
    
    .help-nav a {
        display: block;
        padding: 0.75rem 1rem;
        color: #6b7280;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .help-nav a:hover,
    .help-nav a.active {
        background-color: #2563eb;
        color: white;
    }
    
    .help-content {
        background-color: white;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .help-section {
        display: none;
        animation: fadeIn 0.3s ease-in-out;
    }
    
    .help-section.active {
        display: block;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .help-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .help-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .help-card h5 {
        color: #2563eb;
        margin-bottom: 1rem;
    }
    
    .step-list {
        counter-reset: step-counter;
        list-style: none;
        padding: 0;
    }
    
    .step-list li {
        counter-increment: step-counter;
        margin-bottom: 1rem;
        padding-left: 3rem;
        position: relative;
    }
    
    .step-list li::before {
        content: counter(step-counter);
        position: absolute;
        left: 0;
        top: 0;
        background-color: #2563eb;
        color: white;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.875rem;
    }
    
    .feature-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
    }
    
    .feature-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        text-align: center;
    }
    
    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }
    
    .faq-item {
        border-bottom: 1px solid #e5e7eb;
        padding: 1rem 0;
    }
    
    .faq-item:last-child {
        border-bottom: none;
    }
    
    .faq-question {
        font-weight: 600;
        color: #374151;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .faq-answer {
        margin-top: 1rem;
        color: #6b7280;
        display: none;
    }
    
    .faq-answer.show {
        display: block;
        animation: fadeIn 0.3s ease-in-out;
    }
    
    .contact-card {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: white;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        margin-top: 2rem;
    }
    
    .search-box {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .search-input {
        border: none;
        background-color: white;
        border-radius: 25px;
        padding: 0.75rem 1.5rem;
        width: 100%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .search-input:focus {
        outline: none;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-question-circle me-2"></i>
        帮助中心
    </h2>
</div>

<!-- 搜索框 -->
<div class="search-box">
    <div class="input-group">
        <input type="text" class="form-control search-input" id="searchInput" 
               placeholder="搜索帮助内容...">
        <button class="btn btn-primary" type="button">
            <i class="bi bi-search"></i>
        </button>
    </div>
</div>

<div class="row">
    <!-- 侧边栏导航 -->
    <div class="col-lg-3">
        <div class="help-sidebar">
            <h5 class="mb-3">帮助目录</h5>
            <ul class="help-nav">
                <li><a href="#overview" class="nav-link active" onclick="showSection('overview')">
                    <i class="bi bi-house me-2"></i>系统概览
                </a></li>
                <li><a href="#getting-started" class="nav-link" onclick="showSection('getting-started')">
                    <i class="bi bi-play-circle me-2"></i>快速开始
                </a></li>
                <li><a href="#file-upload" class="nav-link" onclick="showSection('file-upload')">
                    <i class="bi bi-cloud-upload me-2"></i>文件上传
                </a></li>
                <li><a href="#analysis-types" class="nav-link" onclick="showSection('analysis-types')">
                    <i class="bi bi-diagram-3 me-2"></i>分析类型
                </a></li>
                <li><a href="#results" class="nav-link" onclick="showSection('results')">
                    <i class="bi bi-bar-chart me-2"></i>结果查看
                </a></li>
                <li><a href="#management" class="nav-link" onclick="showSection('management')">
                    <i class="bi bi-gear me-2"></i>系统管理
                </a></li>
                <li><a href="#prompt-management" class="nav-link" onclick="showSection('prompt-management')">
                    <i class="bi bi-pencil-square me-2"></i>提示词管理
                </a></li>
                <li><a href="#faq" class="nav-link" onclick="showSection('faq')">
                    <i class="bi bi-question-circle me-2"></i>常见问题
                </a></li>
                <li><a href="#contact" class="nav-link" onclick="showSection('contact')">
                    <i class="bi bi-envelope me-2"></i>联系我们
                </a></li>
            </ul>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="col-lg-9">
        <div class="help-content">
            <!-- 系统概览 -->
            <div id="overview" class="help-section active">
                <h3>系统概览</h3>
                <p class="lead">{{ SYSTEM_NAME }}是一个基于人工智能的多场景文档分析系统，能够智能识别和分析各种类型的金融文档。</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-file-earmark-text"></i>
                        </div>
                        <h5>智能识别</h5>
                        <p>支持PDF、图片等多种格式的文档智能识别和信息提取</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-diagram-3"></i>
                        </div>
                        <h5>多场景支持</h5>
                        <p>涵盖期货、理财、券商等6种业务场景的专业分析</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="bi bi-shield-check"></i>
                        </div>
                        <h5>质量保证</h5>
                        <p>内置复核机制和准确率评估，确保分析结果的可靠性</p>
                    </div>
                </div>
            </div>
            
            <!-- 快速开始 -->
            <div id="getting-started" class="help-section">
                <h3>快速开始</h3>
                <p>按照以下步骤快速上手使用系统：</p>
                
                <div class="help-card">
                    <h5>第一步：登录系统</h5>
                    <ol class="step-list">
                        <li>访问系统登录页面</li>
                        <li>输入用户名和密码</li>
                        <li>点击"登录"按钮进入系统</li>
                    </ol>
                </div>
                
                <div class="help-card">
                    <h5>第二步：上传文档</h5>
                    <ol class="step-list">
                        <li>在首页选择分析类型</li>
                        <li>点击"选择文件"或拖拽文件到上传区域</li>
                        <li>等待文件上传完成</li>
                    </ol>
                </div>
                
                <div class="help-card">
                    <h5>第三步：开始分析</h5>
                    <ol class="step-list">
                        <li>确认文件信息无误</li>
                        <li>点击"开始分析"按钮</li>
                        <li>等待AI分析完成</li>
                    </ol>
                </div>
                
                <div class="help-card">
                    <h5>第四步：查看结果</h5>
                    <ol class="step-list">
                        <li>分析完成后自动跳转到结果页面</li>
                        <li>查看识别结果和准确率评分</li>
                        <li>可以导出或保存分析报告</li>
                    </ol>
                </div>
            </div>
            
            <!-- 文件上传 -->
            <div id="file-upload" class="help-section">
                <h3>文件上传指南</h3>
                
                <div class="help-card">
                    <h5>支持的文件格式</h5>
                    <ul>
                        <li><strong>PDF文件：</strong>支持标准PDF格式，系统会自动转换为图片进行分析</li>
                        <li><strong>图片文件：</strong>支持PNG、JPG、JPEG、GIF、BMP、TIFF格式</li>
                        <li><strong>文件大小：</strong>单个文件最大支持50MB</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>上传方式</h5>
                    <ul>
                        <li><strong>点击上传：</strong>点击"选择文件"按钮，从本地选择文件</li>
                        <li><strong>拖拽上传：</strong>直接将文件拖拽到上传区域</li>
                        <li><strong>批量上传：</strong>可以同时选择多个文件进行批量上传</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>上传注意事项</h5>
                    <ul>
                        <li>确保文档图像清晰，避免模糊或倾斜</li>
                        <li>PDF文件页数不宜过多，建议单次上传不超过10页</li>
                        <li>文档内容应完整，避免关键信息被遮挡</li>
                        <li>网络不稳定时建议分批上传</li>
                    </ul>
                </div>
            </div>
            
            <!-- 分析类型 -->
            <div id="analysis-types" class="help-section">
                <h3>分析类型说明</h3>
                
                <div class="help-card">
                    <h5><i class="bi bi-graph-up me-2"></i>期货账户分析</h5>
                    <p>专门用于分析期货账户相关文档，提取账户信息、交易记录、资金变动等关键数据。</p>
                    <strong>适用文档：</strong>期货账户开户申请、账户变更通知、交易确认单等
                </div>
                
                <div class="help-card">
                    <h5><i class="bi bi-piggy-bank me-2"></i>理财产品分析</h5>
                    <p>分析理财产品说明书、合同等文档，提取产品信息、收益率、风险等级等要素。</p>
                    <strong>适用文档：</strong>理财产品说明书、投资合同、产品公告等
                </div>
                
                <div class="help-card">
                    <h5><i class="bi bi-calculator me-2"></i>券商计息变更</h5>
                    <p>处理券商计息相关的变更文档，识别利率调整、计息规则变化等信息。</p>
                    <strong>适用文档：</strong>计息通知书、利率调整公告、计息规则说明等
                </div>
                
                <div class="help-card">
                    <h5><i class="bi bi-people me-2"></i>期货交易会员</h5>
                    <p>分析期货交易所会员相关文档，提取会员信息、交易权限、资格变更等数据。</p>
                    <strong>适用文档：</strong>会员申请书、资格证明、权限变更通知等
                </div>
                
                <div class="help-card">
                    <h5><i class="bi bi-receipt me-2"></i>宁银费用变更</h5>
                    <p>专门处理宁夏银行费用相关的变更文档，识别费用标准、收费项目等信息。</p>
                    <strong>适用文档：</strong>费用通知书、收费标准调整公告、费用明细单等
                </div>
                
                <div class="help-card">
                    <h5><i class="bi bi-file-text me-2"></i>非标交易确认单</h5>
                    <p>分析非标准化交易的确认文档，提取交易详情、确认信息、风险提示等内容。</p>
                    <strong>适用文档：</strong>非标交易确认单、风险揭示书、交易协议等
                </div>
            </div>
            
            <!-- 结果查看 -->
            <div id="results" class="help-section">
                <h3>结果查看与管理</h3>
                
                <div class="help-card">
                    <h5>分析结果页面</h5>
                    <ul>
                        <li><strong>识别结果：</strong>显示AI提取的关键信息字段</li>
                        <li><strong>准确率评分：</strong>系统自动计算的识别准确率</li>
                        <li><strong>对比分析：</strong>AI结果与系统数据的对比情况</li>
                        <li><strong>置信度：</strong>每个字段的识别置信度评分</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>记录管理</h5>
                    <ul>
                        <li><strong>历史记录：</strong>查看所有分析历史记录</li>
                        <li><strong>筛选搜索：</strong>按时间、类型、状态等条件筛选</li>
                        <li><strong>批量操作：</strong>支持批量导出、删除等操作</li>
                        <li><strong>标签管理：</strong>为记录添加自定义标签</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>报告导出</h5>
                    <ul>
                        <li><strong>Excel格式：</strong>导出结构化的分析数据</li>
                        <li><strong>PDF报告：</strong>生成完整的分析报告</li>
                        <li><strong>JSON数据：</strong>导出原始的分析数据</li>
                        <li><strong>自定义模板：</strong>使用预设模板生成报告</li>
                    </ul>
                </div>
            </div>
            
            <!-- 系统管理 -->
            <div id="management" class="help-section">
                <h3>系统管理功能</h3>
                <p class="text-muted">以下功能仅限管理员用户使用</p>
                
                <div class="help-card">
                    <h5>用户管理</h5>
                    <ul>
                        <li>添加、编辑、删除用户账户</li>
                        <li>设置用户角色和权限</li>
                        <li>查看用户活动日志</li>
                        <li>批量导入用户数据</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>模型配置</h5>
                    <ul>
                        <li>配置AI模型参数</li>
                        <li>测试模型连接状态</li>
                        <li>切换不同的AI模型</li>
                        <li>监控模型性能指标</li>
                    </ul>
                </div>
                
                <div class="help-card">
                    <h5>系统配置</h5>
                    <ul>
                        <li>修改系统基础设置</li>
                        <li>配置文件上传限制</li>
                        <li>设置分析类型参数</li>
                        <li>查看系统运行状态</li>
                    </ul>
                </div>
            </div>
            
            <!-- 提示词管理部分 -->
            <div class="help-section" id="prompt-management">
                <h3>提示词管理</h3>
                <div class="help-content">
                    <h4>概述</h4>
                    <p>提示词管理功能允许管理员为不同的文档分析场景配置和优化AI大模型所需的提示词（Prompt）。本功能支持提示词的创建、编辑、版本管理和历史追踪。</p>
                    
                    <h4>主要功能</h4>
                    <ul>
                        <li><strong>基础提示词管理</strong>：创建、编辑和删除提示词</li>
                        <li><strong>版本控制</strong>：为每个提示词维护多个版本，支持版本切换</li>
                        <li><strong>在线编辑</strong>：直接在页面上编辑提示词内容</li>
                        <li><strong>默认提示词</strong>：支持初始化系统预设的默认提示词</li>
                    </ul>
                    
                    <h4>使用方法</h4>
                    <ol>
                        <li>
                            <strong>访问提示词管理页面</strong>
                            <p>在系统左侧导航栏中，点击"系统管理" -> "提示词管理"</p>
                        </li>
                        <li>
                            <strong>选择功能类型和提示词类型</strong>
                            <p>从"选择功能类型"下拉框中选择需要管理的业务场景（如期货开户、理财产品等）</p>
                            <p>从"提示词类型"下拉框中选择提示词类型（如系统提示词、用户提示词等）</p>
                            <p>选择完成后，系统会自动加载当前激活的提示词版本</p>
                        </li>
                        <li>
                            <strong>编辑提示词</strong>
                            <p>在编辑区域中直接修改提示词内容</p>
                            <p>修改完成后，点击"保存"按钮，系统会自动创建一个新的版本</p>
                            <p>如果需要撤销修改，点击"重置"按钮恢复到原始内容</p>
                        </li>
                        <li>
                            <strong>创建新版本</strong>
                            <p>点击"新建版本"按钮</p>
                            <p>填写版本名称、版本号和版本说明</p>
                            <p>编辑提示词内容</p>
                            <p>选择是否立即设为当前激活版本</p>
                            <p>点击"创建版本"按钮完成创建</p>
                        </li>
                        <li>
                            <strong>管理版本历史</strong>
                            <p>点击"版本历史"按钮查看所有历史版本</p>
                            <p>在左侧版本列表中选择要查看的版本</p>
                            <p>点击"激活选中版本"按钮可以将选中的版本设为当前激活版本</p>
                        </li>
                        <li>
                            <strong>初始化默认提示词</strong>
                            <p>如果是首次使用或需要重置为系统推荐的提示词，可以点击"初始化默认提示词"按钮</p>
                            <p>系统会自动添加预设的提示词模板</p>
                        </li>
                    </ol>
                    
                    <h4>最佳实践</h4>
                    <ul>
                        <li><strong>版本命名规范</strong>：建议使用语义化的版本号，如v1.0、v1.1等，便于追踪版本变化</li>
                        <li><strong>版本说明</strong>：在创建新版本时，详细描述变更内容和目的，便于后续回溯</li>
                        <li><strong>定期优化</strong>：根据实际使用效果，定期优化提示词内容，提高识别准确率</li>
                        <li><strong>备份重要版本</strong>：对于效果特别好的提示词版本，建议保留并做好标记，避免被意外覆盖</li>
                    </ul>
                    
                    <h4>注意事项</h4>
                    <ul>
                        <li>修改提示词后需要点击"保存"按钮才能生效</li>
                        <li>当前激活的版本无法被删除</li>
                        <li>版本切换会立即影响系统的识别结果，请谨慎操作</li>
                        <li>建议在测试环境中验证新版本提示词的效果后，再在生产环境中激活</li>
                    </ul>
                </div>
            </div>
            
            <!-- 常见问题 -->
            <div id="faq" class="help-section">
                <h3>常见问题</h3>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>为什么上传的文件分析失败？</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>文件分析失败可能有以下原因：</p>
                        <ul>
                            <li>文件格式不支持或文件损坏</li>
                            <li>文档图像质量过低，无法识别</li>
                            <li>文档内容与选择的分析类型不匹配</li>
                            <li>网络连接不稳定导致上传中断</li>
                        </ul>
                        <p>建议检查文件质量和网络连接后重新尝试。</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>如何提高识别准确率？</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>提高识别准确率的建议：</p>
                        <ul>
                            <li>确保文档图像清晰，分辨率足够高</li>
                            <li>避免文档倾斜或变形</li>
                            <li>选择正确的分析类型</li>
                            <li>确保关键信息区域没有遮挡</li>
                            <li>使用标准格式的文档模板</li>
                        </ul>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>忘记密码怎么办？</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>如果忘记密码，可以通过以下方式重置：</p>
                        <ul>
                            <li>在登录页面点击"忘记密码"链接</li>
                            <li>输入注册时的邮箱地址</li>
                            <li>查收邮件中的重置链接</li>
                            <li>按照邮件指引设置新密码</li>
                        </ul>
                        <p>如果仍无法解决，请联系系统管理员。</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>系统支持哪些浏览器？</span>
                        <i class="bi bi-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>系统支持以下现代浏览器：</p>
                        <ul>
                            <li>Chrome 80+</li>
                            <li>Firefox 75+</li>
                            <li>Safari 13+</li>
                            <li>Edge 80+</li>
                        </ul>
                        <p>建议使用最新版本的浏览器以获得最佳体验。</p>
                    </div>
                </div>
            </div>
            
            <!-- 联系我们 -->
            <div id="contact" class="help-section">
                <h3>联系我们</h3>
                <p>如果您在使用过程中遇到问题，或有任何建议，欢迎通过以下方式联系我们：</p>
                
                <div class="contact-card">
                    <h4>技术支持</h4>
                    <div class="row mt-4">
                        <div class="col-md-4 text-center">
                            <i class="bi bi-envelope display-4 mb-3"></i>
                            <h6>邮箱支持</h6>
                            <p><EMAIL></p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-telephone display-4 mb-3"></i>
                            <h6>电话支持</h6>
                            <p>************</p>
                        </div>
                        <div class="col-md-4 text-center">
                            <i class="bi bi-chat-dots display-4 mb-3"></i>
                            <h6>在线客服</h6>
                            <p>工作日 9:00-18:00</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    // 显示指定的帮助部分
    function showSection(sectionId) {
        // 隐藏所有部分
        document.querySelectorAll('.help-section').forEach(section => {
            section.classList.remove('active');
        });
        
        // 显示指定部分
        document.getElementById(sectionId).classList.add('active');
        
        // 更新导航状态
        document.querySelectorAll('.help-nav a').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[onclick="showSection('${sectionId}')"]`).classList.add('active');
        
        // 滚动到顶部
        document.querySelector('.help-content').scrollTop = 0;
    }
    
    // 切换FAQ答案显示
    function toggleFaq(element) {
        const answer = element.nextElementSibling;
        const icon = element.querySelector('i');
        
        if (answer.classList.contains('show')) {
            answer.classList.remove('show');
            icon.classList.remove('bi-chevron-up');
            icon.classList.add('bi-chevron-down');
        } else {
            // 关闭其他FAQ
            document.querySelectorAll('.faq-answer.show').forEach(item => {
                item.classList.remove('show');
            });
            document.querySelectorAll('.faq-question i').forEach(item => {
                item.classList.remove('bi-chevron-up');
                item.classList.add('bi-chevron-down');
            });
            
            // 打开当前FAQ
            answer.classList.add('show');
            icon.classList.remove('bi-chevron-down');
            icon.classList.add('bi-chevron-up');
        }
    }
    
    // 搜索功能
    document.getElementById('searchInput').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const sections = document.querySelectorAll('.help-section');
        
        if (searchTerm === '') {
            // 如果搜索为空，显示概览部分
            showSection('overview');
            return;
        }
        
        let found = false;
        sections.forEach(section => {
            const content = section.textContent.toLowerCase();
            if (content.includes(searchTerm)) {
                if (!found) {
                    showSection(section.id);
                    found = true;
                }
            }
        });
        
        if (!found) {
            Utils.showMessage('未找到相关内容', 'info');
        }
    });
    
    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 检查URL哈希，如果有则显示对应部分
        const hash = window.location.hash.substring(1);
        if (hash && document.getElementById(hash)) {
            showSection(hash);
        }
        
        // 监听哈希变化
        window.addEventListener('hashchange', function() {
            const hash = window.location.hash.substring(1);
            if (hash && document.getElementById(hash)) {
                showSection(hash);
            }
        });
    });
</script>
{% endblock %}

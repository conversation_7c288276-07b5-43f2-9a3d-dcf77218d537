{% extends "base.html" %}

{% block title %}挡板数据管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .mock-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .mock-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .query-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
    }
    
    .json-editor {
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        min-height: 200px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        padding: 1rem;
        background-color: #f8fafc;
    }
    
    .json-editor:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        outline: none;
    }
    
    .filter-section {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .mock-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .code-preview {
        background-color: #1f2937;
        color: #f9fafb;
        border-radius: 8px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        max-height: 300px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-database me-2"></i>
        挡板数据管理
    </h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#mockModal">
        <i class="bi bi-plus-circle me-2"></i>
        添加挡板数据
    </button>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-4">
            <label class="form-label">查询类型</label>
            <select class="form-select" id="queryTypeFilter">
                <option value="">全部类型</option>
                <option value="ai_future">AI-期货账户</option>
                <option value="ai_financial">AI-理财产品</option>
                <option value="ai_broker_interest">AI-券商计息</option>
                <option value="ai_futures_member">AI-期货会员</option>
                <option value="ai_ningyin_fee">AI-宁银费用</option>
                <option value="ai_non_standard_trade">AI-非标交易</option>
                <option value="system_future">系统-期货账户</option>
                <option value="system_financial">系统-理财产品</option>
                <option value="system_broker_interest">系统-券商计息</option>
                <option value="system_futures_member">系统-期货会员</option>
                <option value="system_ningyin_fee">系统-宁银费用</option>
                <option value="system_non_standard_trade">系统-非标交易</option>
            </select>
        </div>
        <div class="col-md-4">
            <label class="form-label">查询关键字</label>
            <input type="text" class="form-control" id="queryKeyFilter" placeholder="输入查询关键字">
        </div>
        <div class="col-md-4 d-flex align-items-end">
            <button class="btn btn-outline-primary me-2" onclick="loadMockData()">
                <i class="bi bi-search me-2"></i>
                搜索
            </button>
            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                <i class="bi bi-arrow-clockwise me-2"></i>
                重置
            </button>
        </div>
    </div>
</div>

<!-- 挡板数据列表 -->
<div class="row" id="mockDataList">
    <div class="col-12 text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载挡板数据...</p>
    </div>
</div>

<!-- 分页 -->
<nav id="paginationContainer" style="display: none;">
    <ul class="pagination justify-content-center" id="pagination">
    </ul>
</nav>

<!-- 挡板数据模态框 -->
<div class="modal fade" id="mockModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-database me-2"></i>
                    <span id="modalTitle">添加挡板数据</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="mockForm">
                    <input type="hidden" id="mockId" name="mock_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="queryKey" name="query_key" required>
                                <label for="queryKey">查询关键字 *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <select class="form-select" id="queryType" name="query_type" required>
                                    <option value="">请选择查询类型</option>
                                    <optgroup label="AI识别结果">
                                        <option value="ai_future">期货账户分析</option>
                                        <option value="ai_financial">理财产品分析</option>
                                        <option value="ai_broker_interest">券商计息变更</option>
                                        <option value="ai_futures_member">期货交易会员</option>
                                        <option value="ai_ningyin_fee">宁银费用变更</option>
                                        <option value="ai_non_standard_trade">非标交易确认单</option>
                                    </optgroup>
                                    <optgroup label="系统查询结果">
                                        <option value="system_future">期货账户查询</option>
                                        <option value="system_financial">理财产品查询</option>
                                        <option value="system_broker_interest">券商计息查询</option>
                                        <option value="system_futures_member">期货会员查询</option>
                                        <option value="system_ningyin_fee">宁银费用查询</option>
                                        <option value="system_non_standard_trade">非标交易查询</option>
                                    </optgroup>
                                </select>
                                <label for="queryType">查询类型 *</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">描述</label>
                        <input type="text" class="form-control" id="description" name="description" placeholder="可选的描述信息">
                    </div>
                    
                    <div class="mb-3">
                        <label for="mockResult" class="form-label">挡板结果 (JSON格式) *</label>
                        <textarea class="json-editor" id="mockResult" name="mock_result" required placeholder='请输入JSON格式的挡板数据，例如：
{
  "status": "success",
  "data": {
    "account_number": "*********",
    "customer_name": "测试客户",
    "balance": 1000000.00
  }
}'></textarea>
                        <div class="form-text">请确保输入的是有效的JSON格式数据</div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            启用此挡板数据
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="validateBtn">
                    <i class="bi bi-check-square me-2"></i>
                    验证JSON
                </button>
                <button type="button" class="btn btn-primary" id="saveBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-eye me-2"></i>
                    挡板数据预览
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>查询关键字:</strong> <span id="previewQueryKey"></span>
                </div>
                <div class="mb-3">
                    <strong>查询类型:</strong> <span id="previewQueryType"></span>
                </div>
                <div class="mb-3">
                    <strong>描述:</strong> <span id="previewDescription"></span>
                </div>
                <div class="mb-3">
                    <strong>挡板结果:</strong>
                    <div class="code-preview" id="previewMockResult"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除挡板数据 "<span id="deleteMockKey"></span>" 吗？</p>
                <p class="text-muted small">此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-2"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let currentMockId = null;
    let isEditMode = false;
    let currentPage = 1;
    const perPage = 12;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadMockData();
        initEventListeners();
    });

    // 初始化事件监听器
    function initEventListeners() {
        // 保存按钮
        document.getElementById('saveBtn').addEventListener('click', saveMockData);

        // 验证JSON按钮
        document.getElementById('validateBtn').addEventListener('click', validateJSON);

        // 确认删除按钮
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteMockData);

        // 模态框重置
        document.getElementById('mockModal').addEventListener('hidden.bs.modal', resetForm);

        // 筛选器变化
        document.getElementById('queryTypeFilter').addEventListener('change', loadMockData);
        document.getElementById('queryKeyFilter').addEventListener('input',
            Utils.debounce(loadMockData, 500));
    }

    // 加载挡板数据
    function loadMockData(page = 1) {
        currentPage = page;

        const queryType = document.getElementById('queryTypeFilter').value;
        const queryKey = document.getElementById('queryKeyFilter').value;

        const params = {
            page: page,
            per_page: perPage
        };

        if (queryType) params.query_type = queryType;
        if (queryKey) params.query_key = queryKey;

        API.get('/api/mock-data', params)
            .then(response => {
                if (response.success) {
                    renderMockData(response.data.mock_data);
                    renderPagination(response.data.pagination);
                } else {
                    Utils.showMessage('加载挡板数据失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载挡板数据失败:', error);
                Utils.showMessage('加载挡板数据失败', 'error');
            });
    }

    // 渲染挡板数据
    function renderMockData(mockDataList) {
        const container = document.getElementById('mockDataList');

        if (mockDataList.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-database display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无挡板数据</h5>
                    <p class="text-muted">点击"添加挡板数据"按钮创建第一个挡板数据</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        mockDataList.forEach(mockData => {
            const mockCard = createMockCard(mockData);
            container.appendChild(mockCard);
        });
    }

    // 创建挡板数据卡片
    function createMockCard(mockData) {
        const col = document.createElement('div');
        col.className = 'col-lg-6 col-xl-4 mb-4';

        const typeColor = getTypeColor(mockData.query_type);
        const typeName = getTypeName(mockData.query_type);

        col.innerHTML = `
            <div class="card mock-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-truncate" title="${mockData.query_key}">
                        ${mockData.query_key}
                    </h6>
                    <span class="query-type-badge" style="background-color: ${typeColor}; color: white;">
                        ${typeName}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">描述:</small>
                        <div class="small">${mockData.description || '无描述'}</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">状态:</small>
                        <span class="badge ${mockData.is_active ? 'bg-success' : 'bg-secondary'}">
                            ${mockData.is_active ? '启用' : '禁用'}
                        </span>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">创建时间:</small>
                        <div class="small">${Utils.formatDateTime(mockData.created_at)}</div>
                    </div>

                    <div class="mock-actions">
                        <button class="btn btn-sm btn-info" onclick="previewMockData(${mockData.id})">
                            <i class="bi bi-eye me-1"></i>预览
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editMockData(${mockData.id})">
                            <i class="bi bi-pencil me-1"></i>编辑
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="confirmDelete(${mockData.id}, '${mockData.query_key}')">
                            <i class="bi bi-trash me-1"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        `;

        return col;
    }

    // 获取类型颜色
    function getTypeColor(queryType) {
        const colors = {
            'ai_': '#2563eb',
            'system_': '#059669'
        };

        for (const prefix in colors) {
            if (queryType.startsWith(prefix)) {
                return colors[prefix];
            }
        }

        return '#6b7280';
    }

    // 获取类型名称
    function getTypeName(queryType) {
        const names = {
            'ai_future': 'AI-期货',
            'ai_financial': 'AI-理财',
            'ai_broker_interest': 'AI-券商',
            'ai_futures_member': 'AI-会员',
            'ai_ningyin_fee': 'AI-费用',
            'ai_non_standard_trade': 'AI-非标',
            'system_future': '系统-期货',
            'system_financial': '系统-理财',
            'system_broker_interest': '系统-券商',
            'system_futures_member': '系统-会员',
            'system_ningyin_fee': '系统-费用',
            'system_non_standard_trade': '系统-非标'
        };

        return names[queryType] || queryType;
    }

    // 渲染分页
    function renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        const paginationList = document.getElementById('pagination');

        if (pagination.pages <= 1) {
            container.style.display = 'none';
            return;
        }

        container.style.display = 'block';
        paginationList.innerHTML = '';

        // 上一页
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
        prevItem.innerHTML = `
            <a class="page-link" href="#" onclick="loadMockData(${pagination.page - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;
        paginationList.appendChild(prevItem);

        // 页码
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === pagination.page ? 'active' : ''}`;
            pageItem.innerHTML = `
                <a class="page-link" href="#" onclick="loadMockData(${i})">${i}</a>
            `;
            paginationList.appendChild(pageItem);
        }

        // 下一页
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
        nextItem.innerHTML = `
            <a class="page-link" href="#" onclick="loadMockData(${pagination.page + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;
        paginationList.appendChild(nextItem);
    }

    // 编辑挡板数据
    function editMockData(mockId) {
        API.get('/api/mock-data')
            .then(response => {
                if (response.success) {
                    const mockData = response.data.mock_data.find(m => m.id === mockId);
                    if (mockData) {
                        fillForm(mockData);
                        isEditMode = true;
                        currentMockId = mockId;
                        document.getElementById('modalTitle').textContent = '编辑挡板数据';
                        new bootstrap.Modal(document.getElementById('mockModal')).show();
                    }
                }
            })
            .catch(error => {
                console.error('获取挡板数据失败:', error);
                Utils.showMessage('获取挡板数据失败', 'error');
            });
    }

    // 填充表单
    function fillForm(mockData) {
        document.getElementById('mockId').value = mockData.id;
        document.getElementById('queryKey').value = mockData.query_key;
        document.getElementById('queryType').value = mockData.query_type;
        document.getElementById('description').value = mockData.description || '';
        document.getElementById('mockResult').value = JSON.stringify(mockData.mock_result, null, 2);
        document.getElementById('isActive').checked = mockData.is_active;
    }

    // 重置表单
    function resetForm() {
        document.getElementById('mockForm').reset();
        document.getElementById('mockId').value = '';
        document.getElementById('modalTitle').textContent = '添加挡板数据';
        isEditMode = false;
        currentMockId = null;
    }

    // 验证JSON
    function validateJSON() {
        const mockResult = document.getElementById('mockResult').value.trim();

        if (!mockResult) {
            Utils.showMessage('请输入挡板结果', 'warning');
            return;
        }

        try {
            JSON.parse(mockResult);
            Utils.showMessage('JSON格式验证通过', 'success');
        } catch (error) {
            Utils.showMessage('JSON格式错误: ' + error.message, 'error');
        }
    }

    // 保存挡板数据
    function saveMockData() {
        const form = document.getElementById('mockForm');
        const formData = new FormData(form);

        // 验证JSON格式
        const mockResultText = formData.get('mock_result').trim();
        if (!mockResultText) {
            Utils.showMessage('请输入挡板结果', 'warning');
            return;
        }

        let mockResult;
        try {
            mockResult = JSON.parse(mockResultText);
        } catch (error) {
            Utils.showMessage('JSON格式错误: ' + error.message, 'error');
            return;
        }

        const data = {
            query_key: formData.get('query_key'),
            query_type: formData.get('query_type'),
            description: formData.get('description'),
            mock_result: mockResult,
            is_active: formData.get('is_active') === 'on'
        };

        const saveBtn = document.getElementById('saveBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>保存中...';
        saveBtn.disabled = true;

        const apiCall = isEditMode ?
            API.put(`/api/mock-data/${currentMockId}`, data) :
            API.post('/api/mock-data', data);

        apiCall
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('mockModal')).hide();
                    loadMockData(currentPage);
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存挡板数据失败:', error);
                Utils.showMessage('保存挡板数据失败', 'error');
            })
            .finally(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
    }

    // 预览挡板数据
    function previewMockData(mockId) {
        API.get('/api/mock-data')
            .then(response => {
                if (response.success) {
                    const mockData = response.data.mock_data.find(m => m.id === mockId);
                    if (mockData) {
                        document.getElementById('previewQueryKey').textContent = mockData.query_key;
                        document.getElementById('previewQueryType').textContent = getTypeName(mockData.query_type);
                        document.getElementById('previewDescription').textContent = mockData.description || '无描述';
                        document.getElementById('previewMockResult').textContent = JSON.stringify(mockData.mock_result, null, 2);

                        new bootstrap.Modal(document.getElementById('previewModal')).show();
                    }
                }
            })
            .catch(error => {
                console.error('获取挡板数据失败:', error);
                Utils.showMessage('获取挡板数据失败', 'error');
            });
    }

    // 确认删除
    function confirmDelete(mockId, queryKey) {
        currentMockId = mockId;
        document.getElementById('deleteMockKey').textContent = queryKey;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }

    // 删除挡板数据
    function deleteMockData() {
        API.delete(`/api/mock-data/${currentMockId}`)
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    loadMockData(currentPage);
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除挡板数据失败:', error);
                Utils.showMessage('删除挡板数据失败', 'error');
            });
    }

    // 重置筛选器
    function resetFilters() {
        document.getElementById('queryTypeFilter').value = '';
        document.getElementById('queryKeyFilter').value = '';
        loadMockData(1);
    }
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}模型配置管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .model-card {
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .model-card.active {
        border-color: #10b981;
        background-color: #f0fdf4;
    }
    
    .model-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
    }
    
    .status-success {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-failed {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .status-pending {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .response-time {
        font-size: 0.875rem;
        color: #6b7280;
    }
    
    .model-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .form-floating label {
        color: #6b7280;
    }
    
    .form-control:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .test-result {
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 8px;
        display: none;
    }
    
    .test-success {
        background-color: #d1fae5;
        border: 1px solid #10b981;
        color: #065f46;
    }
    
    .test-failed {
        background-color: #fee2e2;
        border: 1px solid #ef4444;
        color: #991b1b;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-cpu me-2"></i>
        模型配置管理
    </h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#modelModal">
        <i class="bi bi-plus-circle me-2"></i>
        添加模型
    </button>
</div>

<!-- 模型列表 -->
<div class="row" id="modelList">
    <div class="col-12 text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载模型配置...</p>
    </div>
</div>

<!-- 模型配置模态框 -->
<div class="modal fade" id="modelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-cpu me-2"></i>
                    <span id="modalTitle">添加模型配置</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="modelForm">
                    <input type="hidden" id="modelId" name="model_id_hidden">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="modelIdInput" name="model_id" required>
                                <label for="modelIdInput">模型标识 *</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="modelName" name="model_name" required>
                                <label for="modelName">模型名称 *</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <input type="url" class="form-control" id="apiUrl" name="api_url" required>
                        <label for="apiUrl">API地址 *</label>
                    </div>
                    
                    <div class="form-floating mb-3">
                        <input type="text" class="form-control" id="apiKey" name="api_key" required>
                        <label for="apiKey">API密钥 *</label>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="text" class="form-control" id="visionModel" name="vision_model">
                                <label for="visionModel">视觉模型名称</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="timeout" name="timeout" value="30" min="5" max="300">
                                <label for="timeout">超时时间(秒)</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="maxTokens" name="max_tokens" value="4096" min="100" max="32000">
                                <label for="maxTokens">最大令牌数</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating mb-3">
                                <input type="number" class="form-control" id="temperature" name="temperature" value="0.7" min="0" max="2" step="0.1">
                                <label for="temperature">温度参数</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active">
                        <label class="form-check-label" for="isActive">
                            设为激活状态
                        </label>
                    </div>
                    
                    <!-- 测试结果显示区域 -->
                    <div id="testResult" class="test-result">
                        <div id="testContent"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="testBtn">
                    <i class="bi bi-wifi me-2"></i>
                    测试连接
                </button>
                <button type="button" class="btn btn-primary" id="saveBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除模型配置 "<span id="deleteModelName"></span>" 吗？</p>
                <p class="text-muted small">此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-2"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let currentModelId = null;
    let isEditMode = false;
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadModels();
        initEventListeners();
    });
    
    // 初始化事件监听器
    function initEventListeners() {
        // 保存按钮
        document.getElementById('saveBtn').addEventListener('click', saveModel);
        
        // 测试连接按钮
        document.getElementById('testBtn').addEventListener('click', testConnection);
        
        // 确认删除按钮
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteModel);
        
        // 模态框重置
        document.getElementById('modelModal').addEventListener('hidden.bs.modal', resetForm);
    }
    
    // 加载模型列表
    function loadModels() {
        API.get('/api/models')
            .then(response => {
                if (response.success) {
                    renderModels(response.data);
                } else {
                    Utils.showMessage('加载模型配置失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载模型配置失败:', error);
                Utils.showMessage('加载模型配置失败', 'error');
            });
    }
    
    // 渲染模型列表
    function renderModels(models) {
        const container = document.getElementById('modelList');
        
        if (models.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-cpu display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无模型配置</h5>
                    <p class="text-muted">点击"添加模型"按钮创建第一个模型配置</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        
        models.forEach(model => {
            const modelCard = createModelCard(model);
            container.appendChild(modelCard);
        });
    }
    
    // 创建模型卡片
    function createModelCard(model) {
        const col = document.createElement('div');
        col.className = 'col-lg-6 col-xl-4 mb-4';
        
        const statusClass = getStatusClass(model.test_status);
        const statusText = getStatusText(model.test_status);
        const responseTime = model.response_time ? `${model.response_time.toFixed(2)}s` : 'N/A';
        
        col.innerHTML = `
            <div class="card model-card ${model.is_active ? 'active' : ''}">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        ${model.is_active ? '<i class="bi bi-check-circle text-success me-2"></i>' : ''}
                        ${model.model_name}
                    </h6>
                    <span class="status-badge ${statusClass}">${statusText}</span>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">模型标识:</small>
                        <div class="fw-medium">${model.model_id}</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">API地址:</small>
                        <div class="small text-truncate">${model.api_url}</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">视觉模型:</small>
                        <div class="small">${model.vision_model || 'N/A'}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">响应时间:</small>
                        <span class="response-time">${responseTime}</span>
                    </div>
                    
                    <div class="model-actions">
                        ${!model.is_active ? `<button class="btn btn-sm btn-success" onclick="activateModel(${model.id})">
                            <i class="bi bi-play-circle me-1"></i>激活
                        </button>` : ''}
                        <button class="btn btn-sm btn-info" onclick="testModel(${model.id})">
                            <i class="bi bi-wifi me-1"></i>测试
                        </button>
                        <button class="btn btn-sm btn-primary" onclick="editModel(${model.id})">
                            <i class="bi bi-pencil me-1"></i>编辑
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="confirmDelete(${model.id}, '${model.model_name}')">
                            <i class="bi bi-trash me-1"></i>删除
                        </button>
                    </div>
                </div>
                ${model.error_message ? `
                <div class="card-footer">
                    <small class="text-danger">
                        <i class="bi bi-exclamation-triangle me-1"></i>
                        ${model.error_message}
                    </small>
                </div>
                ` : ''}
            </div>
        `;
        
        return col;
    }
    
    // 获取状态样式类
    function getStatusClass(status) {
        switch (status) {
            case 'success': return 'status-success';
            case 'failed': return 'status-failed';
            default: return 'status-pending';
        }
    }
    
    // 获取状态文本
    function getStatusText(status) {
        switch (status) {
            case 'success': return '连接正常';
            case 'failed': return '连接失败';
            default: return '未测试';
        }
    }
    
    // 编辑模型
    function editModel(modelId) {
        API.get(`/api/models`)
            .then(response => {
                if (response.success) {
                    const model = response.data.find(m => m.id === modelId);
                    if (model) {
                        fillForm(model);
                        isEditMode = true;
                        currentModelId = modelId;
                        document.getElementById('modalTitle').textContent = '编辑模型配置';
                        document.getElementById('modelIdInput').disabled = true;
                        new bootstrap.Modal(document.getElementById('modelModal')).show();
                    }
                }
            })
            .catch(error => {
                console.error('获取模型信息失败:', error);
                Utils.showMessage('获取模型信息失败', 'error');
            });
    }
    
    // 填充表单
    function fillForm(model) {
        document.getElementById('modelId').value = model.id;
        document.getElementById('modelIdInput').value = model.model_id;
        document.getElementById('modelName').value = model.model_name;
        document.getElementById('apiUrl').value = model.api_url;
        document.getElementById('apiKey').value = model.api_key;
        document.getElementById('visionModel').value = model.vision_model || '';
        document.getElementById('timeout').value = model.timeout;
        document.getElementById('maxTokens').value = model.max_tokens;
        document.getElementById('temperature').value = model.temperature;
        document.getElementById('isActive').checked = model.is_active;
    }
    
    // 重置表单
    function resetForm() {
        document.getElementById('modelForm').reset();
        document.getElementById('modelId').value = '';
        document.getElementById('modelIdInput').disabled = false;
        document.getElementById('modalTitle').textContent = '添加模型配置';
        document.getElementById('testResult').style.display = 'none';
        isEditMode = false;
        currentModelId = null;
    }
    
    // 保存模型
    function saveModel() {
        const form = document.getElementById('modelForm');
        const formData = new FormData(form);
        
        const data = {
            model_id: formData.get('model_id'),
            model_name: formData.get('model_name'),
            api_url: formData.get('api_url'),
            api_key: formData.get('api_key'),
            vision_model: formData.get('vision_model'),
            timeout: parseInt(formData.get('timeout')),
            max_tokens: parseInt(formData.get('max_tokens')),
            temperature: parseFloat(formData.get('temperature')),
            is_active: formData.get('is_active') === 'on'
        };
        
        const saveBtn = document.getElementById('saveBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>保存中...';
        saveBtn.disabled = true;
        
        const apiCall = isEditMode ? 
            API.put(`/api/models/${currentModelId}`, data) : 
            API.post('/api/models', data);
        
        apiCall
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('modelModal')).hide();
                    loadModels();
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存模型配置失败:', error);
                Utils.showMessage('保存模型配置失败', 'error');
            })
            .finally(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
    }
    
    // 测试连接
    function testConnection() {
        if (!isEditMode && !currentModelId) {
            Utils.showMessage('请先保存模型配置后再测试连接', 'warning');
            return;
        }
        
        const testBtn = document.getElementById('testBtn');
        const originalText = testBtn.innerHTML;
        testBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>测试中...';
        testBtn.disabled = true;
        
        const modelId = currentModelId || document.getElementById('modelId').value;
        
        API.post(`/api/models/${modelId}/test`)
            .then(response => {
                if (response.success) {
                    showTestResult(response.data.test_result);
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('测试连接失败:', error);
                Utils.showMessage('测试连接失败', 'error');
            })
            .finally(() => {
                testBtn.innerHTML = originalText;
                testBtn.disabled = false;
            });
    }
    
    // 显示测试结果
    function showTestResult(result) {
        const testResult = document.getElementById('testResult');
        const testContent = document.getElementById('testContent');
        
        if (result.success) {
            testResult.className = 'test-result test-success';
            testContent.innerHTML = `
                <i class="bi bi-check-circle me-2"></i>
                连接测试成功！响应时间: ${result.response_time?.toFixed(2) || 'N/A'}秒
            `;
        } else {
            testResult.className = 'test-result test-failed';
            testContent.innerHTML = `
                <i class="bi bi-x-circle me-2"></i>
                连接测试失败: ${result.error || '未知错误'}
            `;
        }
        
        testResult.style.display = 'block';
    }
    
    // 激活模型
    function activateModel(modelId) {
        if (confirm('确定要激活此模型配置吗？这将停用其他所有模型。')) {
            API.post(`/api/models/${modelId}/activate`)
                .then(response => {
                    if (response.success) {
                        Utils.showMessage(response.message, 'success');
                        loadModels();
                    } else {
                        Utils.showMessage(response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('激活模型失败:', error);
                    Utils.showMessage('激活模型失败', 'error');
                });
        }
    }
    
    // 测试指定模型
    function testModel(modelId) {
        API.post(`/api/models/${modelId}/test`)
            .then(response => {
                if (response.success) {
                    const result = response.data.test_result;
                    if (result.success) {
                        Utils.showMessage(`测试成功！响应时间: ${result.response_time?.toFixed(2) || 'N/A'}秒`, 'success');
                    } else {
                        Utils.showMessage(`测试失败: ${result.error}`, 'error');
                    }
                    loadModels(); // 刷新列表以显示最新测试结果
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('测试模型失败:', error);
                Utils.showMessage('测试模型失败', 'error');
            });
    }
    
    // 确认删除
    function confirmDelete(modelId, modelName) {
        currentModelId = modelId;
        document.getElementById('deleteModelName').textContent = modelName;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
    
    // 删除模型
    function deleteModel() {
        API.delete(`/api/models/${currentModelId}`)
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    loadModels();
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除模型失败:', error);
                Utils.showMessage('删除模型失败', 'error');
            });
    }
</script>
{% endblock %}

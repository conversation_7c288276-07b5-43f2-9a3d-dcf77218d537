{% extends "base.html" %}

{% block title %}提示词管理 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}提示词管理{% endblock %}

{% block extra_css %}
<style>
  .card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(30,58,138,0.06);
    margin-bottom: 24px;
  }
  
  .card-title {
    padding: 16px 20px;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 700;
    font-size: 1.1rem;
    color: #1e3a8a;
  }
  
  .prompt-selectors {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
    align-items: end;
    flex-wrap: wrap;
  }
  
  .prompt-type-selector, .version-selector {
    flex-grow: 1;
    min-width: 200px;
  }
  
  .version-actions {
    display: flex;
    gap: 8px;
  }
  
  .version-info {
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    border-left: 4px solid #2196f3;
    transition: all 0.3s ease;
  }
  
  .version-info:hover {
    box-shadow: 0 6px 16px rgba(0,0,0,0.08);
    transform: translateY(-2px);
  }
  
  .prompt-editor {
    padding: 16px;
  }
  
  .prompt-section {
    margin-bottom: 24px;
  }
  
  .prompt-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
  }
  
  /* 骨架屏样式 */
  .skeleton-line {
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #2563eb;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* 版本历史模态框样式 */
  .version-row {
    border-bottom: 1px solid #e5e7eb;
    padding: 12px;
    transition: all 0.2s ease;
  }
  
  .version-row:hover {
    background-color: #f9fafb;
  }
  
  .version-row.active {
    background-color: #dbeafe;
  }
  
  .version-number {
    font-weight: 700;
    color: #2563eb;
  }
  
  .version-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    color: white;
    background-color: #10b981;
    margin-left: 8px;
  }
  
  .version-content {
    max-height: 300px;
    overflow-y: auto;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 12px;
    font-family: monospace;
    font-size: 0.9em;
    margin-top: 8px;
    white-space: pre-wrap;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
  <div class="card position-relative">
    <div class="card-title">提示词管理</div>
    <div class="prompt-config-content p-4">
      <!-- 加载中遮罩层 -->
      <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
      </div>
      
      <!-- 功能类型和版本选择器 -->
      <div class="prompt-selectors">
        <div class="prompt-type-selector">
          <label class="fw-bold mb-2 d-block">选择功能类型：</label>
          <select id="promptTypeSelect" class="form-select" onchange="loadPromptConfig()">
            <option value="future">开户文件解析</option>
            <option value="financial">理财产品说明书</option>
            <option value="broker_interest">券商账户计息变更</option>
            <option value="futures_member">非标交易确认单解析</option>
            <option value="ningyin_fee">宁银理财费用变更</option>
            <option value="product_manual">账户开户场景</option>
          </select>
        </div>

        <div class="version-selector">
          <label class="fw-bold mb-2 d-block">选择版本：</label>
          <select id="versionSelect" class="form-select" onchange="loadVersionContent()">
            <option value="">请先选择功能类型</option>
          </select>
        </div>

        <div class="version-actions">
          <button class="btn btn-success" onclick="showCreateVersionModal()">
            <i class="bi bi-plus-lg"></i> 新建版本
          </button>
          <button class="btn btn-secondary" onclick="showVersionHistory()">
            <i class="bi bi-list-ul"></i> 版本历史
          </button>
        </div>
      </div>

      <!-- 当前版本信息 -->
      <div id="currentVersionInfo" class="version-info" style="display: none;">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <span class="fw-bold text-primary">当前使用版本：</span>
            <span id="currentVersionNumber" class="fw-bold text-primary fs-5"></span>
            <span id="currentVersionName" class="ms-3 text-muted"></span>
          </div>
          <div class="fs-sm text-muted">
            <span>使用次数：</span><span id="currentVersionUsage">0</span>
            <span class="ms-3">最后使用：</span><span id="currentVersionLastUsed">从未使用</span>
          </div>
        </div>
        <div id="currentVersionDescription" class="mt-2 fst-italic text-muted"></div>
      </div>

      <div class="prompt-editor">
        <div class="prompt-section">
          <label class="fw-bold mb-2 d-block">当前提示词：</label>
          <textarea id="systemPrompt" class="form-control" style="height: 400px; font-family: monospace;" 
            placeholder="请输入完整的当前提示词，包括基本规则、输出格式、具体任务、字段要求、业务逻辑等..."></textarea>
        </div>

        <!-- 版本备注区域 -->
        <div class="prompt-section">
          <label class="fw-bold mb-2 d-block">版本更新说明：</label>
          <textarea id="versionNote" class="form-control" style="height: 80px;"
            placeholder="请描述本次更新的内容和改进点（可选）..."></textarea>
        </div>

        <div class="prompt-actions">
          <button class="btn btn-primary" onclick="savePromptConfig()">
            <i class="bi bi-save"></i> 保存为新版本
          </button>
          <button class="btn btn-secondary" onclick="updateCurrentVersion()">
            <i class="bi bi-pencil-square"></i> 更新当前版本
          </button>
          <button class="btn btn-outline-secondary" onclick="resetPromptConfig()">
            <i class="bi bi-arrow-repeat"></i> 重置为默认
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 创建版本模态框 -->
<div class="modal fade" id="createVersionModal" tabindex="-1" aria-labelledby="createVersionModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="createVersionModalLabel">
          <i class="bi bi-plus-circle"></i> 创建新版本
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="createVersionForm">
          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="newVersionNumber" class="form-label fw-bold">版本号 <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="newVersionNumber" required 
                     placeholder="请输入完整版本号（必填）"
                     oninvalid="this.setCustomValidity('请输入版本号，这将完整保存到数据库')"
                     oninput="this.setCustomValidity('')">
              <small class="text-muted">您输入的版本号将完全保留，不会被修改</small>
            </div>
            <div class="col-md-6 mb-3">
              <label for="newVersionName" class="form-label fw-bold">版本名称 <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="newVersionName" required 
                     placeholder="请输入完整版本名称（必填）"
                     oninvalid="this.setCustomValidity('请输入版本名称，这将完整保存到数据库')"
                     oninput="this.setCustomValidity('')">
              <small class="text-muted">您输入的版本名称将完全保留，不会被修改</small>
            </div>
          </div>
          
          <div class="mb-3">
            <label for="newVersionType" class="form-label fw-bold">提示词类型 <span class="text-danger">*</span></label>
            <select class="form-select" id="newVersionType" required>
              <option value="system_prompt">系统提示词</option>
              <option value="img_prompt_1">图片提示词1</option>
              <option value="img_prompt_2">图片提示词2</option>
            </select>
          </div>
          
          <div class="mb-3">
            <label for="newVersionPrompt" class="form-label fw-bold">提示词内容 <span class="text-danger">*</span></label>
            <textarea class="form-control" id="newVersionPrompt" rows="10" required placeholder="请输入提示词内容..."></textarea>
          </div>
          
          <div class="mb-3">
            <label for="newVersionDescription" class="form-label fw-bold">版本描述</label>
            <textarea class="form-control" id="newVersionDescription" rows="3" placeholder="描述此版本的改进内容和变更点..."></textarea>
          </div>
          
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="setAsActive" checked>
            <label class="form-check-label" for="setAsActive">
              设为当前激活版本
            </label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="createVersionBtn" onclick="createVersion()">创建版本</button>
      </div>
    </div>
  </div>
</div>

<!-- 版本历史模态框 -->
<div class="modal fade" id="versionHistoryModal" tabindex="-1" aria-labelledby="versionHistoryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header bg-secondary text-white">
        <h5 class="modal-title" id="versionHistoryModalLabel">
          <i class="bi bi-clock-history"></i> 版本历史
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- 版本历史加载中遮罩 -->
        <div id="historyLoadingOverlay" class="d-flex justify-content-center p-5" style="display: none;">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
        </div>
        
        <!-- 版本列表 -->
        <div class="row">
          <!-- 版本列表面板 -->
          <div class="col-md-4 border-end">
            <div class="list-group" id="versionsList">
              <!-- 版本列表将动态加载 -->
            </div>
          </div>
          
          <!-- 版本详情面板 -->
          <div class="col-md-8">
            <div class="version-details p-3" id="versionDetails">
              <div class="text-center text-muted p-5">
                <i class="bi bi-arrow-left-circle fs-1"></i>
                <p class="mt-3">请从左侧选择一个版本查看详情</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  // 全局变量
  let allVersions = [];
  let currentAnalysisType = '';
  let versionModal = null;
  let historyModal = null;
  
  // 硬编码默认提示词函数
  function getHardcodedDefaultPrompts() {
    return {
      'future': `你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用\`交易编码对应名称\`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
\`\`\`json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
\`\`\``,
      
      'financial': `请从图片中提取理财产品说明书的相关信息，严格按照JSON格式输出：

请以JSON格式输出，格式如下：
\`\`\`json
{
  "产品信息": {
    "产品名称": "产品名称",
    "产品代码": "产品代码",
    "产品类型": "产品类型",
    "管理人": "管理人",
    "托管人": "托管人",
    "登记备案": "登记备案",
    "成立日期": "成立日期",
    "产品期限": "产品期限",
    "投资范围": "投资范围",
    "预警线": "预警线",
    "止损线": "止损线"
  },
  "费率结构": {
    "认购费": "认购费",
    "申购费": "申购费",
    "赎回费": "赎回费",
    "销售服务费": "销售服务费",
    "固定管理费": "固定管理费",
    "浮动管理费": "浮动管理费",
    "托管费": "托管费"
  },
  "业绩比较基准": "业绩比较基准",
  "估值方法": "估值方法",
  "开放期安排": "开放期安排"
}
\`\`\``,
      
      'broker_interest': `请从图片中提取券商账户计息变更的相关信息，严格按照以下格式输出JSON数组：

=====================【必须提取的字段】=====================
1. **产品名称**：具体的产品或资产名称
2. **产品类别**：区分是"单产品"还是"全公司产品"等
3. **利率(年化)**：
   • 如果是统一利率，格式为 \`{"all": "X.XX%"}\`
   • 如果按客户类型分段，格式为 \`{"个人": "X.XX%", "非个人": "X.XX%"}\`
   • 如果按时间分段，格式为 \`{"START:YYYY-MM-DD": "X.XX%", "YYYY-MM-DD:END": "X.XX%"}\`
4. **开始时间**：变更生效的开始日期（YYYY-MM-DD格式）
5. **截止时间**：变更的截止日期，如无明确截止则填""  
6. **计息天数**：年化计息的天数基础（如360天、365天等）
7. **备注**：其他重要信息

=====================【输出JSON数组示例】=====================
\`\`\`json
[
  {
    "产品名称": "汇添富远景成长一年持有期混合型基金",
    "产品类别": "单产品",
    "利率(年化)": {"all": "1.4%"},
    "开始时间": "2025-01-02",
    "截止时间": "",
    "计息天数": 360,
    "备注": "按月20日前结息至期货账户"
  }
]
\`\`\``,
      
      'ningyin_fee': `你是一名费用变更文件解析专家。请从用户提供的宁银理财费用变更通知书中提取以下信息，并用JSON数组格式返回。

=====================
【必须提取的字段】
1. 产品名称：理财产品的全称
2. 变更类别：例如"管理费率变更"、"托管费率变更"、"估值服务费变更"等
3. 变更前费率：变更前的费率值，如"0.2%"、"0.04%"，不含费率则填"/"
4. 变更后费率：变更后的费率值，如"0.3%"、"0.05%"，不含费率则填"/"
5. 生效日期：费率变更的生效日期（YYYY-MM-DD格式，缺失填"/"）
6. 通知日期：通知发布的日期（YYYY-MM-DD格式，缺失填"/"）

=====================
【输出JSON数组示例】
\`\`\`json
[
  {
    "产品名称": "宁银理财宁欣固定收益12个月持有期第2期",
    "变更类别": "管理费率变更",
    "变更前费率": "0.4%",
    "变更后费率": "0.2%",
    "生效日期": "2025-02-01",
    "通知日期": "2025-01-15"
  },
  {
    "产品名称": "宁银理财宁欣固定收益12个月持有期第2期",
    "变更类别": "托管费率变更",
    "变更前费率": "0.03%",
    "变更后费率": "0.02%",
    "生效日期": "2025-02-01",
    "通知日期": "2025-01-15"
  }
]
\`\`\`

注意：同一个通知中可能包含多个产品的费率变更，或者一个产品多个费率项目的变更，请分别提取为数组的不同项。`,
      
      'non_standard_trade': `你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 投资者名称：通常指代客户姓名，一般是资管计划的名称
2. 投资者账号：通常指代客户的资金账号
3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回
5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填"/"）
8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填"/"）`,
      
      'account_opening': `你是一名账户开户场景解析专家。请从用户提供的开户文件中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 客户名称：开户客户的真实姓名或机构名称
2. 客户类型：个人/机构/产品
3. 证件类型：身份证/护照/营业执照等
4. 证件号码：客户提供的证件号码
5. 联系方式：手机号码或联系电话
6. 联系地址：客户的联系地址
7. 账户类型：需要区分是哪种类型的账户
8. 开户银行：办理开户的银行名称
9. 银行卡号：客户的银行卡号码
10. 开户日期：格式为 YYYY-MM-DD`
    };
  }
  
  // 缓存控制
  const NO_CACHE = {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  };
  
  // 创建提示词类型映射表，解决前端与后端命名不一致的问题
  const typeMapping = {
    'future': 'future',  // 开户文件解析 -> future
    'financial': 'financial', // 理财产品说明书 -> financial
    'broker_interest': 'broker_interest', // 券商账户计息变更 -> broker_interest
    'futures_member': 'non_standard_trade', // 非标交易确认单解析 -> non_standard_trade (修正)
    'ningyin_fee': 'ningyin_fee', // 宁银理财费用变更 -> ningyin_fee
    'product_manual': 'account_opening' // 账户开户场景 -> account_opening (修正)
  };
  
  // 显示提示词配置面板
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化Bootstrap模态框
    versionModal = new bootstrap.Modal(document.getElementById('createVersionModal'));
    historyModal = new bootstrap.Modal(document.getElementById('versionHistoryModal'));
    
    // 从URL参数中获取分析类型
    const urlParams = new URLSearchParams(window.location.search);
    const analysisType = urlParams.get('type');
    
    if (analysisType) {
      document.getElementById('promptTypeSelect').value = analysisType;
    }
    
    // 清除可能存在的缓存数据
    allVersions = [];
    
    // 显示版本加载中的提示
    const versionSelect = document.getElementById('versionSelect');
    versionSelect.innerHTML = '<option value="">正在加载版本列表...</option>';
    
    // 加载提示词配置
    console.log('页面加载完成，开始加载提示词配置...');
    setTimeout(() => {
      loadPromptConfig();
    }, 100);
  });
  
  // 加载提示词配置
  function loadPromptConfig() {
    const analysisType = document.getElementById('promptTypeSelect').value;
    currentAnalysisType = analysisType;
    
    console.log('当前选择的分析类型:', currentAnalysisType);
    
    // 获取映射后的类型，用于与后端交互
    const mappedType = typeMapping[currentAnalysisType] || currentAnalysisType;
    console.log('映射后的类型:', mappedType);
    
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // 重置版本选择器，避免加载过程中显示旧数据
    const versionSelect = document.getElementById('versionSelect');
    versionSelect.innerHTML = '<option value="">正在加载版本列表...</option>';
    
    // 获取映射后的类型，用于与后端交互
    const mappedAnalysisType = typeMapping[currentAnalysisType] || currentAnalysisType;
    const safeAnalysisType = encodeURIComponent(mappedAnalysisType);
    console.log(`加载提示词版本，原始类型: ${currentAnalysisType}, 映射后类型: ${mappedAnalysisType}`);
    
    // 添加随机参数和时间戳参数防止缓存
    const timestamp = new Date().getTime();
    const randomStr = Math.random().toString(36).substring(2, 15);
    const cacheBreaker = `${timestamp}_${randomStr}`;
    
    console.log(`发送请求到: /api/prompt-versions/${safeAnalysisType}?_=${cacheBreaker}`);
    
    // 完全禁用缓存的fetch请求
    fetch(`/api/prompt-versions/${safeAnalysisType}?_=${cacheBreaker}`, {
      method: 'GET',
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Requested-With': 'XMLHttpRequest',
        'X-Cache-Buster': cacheBreaker
      },
      credentials: 'same-origin' // 包含cookies
    })
      .then(response => response.json())
      .then(data => {
        // 隐藏加载状态
        document.getElementById('loadingOverlay').style.display = 'none';
        
                  if (data.success) {
            console.log('API返回数据:', JSON.stringify(data));
            
            // 检查API返回的数据结构
            if (data.versions && Array.isArray(data.versions)) {
              allVersions = data.versions;
            } else if (data.data && Array.isArray(data.data.versions)) {
              // 兼容可能的嵌套结构
              allVersions = data.data.versions;
            } else {
              // 创建一个空数组作为默认值
              console.error('API返回的数据格式不符合预期，未找到versions数组');
              allVersions = [];
            }
            
            console.log('加载到版本数量:', allVersions.length);
            
            // 检查版本数据的完整性
            if (allVersions.length > 0) {
              console.log('第一个版本信息:', JSON.stringify(allVersions[0]));
              
              // 直接使用API返回的原始数据，不做任何修改或默认值处理
              // 这确保了前端显示的版本信息与数据库中存储的完全一致
              allVersions = allVersions.map(v => {
                // 仅处理布尔值字段，确保它们是布尔类型而非字符串
                return {
                  ...v,  // 保留所有原始字段
                  is_active: v.is_active === true || v.is_active === 'true',
                  is_current: v.is_current === true || v.is_current === 'true'
                };
              });
              
              console.log('数据修复后的第一个版本:', JSON.stringify(allVersions[0]));
            } else {
              console.warn('没有获取到任何版本信息');
            }
            
            // 打印所有版本，便于调试
            console.log('===== 所有版本列表 =====');
            allVersions.forEach((v, i) => {
              console.log(`版本${i+1}: ID=${v.id}, 版本号=${v.version}, 名称=${v.name}, 激活=${v.is_active}, 当前=${v.is_current}`);
            });
          
          // 更新版本选择器
          const versionSelect = document.getElementById('versionSelect');
          versionSelect.innerHTML = '<option value="">请选择版本</option>';
          
          // 添加调试信息
          console.log('API返回的版本数量:', allVersions.length);
          
          // 如果没有版本，添加默认提示词选项
          if (allVersions.length === 0) {
            const defaultOption = document.createElement('option');
            defaultOption.value = 'default';
            defaultOption.textContent = '默认提示词';
            versionSelect.appendChild(defaultOption);
            
            // 自动加载默认提示词
            loadDefaultPrompt();
            return;
          }
          
          // 在页面上显示调试信息
          // 先移除旧的调试信息（如果存在）
          const oldDebug = document.getElementById('debugInfo');
          if (oldDebug) {
            oldDebug.remove();
          }
          
          const debugInfo = document.createElement('div');
          debugInfo.id = 'debugInfo';
          debugInfo.style.display = 'block'; // 默认显示
          debugInfo.style.position = 'fixed';
          debugInfo.style.bottom = '10px';
          debugInfo.style.right = '10px';
          debugInfo.style.backgroundColor = 'rgba(0,0,0,0.8)';
          debugInfo.style.color = 'white';
          debugInfo.style.padding = '10px';
          debugInfo.style.borderRadius = '5px';
          debugInfo.style.zIndex = '9999';
          debugInfo.style.maxHeight = '300px';
          debugInfo.style.overflow = 'auto';
          debugInfo.style.fontFamily = 'monospace';
          debugInfo.style.fontSize = '12px';
          
          // 添加标题和切换按钮
          const debugTitle = document.createElement('div');
          debugTitle.innerHTML = `<strong>版本列表调试信息</strong> <button id="toggleDebug" style="float:right;background:none;border:none;color:white;cursor:pointer;">隐藏</button>`;
          debugInfo.appendChild(debugTitle);
          
          // 添加版本信息
          const debugContent = document.createElement('div');
          debugContent.id = 'debugContent';
          debugContent.style.display = 'block';
          
          // 先显示原始数据，便于调试
          let debugHtml = `
            <div>版本数量: ${allVersions.length}</div>
            <div>时间戳: ${new Date().toISOString()}</div>
            <hr>
            <div style="margin-bottom:10px;font-weight:bold;color:yellow">原始数据:</div>
          `;
          
          // 显示完整的原始数据
          for (let i = 0; i < allVersions.length; i++) {
            const v = allVersions[i];
            debugHtml += `<div style="margin-bottom:5px;word-break:break-all;font-size:10px">
              ${i+1}. 完整数据: ${JSON.stringify(v)}
            </div>`;
          }
          
          debugHtml += `<hr><div style="margin-bottom:10px;font-weight:bold;color:yellow">格式化数据:</div>`;
          
          // 显示格式化的数据
          for (let i = 0; i < allVersions.length; i++) {
            const v = allVersions[i];
            const isActive = v.is_active === true || v.is_active === 'true';
            const versionText = v.version || '未知';
            const nameText = v.name || v.description || '无';
            
            debugHtml += `<div style="margin-bottom:5px;${isActive ? 'color:lime;' : ''}">
              ${i+1}. ID:${v.id || 'undefined'}, 版本:${versionText}, 名称:${nameText}, 激活:${isActive}
            </div>`;
          }
          
          debugContent.innerHTML = debugHtml;
          debugInfo.appendChild(debugContent);
          
          // 添加到页面
          document.body.appendChild(debugInfo);
          
          // 添加切换事件
          document.getElementById('toggleDebug').addEventListener('click', function() {
            const content = document.getElementById('debugContent');
            if (content.style.display === 'none') {
              content.style.display = 'block';
              this.textContent = '隐藏';
            } else {
              content.style.display = 'none';
              this.textContent = '显示';
            }
          });
          
          // 如果有版本数据但显示为空，记录警告
          if (allVersions.length > 0 && document.getElementById('versionSelect').options.length <= 1) {
            console.warn('版本列表存在但下拉框为空，这可能是渲染问题');
          }
          
          // 对版本列表进行排序，优先显示激活版本，然后按ID降序排列
          const sortedVersions = [...allVersions].sort((a, b) => {
            // 激活版本优先显示
            if (a.is_active && !b.is_active) return -1;
            if (!a.is_active && b.is_active) return 1;
            
            // 如果激活状态相同，按创建时间排序（新的在前）
            if (a.created_at && b.created_at) {
              return new Date(b.created_at) - new Date(a.created_at);
            }
            
            // 最后按ID排序（大ID在前，通常是最新创建的）
            return b.id - a.id;
          });
          
          sortedVersions.forEach(version => {
            const option = document.createElement('option');
            option.value = version.id;
            
            // 详细记录版本对象的完整信息，帮助调试
            console.log('版本对象完整信息:', JSON.stringify(version));
            console.log('版本字段详情:');
            console.log('  - id:', version.id);
            console.log('  - version:', version.version);
            console.log('  - name:', version.name);
            console.log('  - description:', version.description);
            console.log('  - is_current:', version.is_current);
            console.log('  - is_default:', version.is_default);
            console.log('  - is_active:', version.is_active);
            
            // 检查版本是否为v1.0，如果是则标记
            const isV10Version = version.version && version.version.toLowerCase().includes('v1.0');
            
            // 使用完整版本信息构建显示文本
            let displayText = '';
            
            // 为激活版本添加特殊标记
            if (version.is_active) {
              displayText += '⭐ '; // 使用星号标记当前激活版本
            }
            
            // 直接使用数据库中的原始版本号，不添加任何默认值
            const versionNumber = version.version;
            displayText += versionNumber;
            console.log(`版本ID=${version.id}的版本号: ${versionNumber}`);
            
            // 使用name字段作为版本名称，这是用户输入的版本名称
            const versionName = version.name;
            
            // 只有当描述存在时才添加分隔符和描述
            if (versionName) {
              displayText += ` - ${versionName}`;
            }
            
            console.log(`版本ID=${version.id}的完整显示文本: ${displayText}`);
            console.log(`版本ID=${version.id}的版本名称: ${versionName}`);
            
            // 如果是v1.0版本，添加默认标记
            if (isV10Version) {
              displayText += ' (默认)';
            }
            
            console.log(`最终显示文本: ${displayText}`);
            
            // 设置选项文本
            option.textContent = displayText;
            
            // 在控制台输出完整信息，便于调试
            console.log('选项文本:', option.textContent);
            
            // 标记当前激活版本
            if (version.is_active) {
              option.selected = true;
              console.log('当前激活版本:', version.version, version.name || version.description);
              // 添加特殊样式
              option.classList.add('fw-bold', 'text-success');
              option.setAttribute('data-is-active', 'true');
            }
            
            // 添加数据属性，便于调试和识别
            option.setAttribute('data-version-id', version.id);
            option.setAttribute('data-version-number', version.version);
            option.setAttribute('data-version-name', version.name || version.description || '');
            
            // 将选项添加到下拉框
            versionSelect.appendChild(option);
          });
          
          // 更新当前版本信息
          const currentVersion = allVersions.find(v => v.is_current);
          if (currentVersion) {
            document.getElementById('currentVersionInfo').style.display = 'block';
            document.getElementById('currentVersionNumber').textContent = currentVersion.version;
            document.getElementById('currentVersionName').textContent = currentVersion.name || '';
            document.getElementById('currentVersionUsage').textContent = currentVersion.usage_count || 0;
            document.getElementById('currentVersionLastUsed').textContent = 
              currentVersion.last_used_at ? new Date(currentVersion.last_used_at).toLocaleString() : '从未使用';
            document.getElementById('currentVersionDescription').textContent = currentVersion.version_description || '';
            
            // 加载当前版本内容
            document.getElementById('systemPrompt').value = currentVersion.prompt || '';
            console.log('已加载当前版本提示词');
          } else {
            document.getElementById('currentVersionInfo').style.display = 'none';
            console.log('未找到当前版本，将加载默认提示词');
            // 如果没有当前版本，自动加载默认提示词
            loadDefaultPrompt();
          }
        } else {
          showMessage('加载版本列表失败: ' + (data.message || '未知错误'), 'error');
        }
      })
      .catch(error => {
        // 隐藏加载状态
        document.getElementById('loadingOverlay').style.display = 'none';
        showMessage('加载版本列表失败: ' + error.message, 'error');
        console.error('加载版本列表失败:', error);
      });
  }
  
  // 加载版本内容
  function loadVersionContent() {
    const versionId = document.getElementById('versionSelect').value;
    console.log('选择的版本ID:', versionId);
    
    if (!versionId) {
      console.log('版本ID为空，加载默认提示词');
      loadDefaultPrompt();
      return;
    }
    
    // 如果选择的是默认提示词
    if (versionId === 'default') {
      console.log('选择默认提示词');
      loadDefaultPrompt();
      return;
    }
    
    // 首先从已加载的版本列表中查找
    const version = allVersions.find(v => v.id == versionId);
    
    // 如果在本地找到，直接使用它的内容
    if (version) {
      console.log('在本地找到版本:', version);
      document.getElementById('systemPrompt').value = version.prompt || '';
      return;
    }
    
    // 否则通过API获取
    console.log('本地未找到版本，通过API获取，版本ID:', versionId);
    
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    const safeAnalysisType = encodeURIComponent(currentAnalysisType);
    const safeVersionId = parseInt(versionId);
    
    console.log(`获取版本内容，类型: ${safeAnalysisType}, 版本ID: ${safeVersionId}`);
    
    fetch(`/api/prompt-versions/${safeAnalysisType}/${safeVersionId}`)
      .then(response => response.json())
      .then(data => {
        // 隐藏加载状态
        document.getElementById('loadingOverlay').style.display = 'none';
        
        if (data.success && data.version) {
          console.log('API返回的版本:', data.version);
          document.getElementById('systemPrompt').value = data.version.prompt;
        } else {
          console.warn('API未找到版本，加载默认提示词');
          loadDefaultPrompt();
        }
      })
      .catch(error => {
        // 隐藏加载状态
        document.getElementById('loadingOverlay').style.display = 'none';
        console.error('加载版本内容失败:', error);
        loadDefaultPrompt();
      });
  }
  
  // 加载默认提示词
  function loadDefaultPrompt() {
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    console.log('请求默认提示词，当前分析类型:', currentAnalysisType);
    
    // 添加测试输出，检查提示词输入框的可写性
    const promptTextarea = document.getElementById('systemPrompt');
    console.log('提示词输入框状态:', {
      readOnly: promptTextarea.readOnly,
      disabled: promptTextarea.disabled,
      visible: promptTextarea.offsetParent !== null
    });
    
    // 创建提示词类型映射表，解决前端与后端命名不一致的问题
    const typeMapping = {
      'future': 'future',  // 开户文件解析 -> future
      'financial': 'financial', // 理财产品说明书 -> financial
      'broker_interest': 'broker_interest', // 券商账户计息变更 -> broker_interest
      'futures_member': 'non_standard_trade', // 非标交易确认单解析 -> non_standard_trade (修正)
      'ningyin_fee': 'ningyin_fee', // 宁银理财费用变更 -> ningyin_fee
      'product_manual': 'account_opening' // 账户开户场景 -> account_opening (修正)
    };
    
    // 获取映射后的类型
    const mappedType = typeMapping[currentAnalysisType] || currentAnalysisType;
    console.log('映射后的类型:', mappedType);
    
    // 创建请求数据
    const requestData = { type: mappedType, reset_only: true };
    console.log('API请求数据:', requestData);
    
    // 打印测试内容到提示词输入框，检查其功能正常
    promptTextarea.value = "正在加载默认提示词...";
    
    // 首先尝试从硬编码提示词中获取
    const hardcodedPrompts = getHardcodedDefaultPrompts();
    if (hardcodedPrompts && hardcodedPrompts[mappedType]) {
      console.log('从硬编码提示词中获取默认提示词');
      promptTextarea.value = hardcodedPrompts[mappedType];
      document.getElementById('loadingOverlay').style.display = 'none';
      showMessage('已加载默认提示词', 'success');
      return;
    }
    
    // 如果硬编码中没有，则通过API获取
    fetch(`/api/direct-prompts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    })
    .then(response => {
      console.log('API响应状态:', response.status);
      return response.json();
    })
    .then(data => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      
      console.log('API返回数据结构:', Object.keys(data));
      console.log('API success值:', data.success);
      console.log('API message值:', data.message);
      console.log('API default_prompts存在:', !!data.default_prompts);
      
      if (data.default_prompts) {
        console.log('默认提示词类型列表:', Object.keys(data.default_prompts));
        
        if (Object.keys(data.default_prompts).length === 0) {
          console.warn('默认提示词对象为空');
        }
        
        // 检查映射类型是否在返回数据中
        if (mappedType in data.default_prompts) {
          const promptValue = data.default_prompts[mappedType];
          console.log(`找到映射类型 ${mappedType} 的提示词，长度:`, promptValue ? promptValue.length : 0);
          console.log('提示词前100个字符:', promptValue ? promptValue.substring(0, 100) : '空');
        } else {
          console.warn(`映射类型 ${mappedType} 不在返回的默认提示词中`);
        }
        
        // 检查原始类型是否在返回数据中
        if (currentAnalysisType in data.default_prompts) {
          const promptValue = data.default_prompts[currentAnalysisType];
          console.log(`找到原始类型 ${currentAnalysisType} 的提示词，长度:`, promptValue ? promptValue.length : 0);
          console.log('提示词前100个字符:', promptValue ? promptValue.substring(0, 100) : '空');
        } else {
          console.warn(`原始类型 ${currentAnalysisType} 不在返回的默认提示词中`);
        }
      } else {
        console.warn('API返回数据中不存在 default_prompts 字段');
      }
      
      if (data.success && data.default_prompts) {
        // 优先尝试使用映射后的类型
        if (data.default_prompts[mappedType]) {
          document.getElementById('systemPrompt').value = data.default_prompts[mappedType];
          console.log('已加载映射类型的默认提示词:', mappedType);
          showMessage('已加载默认提示词', 'success');
        } 
        // 如果映射后的类型不存在，尝试使用原始类型
        else if (data.default_prompts[currentAnalysisType]) {
          document.getElementById('systemPrompt').value = data.default_prompts[currentAnalysisType];
          console.log('已加载原始类型的默认提示词:', currentAnalysisType);
          showMessage('已加载默认提示词', 'success');
        } 
        // 如果都不存在，记录错误
        else {
          // 提取数据中的第一个可用提示词（应急方案）
          const availableTypes = Object.keys(data.default_prompts);
          if (availableTypes.length > 0) {
            const firstType = availableTypes[0];
            document.getElementById('systemPrompt').value = data.default_prompts[firstType];
            console.warn(`未找到匹配的默认提示词，使用 ${firstType} 类型的提示词`);
            showMessage(`已加载 ${firstType} 类型的默认提示词（应急方案）`, 'warning');
          } else {
            document.getElementById('systemPrompt').value = '';
            console.error('未找到任何默认提示词');
            showMessage('未找到默认提示词', 'error');
          }
        }
      } else {
        if (data.available_types && data.available_types.length > 0) {
          console.warn('可用的默认提示词类型:', data.available_types);
        }
        document.getElementById('systemPrompt').value = '';
        console.warn('未找到默认提示词数据');
        showMessage('未找到默认提示词', 'error');
      }
    })
    .catch(error => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      console.error('加载默认提示词失败:', error);
      document.getElementById('systemPrompt').value = '';
      showMessage('加载默认提示词失败: ' + error.message, 'error');
    });
  }
  
  // 保存提示词配置
  function savePromptConfig() {
    const systemPrompt = document.getElementById('systemPrompt').value;
    const versionNote = document.getElementById('versionNote').value;
    
    if (!systemPrompt.trim()) {
      showMessage('请输入当前提示词', 'error');
      return;
    }
    
    // 预填充创建版本表单
    document.getElementById('newVersionNumber').value = `v${new Date().getTime().toString(36).substring(0, 6)}`;
    document.getElementById('newVersionName').value = `${currentAnalysisType} 版本 ${new Date().toLocaleDateString()}`;
    document.getElementById('newVersionType').value = "system_prompt";
    document.getElementById('newVersionPrompt').value = systemPrompt;
    document.getElementById('newVersionDescription').value = versionNote;
    
    // 显示创建版本模态框
    versionModal.show();
  }
  
  // 更新当前版本
  function updateCurrentVersion() {
    const versionId = document.getElementById('versionSelect').value;
    const systemPrompt = document.getElementById('systemPrompt').value;
    
    if (!versionId) {
      showMessage('请先选择一个版本', 'error');
      return;
    }
    
    if (!systemPrompt.trim()) {
      showMessage('请输入当前提示词', 'error');
      return;
    }
    
    if (!confirm('确定要更新当前版本吗？这将会覆盖原有内容。')) {
      return;
    }
    
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // 更新提示词内容
    const updateData = {
      prompt_content: systemPrompt
    };
    
    fetch(`/api/prompts/${versionId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    })
    .then(response => response.json())
    .then(data => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      
      if (data.success) {
        showMessage('更新成功', 'success');
        loadPromptConfig(); // 重新加载配置
      } else {
        showMessage('更新失败: ' + (data.message || '未知错误'), 'error');
      }
    })
    .catch(error => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      showMessage('更新失败: ' + error.message, 'error');
    });
  }
  
  // 重置为默认
  function resetPromptConfig() {
    if (!confirm('确定要重置为默认提示词吗？这将丢失当前未保存的修改。')) {
      return;
    }
    
    // 显示加载状态
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // 创建请求数据
    const requestData = { type: currentAnalysisType };
    console.log('重置为默认，当前分析类型:', currentAnalysisType);
    
    // 首先尝试从硬编码提示词中获取
    const hardcodedPrompts = getHardcodedDefaultPrompts();
    const mappedType = typeMapping[currentAnalysisType] || currentAnalysisType;
    
    if (hardcodedPrompts && hardcodedPrompts[mappedType]) {
      console.log('从硬编码提示词中重置为默认');
      document.getElementById('systemPrompt').value = hardcodedPrompts[mappedType];
      document.getElementById('loadingOverlay').style.display = 'none';
      showMessage('已重置为默认提示词', 'success');
      
      // 清空版本说明
      document.getElementById('versionNote').value = '';
      return;
    }
    
    // 如果硬编码中没有，则通过API获取
    fetch(`/api/direct-prompts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      console.log('重置API返回数据:', data);
      
      if (data.success && data.default_prompts && data.default_prompts[currentAnalysisType]) {
        // 更新提示词内容
        document.getElementById('systemPrompt').value = data.default_prompts[currentAnalysisType];
        showMessage('已重置为默认提示词', 'success');
      } else {
        showMessage('重置失败：未找到默认提示词', 'error');
      }
      
      // 清空版本说明
      document.getElementById('versionNote').value = '';
    })
    .catch(error => {
      // 隐藏加载状态
      document.getElementById('loadingOverlay').style.display = 'none';
      console.error('重置失败:', error);
      showMessage('重置失败: ' + error.message, 'error');
    });
  }
  
  // 创建新版本
  function createVersion() {
    const createButton = document.getElementById('createVersionBtn');
    createButton.disabled = true;
    createButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 创建中...';
    
    const versionData = {
      name: document.getElementById('newVersionName').value,
      version: document.getElementById('newVersionNumber').value,
      type: document.getElementById('newVersionType').value,
      version_description: document.getElementById('newVersionDescription').value,
      prompt: document.getElementById('newVersionPrompt').value,
      set_as_active: document.getElementById('setAsActive').checked
    };
    
    // 详细记录要提交的版本数据
    console.log('提交版本数据:', JSON.stringify(versionData));
    
    // 严格验证必填字段，确保用户输入的值直接被使用
    if (!versionData.name || !versionData.version || !versionData.type || !versionData.prompt) {
      showMessage('请填写所有必填字段，版本号和版本名称不能为空', 'error');
      createButton.disabled = false;
      createButton.textContent = '创建版本';
      return;
    }
    
    // 验证输入是否包含默认值标识符
    if (versionData.name.includes('未命名') || versionData.version.includes('v1.0_') || versionData.name.includes('未知类型')) {
      if (!confirm('检测到可能存在默认值，确定要继续创建吗？')) {
        createButton.disabled = false;
        createButton.textContent = '创建版本';
        return;
      }
    }
    
    // 获取映射后的类型，用于与后端交互
    const mappedAnalysisType = typeMapping[currentAnalysisType] || currentAnalysisType;
    const safeAnalysisType = encodeURIComponent(mappedAnalysisType);
    console.log(`创建新版本，原始类型: ${currentAnalysisType}, 映射后类型: ${mappedAnalysisType}`);
    
    fetch(`/api/prompt-versions/${safeAnalysisType}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(versionData)
    })
    .then(response => response.json())
    .then(data => {
      createButton.disabled = false;
      createButton.textContent = '创建版本';
      
      if (data.success) {
        versionModal.hide();
        showMessage('版本创建成功', 'success');
        
        // 添加延迟，确保服务器处理完成后再刷新
        console.log('版本创建成功，准备重新加载配置...');
        
        // 显示加载状态
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        setTimeout(() => {
          console.log('延迟后强制刷新页面');
          
          // 使用更强的刷新方法 - 完全重载页面
          const timestamp = new Date().getTime();
          const randomStr = Math.random().toString(36).substring(2, 15);
          const cacheBreaker = `${timestamp}_${randomStr}`;
          
          // 强制刷新页面，确保获取最新数据
          const newUrl = window.location.href.split('?')[0] + 
                        '?type=' + encodeURIComponent(currentAnalysisType) + 
                        '&_=' + cacheBreaker;
          
          console.log(`强制刷新到: ${newUrl}`);
          
          // 使用location.replace而不是location.href，防止浏览器返回按钮
          window.location.replace(newUrl);
          
          // 如果上面的方法不起作用，尝试更强力的刷新
          setTimeout(() => {
            window.location.reload(true); // true表示从服务器重新加载，而不是从缓存
          }, 200);
        }, 1000);
        
        document.getElementById('versionNote').value = ''; // 清空备注
      } else {
        showMessage('创建失败: ' + (data.message || '未知错误'), 'error');
        console.error('创建失败:', data);
      }
    })
    .catch(error => {
      createButton.disabled = false;
      createButton.textContent = '创建版本';
      showMessage('创建失败: ' + error.message, 'error');
    });
  }
  
  // 显示创建版本模态框
  function showCreateVersionModal() {
    // 填充表单，使用当前内容
    const systemPrompt = document.getElementById('systemPrompt').value;
    const versionNote = document.getElementById('versionNote').value;
    
    // 生成默认值，但让用户可以完全自定义
    // 使用日期时间作为基础，便于用户修改为有意义的版本号
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const day = now.getDate();
    
    // 生成一个基于日期的随机版本号（但不使用v前缀，让用户完全自定义）
    const randomChars = Math.random().toString(36).substring(2, 7); // 生成随机字符串
    const defaultVersionNumber = `${randomChars}`;
    
    // 生成默认版本名称，包含分析类型和日期
    // 显示的是前端类型名称，而非后端映射类型
    const displayAnalysisType = currentAnalysisType || 'future';
    const defaultVersionName = `${displayAnalysisType} 版本 ${year}/${month}/${day}`;
    
    // 记录准备创建的版本
    console.log(`准备创建新版本，默认值: 版本号=${defaultVersionNumber}, 名称=${defaultVersionName}`);
    
    // 设置表单字段值，但鼓励用户修改
    document.getElementById('newVersionNumber').value = defaultVersionNumber;
    document.getElementById('newVersionName').value = defaultVersionName;
    document.getElementById('newVersionType').value = "system_prompt";
    document.getElementById('newVersionPrompt').value = systemPrompt;
    document.getElementById('newVersionDescription').value = versionNote;
    
    // 显示模态框
    versionModal.show();
  }
  
  // 显示版本历史模态框
  function showVersionHistory() {
    // 显示模态框
    historyModal.show();
    
    // 显示加载状态
    document.getElementById('historyLoadingOverlay').style.display = 'flex';
    
    // 渲染版本列表
    const versionsList = document.getElementById('versionsList');
    versionsList.innerHTML = '';
    
    if (allVersions.length === 0) {
      document.getElementById('historyLoadingOverlay').style.display = 'none';
      versionsList.innerHTML = `
        <div class="text-center p-4">
          <p class="text-muted">暂无版本历史</p>
        </div>
      `;
      return;
    }
    
    // 按版本号排序（降序）
    const sortedVersions = [...allVersions].sort((a, b) => {
      // 尝试提取数字版本号进行比较
      const vA = a.version.replace(/[^0-9.]/g, '');
      const vB = b.version.replace(/[^0-9.]/g, '');
      if (vA && vB) {
        const partsA = vA.split('.').map(Number);
        const partsB = vB.split('.').map(Number);
        for (let i = 0; i < Math.max(partsA.length, partsB.length); i++) {
          const numA = i < partsA.length ? partsA[i] : 0;
          const numB = i < partsB.length ? partsB[i] : 0;
          if (numA !== numB) {
            return numB - numA; // 降序
          }
        }
      }
      // 如果无法比较数字版本，则按时间降序
      return new Date(b.created_at || 0) - new Date(a.created_at || 0);
    });
    
    // 生成版本列表
    sortedVersions.forEach(version => {
      const listItem = document.createElement('a');
      listItem.href = "#";
      listItem.className = `list-group-item list-group-item-action ${version.is_current ? 'active' : ''}`;
      listItem.setAttribute('data-version-id', version.id);
      
      const versionDateStr = version.created_at 
        ? new Date(version.created_at).toLocaleDateString() 
        : '未知日期';
      
      listItem.innerHTML = `
        <div class="d-flex w-100 justify-content-between">
          <h5 class="mb-1">
            ${version.version} - ${version.name || '未命名版本'}
            ${version.is_current ? '<span class="badge bg-success ms-2">当前</span>' : ''}
          </h5>
          <small>${versionDateStr}</small>
        </div>
        <small class="text-muted">
          ${version.version_description ? version.version_description.substring(0, 60) + (version.version_description.length > 60 ? '...' : '') : '无描述'}
        </small>
      `;
      
      listItem.addEventListener('click', function(e) {
        e.preventDefault();
        showVersionDetail(version.id);
        
        // 切换选中状态
        document.querySelectorAll('#versionsList a.active').forEach(el => {
          el.classList.remove('active');
        });
        this.classList.add('active');
      });
      
      versionsList.appendChild(listItem);
    });
    
    document.getElementById('historyLoadingOverlay').style.display = 'none';
    
    // 默认选中第一个版本
    if (sortedVersions.length > 0) {
      showVersionDetail(sortedVersions[0].id);
      versionsList.querySelector('a').classList.add('active');
    }
  }
  
  // 显示版本详情
  function showVersionDetail(versionId) {
    const version = allVersions.find(v => v.id == versionId);
    if (!version) return;
    
    const versionDetails = document.getElementById('versionDetails');
    
    versionDetails.innerHTML = `
      <div class="mb-4">
        <h4 class="mb-3">
          ${version.version}
          ${version.is_current ? '<span class="badge bg-success ms-2">当前版本</span>' : ''}
        </h4>
        <div class="mb-3">
          <strong>名称：</strong> ${version.name || '未命名'}
        </div>
        <div class="mb-3">
          <strong>类型：</strong> ${version.type}
        </div>
        <div class="mb-3">
          <strong>创建时间：</strong> ${version.created_at ? new Date(version.created_at).toLocaleString() : '未知'}
        </div>
        <div class="mb-3">
          <strong>使用次数：</strong> ${version.usage_count || 0}
          ${version.last_used_at ? `<small class="text-muted ms-2">最后使用: ${new Date(version.last_used_at).toLocaleString()}</small>` : ''}
        </div>
        <div class="mb-3">
          <strong>版本描述：</strong>
          <p class="mt-2 fst-italic">${version.version_description || '无描述'}</p>
        </div>
      </div>
      
      <div class="mb-3">
        <h5 class="mb-2">提示词内容：</h5>
        <div class="version-content">${version.prompt || '无内容'}</div>
      </div>
      
      <div class="d-flex gap-2 mt-4">
        ${!version.is_current ? `
          <button class="btn btn-success" onclick="switchToVersion(${version.id})">
            <i class="bi bi-check-circle"></i> 激活此版本
          </button>
        ` : ''}
        
        <button class="btn btn-primary" onclick="useThisVersion(${version.id})">
          <i class="bi bi-pencil-square"></i> 使用此版本内容
        </button>
        
        ${!version.is_current ? `
          <button class="btn btn-danger" onclick="confirmDeleteVersion(${version.id})">
            <i class="bi bi-trash"></i> 删除
          </button>
        ` : ''}
      </div>
    `;
  }
  
  // 切换到选中的版本
  function switchToVersion(versionId) {
    if (!confirm('确定要将此版本设置为当前激活版本吗？')) {
      return;
    }
    
    // 显示加载状态
    document.getElementById('historyLoadingOverlay').style.display = 'flex';
    
    // 确保参数安全编码
    const safeAnalysisType = encodeURIComponent(currentAnalysisType);
    console.log(`激活版本，类型: ${safeAnalysisType}, 版本ID: ${versionId}`);
    
    fetch(`/api/prompt-versions/${safeAnalysisType}/activate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        version_id: versionId,
        reason: '手动切换版本'
      })
    })
    .then(response => response.json())
    .then(data => {
      document.getElementById('historyLoadingOverlay').style.display = 'none';
      
      if (data.success) {
        showMessage('版本切换成功', 'success');
        historyModal.hide();
        loadPromptConfig(); // 重新加载配置
      } else {
        showMessage('版本切换失败: ' + (data.message || '未知错误'), 'error');
      }
    })
    .catch(error => {
      document.getElementById('historyLoadingOverlay').style.display = 'none';
      showMessage('版本切换失败: ' + error.message, 'error');
    });
  }
  
  // 使用选中版本的内容
  function useThisVersion(versionId) {
    // 首先在内存中查找
    const version = allVersions.find(v => v.id == versionId);
    if (version) {
      document.getElementById('systemPrompt').value = version.prompt || '';
      historyModal.hide();
      showMessage('已加载选中版本的内容，您可以编辑后保存为新版本', 'info');
      return;
    }
    
    // 未在内存中找到，从API获取
    console.log('从API获取版本内容，版本ID:', versionId);
    
    // 显示加载状态
    document.getElementById('historyLoadingOverlay').style.display = 'flex';
    
    const safeAnalysisType = encodeURIComponent(currentAnalysisType);
    const safeVersionId = parseInt(versionId);
    
    console.log(`获取版本内容，类型: ${safeAnalysisType}, 版本ID: ${safeVersionId}`);
    
    fetch(`/api/prompt-versions/${safeAnalysisType}/${safeVersionId}`)
      .then(response => response.json())
      .then(data => {
        // 隐藏加载状态
        document.getElementById('historyLoadingOverlay').style.display = 'none';
        
        if (data.success && data.version) {
          console.log('API返回的版本:', data.version);
          document.getElementById('systemPrompt').value = data.version.prompt;
          historyModal.hide();
          showMessage('已加载选中版本的内容，您可以编辑后保存为新版本', 'info');
        } else {
          showMessage('获取版本内容失败：' + (data.message || '未知错误'), 'error');
        }
      })
      .catch(error => {
        // 隐藏加载状态
        document.getElementById('historyLoadingOverlay').style.display = 'none';
        console.error('获取版本内容失败:', error);
        showMessage('获取版本内容失败: ' + error.message, 'error');
      });
  }
  
  // 确认删除版本
  function confirmDeleteVersion(versionId) {
    if (!confirm('确定要删除此版本吗？此操作不可恢复！')) {
      return;
    }
    
    // 显示加载状态
    document.getElementById('historyLoadingOverlay').style.display = 'flex';
    
    // 确保参数安全编码
    const safeAnalysisType = encodeURIComponent(currentAnalysisType);
    const safeVersionId = parseInt(versionId);
    
    console.log(`删除版本，类型: ${safeAnalysisType}, 版本ID: ${safeVersionId}`);
    
    fetch(`/api/prompt-versions/${safeAnalysisType}/${safeVersionId}`, {
      method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
      document.getElementById('historyLoadingOverlay').style.display = 'none';
      
      if (data.success) {
        showMessage('版本删除成功', 'success');
        // 更新全局版本列表
        allVersions = allVersions.filter(v => v.id != versionId);
        // 刷新版本历史模态框
        showVersionHistory();
        loadPromptConfig(); // 重新加载配置
      } else {
        showMessage('删除失败: ' + (data.message || '未知错误'), 'error');
      }
    })
    .catch(error => {
      document.getElementById('historyLoadingOverlay').style.display = 'none';
      showMessage('删除失败: ' + error.message, 'error');
    });
  }
</script>

<!-- 覆盖函数脚本 -->
<script src="{{ url_for('static', filename='js/override_functions.js') }}"></script>
<!-- 硬编码版本数据脚本 -->
<script src="{{ url_for('static', filename='js/hardcoded_versions.js') }}"></script>
<script>
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面加载完成，使用覆盖函数');
        
        // 使用硬编码数据作为备用方案
        if (typeof loadPromptConfigWithHardcoded === 'function') {
            // 延迟执行，确保其他脚本已加载
            setTimeout(() => {
                loadPromptConfigWithHardcoded();
            }, 500);
        }
    });
</script>
{% endblock %}
{% extends "base.html" %}

{% block title %}提示词管理 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}提示词管理{% endblock %}

{% block extra_css %}
<style>
  .stats-card {
    background: white;
    border-radius: 16px;
    padding: 1.75rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(226, 232, 240, 0.8);
  }
  .btn {
    background: #2563eb;
    color: #fff;
    border: 1px solid #2563eb;
  }
  .btn:hover {
    background: #1d4ed8;
  }
  .btn-secondary {
    background: #374151;
    color: #fff;
    border: 1px solid #374151;
  }
  .btn-secondary:hover {
    background: #2563eb;
    color: #fff;
  }
  .btn-danger {
    background: #dc2626;
    color: #fff;
    border: 1px solid #dc2626;
  }
  .btn-danger:hover {
    background: #b91c1c;
  }
  .form-row label {
    color: #2563eb;
  }
  
  /* 表格样式 */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: #fff;
    color: #1f2937;
    border-radius: 8px;
    overflow: hidden;
  }
  th {
    background: #1e3a8a;
    color: #fff;
    padding: 12px;
    text-align: left;
  }
  td {
    padding: 12px;
    border-top: 1px solid #e5e7eb;
  }
  tr:nth-child(even) {
    background: #f9fafb;
  }
  tr:nth-child(odd) {
    background: #fff;
  }
  tr:hover {
    background: #eff6ff;
  }
  
  /* 模态框样式 */
  .modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
  }
  .modal-content {
    background: #fff;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    padding: 20px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.25rem;
    font-weight: 600;
  }
  .close {
    cursor: pointer;
    font-size: 1.5rem;
  }
  .form-row {
    margin-bottom: 15px;
  }
  .form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
  }
  .form-row input, .form-row select, .form-row textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
  }
  .form-row textarea {
    height: 150px;
    resize: vertical;
  }
  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
  }
  
  /* 骨架屏样式 */
  .skeleton-line {
    height: 20px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    margin: 8px 0;
  }
  
  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
  
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #2563eb;
    animation: spin 1s ease-in-out infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="mb-3">
        <label class="me-2 fw-bold" data-i18n="prompt_config.filter_by_function">按主要功能筛选：</label>
        <select id="mainTypeFilter" onchange="filterPromptsByMainType()" class="form-select d-inline-block w-auto">
            <option value="" data-i18n="prompt_config.all_functions">全部功能</option>
            <option value="future" data-i18n="prompt_config.future_account">开户文件解析</option>
            <option value="financial">理财产品说明书</option>
            <option value="broker_interest">券商账户计息变更</option>
            <option value="futures_member">非标交易确认单解析</option>
            <option value="ningyin_fee">宁银理财费用变更</option>
            <option value="product_manual">账户开户场景</option>
        </select>
        <button class="btn ms-3" onclick="showAddModal()" data-i18n="prompt_config.add_prompt">新增提示词</button>
        <button class="btn btn-secondary ms-2" onclick="initDefaultPrompts()" data-i18n="prompt_config.init_default">初始化默认提示词</button>
    </div>
    <div class="card position-relative">
        <!-- 加载中遮罩层 -->
        <div id="loadingOverlay" class="loading-overlay">
            <div class="loading-spinner"></div>
        </div>
        <div class="card-body p-0">
            <table class="table table-hover m-0">
                <thead>
                    <tr>
                        <th data-i18n="prompt_config.table.name">名称</th>
                        <th data-i18n="prompt_config.table.main_function">主要功能</th>
                        <th data-i18n="prompt_config.table.type">类型</th>
                        <th data-i18n="prompt_config.table.content">内容</th>
                        <th data-i18n="prompt_config.table.actions">操作</th>
                    </tr>
                </thead>
                <tbody id="promptTableBody">
                    <!-- 骨架屏占位 -->
                    <tr>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                    </tr>
                    <tr>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                    </tr>
                    <tr>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                        <td><div class="skeleton-line"></div></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 编辑/新增弹窗 -->
<div class="modal" id="promptModal">
    <div class="modal-content">
        <div class="modal-header">
            <span id="modalTitle">新增提示词</span>
            <span class="close" onclick="closeModal()">&times;</span>
        </div>
        <div class="form-row">
            <label>名称</label>
            <input type="text" id="promptName" class="form-control">
        </div>
        <div class="form-row">
            <label>类型</label>
            <input type="text" id="promptType" class="form-control">
        </div>
        <div class="form-row">
            <label>主要功能</label>
            <select id="promptMainType" class="form-control">
                <option value="">请选择</option>
                <option value="future">开户文件解析</option>
                <option value="financial">理财产品说明书</option>
                <option value="broker_interest">券商账户计息变更</option>
                <option value="futures_member">非标交易确认单解析</option>
                <option value="ningyin_fee">宁银理财费用变更</option>
                <option value="product_manual">账户开户场景</option>
            </select>
        </div>
        <div class="form-row">
            <label>内容</label>
            <textarea id="promptContent" class="form-control"></textarea>
        </div>
        <div class="form-actions">
            <button id="saveButton" class="btn" onclick="savePrompt()">保存</button>
            <button class="btn btn-secondary" onclick="closeModal()">取消</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let editingId = null;
    let defaultPrompts = [];
    let allPrompts = [];
    let currentMainTypeFilter = '';
    const MAIN_TYPE_MAP = {
        'future': i18n('prompt_config.types.future_account'),
        'financial': i18n('prompt_config.types.financial_product'),
        'broker_interest': i18n('prompt_config.types.broker_interest'),
        'futures_member': i18n('prompt_config.types.futures_member'),
        'ningyin_fee': i18n('prompt_config.types.ningyin_fee'),
        'product_manual': i18n('prompt_config.types.product_manual')
    };
    
    // 加载提示词
    function loadPrompts() {
        // 显示加载状态
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        fetch('/api/prompts' + (currentMainTypeFilter ? ('?main_type=' + currentMainTypeFilter) : ''))
            .then(res => res.json())
            .then(data => {
                // 隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                
                if (Array.isArray(data)) {
                    allPrompts = data;
                    filterPromptsByMainType();
                    defaultPrompts = data;
                } else {
                    allPrompts = [];
                    renderPromptTable([]);
                }
            })
            .catch(error => {
                // 出错时也隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                showMessage('加载提示词失败: ' + error.message, 'error');
                console.error('加载提示词失败:', error);
            });
    }
    
    function filterPromptsByMainType() {
        const mainType = document.getElementById('mainTypeFilter').value;
        currentMainTypeFilter = mainType;
        let filtered = allPrompts;
        if (mainType) {
            filtered = allPrompts.filter(p => p.main_type === mainType);
        }
        renderPromptTable(filtered);
    }
    
    function renderPromptTable(prompts) {
        const tbody = document.getElementById('promptTableBody');
        if (prompts.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center">暂无提示词</td></tr>';
            return;
        }
        
        // 按main_type分组统计
        const typeStats = {};
        prompts.forEach(p => {
            if (!typeStats[p.main_type]) {
                typeStats[p.main_type] = {};
            }
            if (!typeStats[p.main_type][p.type]) {
                typeStats[p.main_type][p.type] = 0;
            }
            typeStats[p.main_type][p.type]++;
        });
        
        tbody.innerHTML = prompts.map(p => {
            const count = typeStats[p.main_type][p.type];
            const typeDisplay = count > 1 ? `${p.type} (${count}个)` : p.type;
            return `
            <tr>
                <td>${p.name}</td>
                <td>${MAIN_TYPE_MAP[p.main_type] || p.main_type}</td>
                <td>${typeDisplay}</td>
                <td style="max-width:320px;word-break:break-all;">${p.prompt.replace(/\n/g,'<br>')}</td>
                <td>
                    <button class="btn btn-sm btn-secondary edit-btn"
                        data-id="${p.id}"
                        data-name="${encodeURIComponent(p.name)}"
                        data-type="${encodeURIComponent(p.type)}"
                        data-main_type="${encodeURIComponent(p.main_type)}"
                        data-prompt="${encodeURIComponent(p.prompt)}"
                    >编辑</button>
                    <button class="btn btn-sm btn-danger delete-btn" data-id="${p.id}">删除</button>
                </td>
            </tr>
        `}).join('');
    }
    
    // 事件委托
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('promptTableBody').onclick = function(e) {
            if (e.target.classList.contains('edit-btn')) {
                const btn = e.target;
                editingId = btn.dataset.id;
                document.getElementById('modalTitle').textContent = '编辑提示词';
                document.getElementById('promptName').value = decodeURIComponent(btn.dataset.name);
                document.getElementById('promptType').value = decodeURIComponent(btn.dataset.type);
                document.getElementById('promptMainType').value = decodeURIComponent(btn.dataset.main_type);
                document.getElementById('promptContent').value = decodeURIComponent(btn.dataset.prompt);
                document.getElementById('promptModal').style.display = 'flex';
            }
            if (e.target.classList.contains('delete-btn')) {
                const id = e.target.dataset.id;
                deletePrompt(id);
            }
        };
        
        // 初始化
        loadPrompts();
    });
    
    function showAddModal() {
        editingId = null;
        document.getElementById('modalTitle').textContent = '新增提示词';
        document.getElementById('promptName').value = '';
        document.getElementById('promptType').value = '';
        document.getElementById('promptMainType').value = currentMainTypeFilter || '';
        document.getElementById('promptContent').value = '';
        document.getElementById('promptModal').style.display = 'flex';
    }
    
    function closeModal() {
        document.getElementById('promptModal').style.display = 'none';
    }
    
    function savePrompt() {
        const saveButton = document.getElementById('saveButton');
        saveButton.disabled = true;
        saveButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 保存中...';
        
        const name = document.getElementById('promptName').value.trim();
        const type = document.getElementById('promptType').value.trim();
        const main_type = document.getElementById('promptMainType').value.trim();
        const prompt = document.getElementById('promptContent').value.trim();
        
        if (!name || !type || !main_type || !prompt) {
            showMessage('请填写完整信息', 'error');
            saveButton.disabled = false;
            saveButton.textContent = '保存';
            return;
        }
        
        if (editingId) {
            fetch(`/api/prompts/${editingId}`, {
                method: 'PUT',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({name, type, main_type, prompt})
            }).then(res => res.json()).then(data => {
                saveButton.disabled = false;
                saveButton.textContent = '保存';
                
                if (data.success) {
                    closeModal();
                    loadPrompts();
                    showMessage('提示词更新成功', 'success');
                } else {
                    showMessage('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            }).catch(error => {
                saveButton.disabled = false;
                saveButton.textContent = '保存';
                showMessage('保存失败: ' + error.message, 'error');
            });
        } else {
            fetch('/api/prompts', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({name, type, main_type, prompt})
            }).then(res => res.json()).then(data => {
                saveButton.disabled = false;
                saveButton.textContent = '保存';
                
                if (data.success) {
                    closeModal();
                    loadPrompts();
                    showMessage('提示词添加成功', 'success');
                } else {
                    showMessage('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            }).catch(error => {
                saveButton.disabled = false;
                saveButton.textContent = '保存';
                showMessage('保存失败: ' + error.message, 'error');
            });
        }
    }
    
    function deletePrompt(id) {
        if (!confirm('确定要删除该提示词吗？')) return;
        
        // 显示加载状态
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        fetch(`/api/prompts/${id}`, {method: 'DELETE'})
            .then(res => res.json())
            .then(data => {
                // 隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                
                if (data.success) {
                    loadPrompts();
                    showMessage('提示词删除成功', 'success');
                }
                else showMessage('删除失败: ' + (data.message || '未知错误'), 'error');
            })
            .catch(error => {
                // 隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                showMessage('删除失败: ' + error.message, 'error');
            });
    }
    
    function initDefaultPrompts() {
        if (!confirm(i18n('prompt_config.confirm_init_default'))) return;
        
        // 显示加载状态
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        fetch('/api/prompts/init_default', {method: 'POST'})
            .then(res => res.json())
            .then(data => {
                // 隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                
                if (data.success) {
                    showMessage(data.message || i18n('prompt_config.init_success', {count: data.inserted}), 'success');
                    loadPrompts();
                } else {
                    showMessage(i18n('prompt_config.init_failed'), 'error');
                }
            })
            .catch(err => {
                // 隐藏加载状态
                document.getElementById('loadingOverlay').style.display = 'none';
                console.error('初始化失败:', err);
                showMessage('初始化失败，请检查网络连接', 'error');
            });
    }
    
    // 语言切换功能
    const translations = {
        zh: {
            prompt_config: {
                title: "提示词管理",
                header: "提示词管理",
                filter_by_function: "按主要功能筛选：",
                all_functions: "全部功能",
                add_prompt: "新增提示词",
                init_default: "初始化默认提示词",
                table: {
                    name: "名称",
                    main_function: "主要功能",
                    type: "类型",
                    content: "内容",
                    actions: "操作"
                },
                types: {
                    future_account: "开户文件解析",
                    financial_product: "理财产品说明书",
                    broker_interest: "券商账户计息变更",
                    futures_member: "非标交易确认单解析",
                    ningyin_fee: "宁银理财费用变更",
                    product_manual: "账户开户场景"
                },
                confirm_init_default: "确定要初始化默认提示词吗？这将把系统预设的提示词导入到数据库中，已存在的提示词不会被覆盖。",
                init_success: "成功初始化 {count} 个默认提示词",
                init_failed: "初始化失败"
            }
        },
        en: {
            prompt_config: {
                title: "Prompt Management",
                header: "Prompt Management",
                filter_by_function: "Filter by main function:",
                all_functions: "All Functions",
                add_prompt: "Add Prompt",
                init_default: "Initialize Default Prompts",
                table: {
                    name: "Name",
                    main_function: "Main Function",
                    type: "Type",
                    content: "Content",
                    actions: "Actions"
                },
                types: {
                    future_account: "Account Opening Document Analysis",
                    financial_product: "Financial Product Manual",
                    broker_interest: "Broker Account Interest Change",
                    futures_member: "Non-standard Transaction Confirmation",
                    ningyin_fee: "Ningyin Financial Fee Change",
                    product_manual: "Account Opening Scenario"
                },
                confirm_init_default: "Are you sure you want to initialize default prompts? This will import system preset prompts into the database, and existing prompts will not be overwritten.",
                init_success: "Successfully initialized {count} default prompts",
                init_failed: "Initialization failed"
            }
        }
    };

    // 获取当前语言设置，默认为中文
    function getCurrentLanguage() {
        return localStorage.getItem('language') || 'zh';
    }

    // 翻译函数
    function i18n(key, replacements = {}) {
        const lang = getCurrentLanguage();
        const keys = key.split('.');
        let value = translations[lang];
        for (const k of keys) {
            if (value && value[k]) {
                value = value[k];
            } else {
                return key; // 如果找不到翻译，返回原键
            }
        }
        // 替换占位符
        for (const [placeholder, replacement] of Object.entries(replacements)) {
            value = value.replace(`{${placeholder}}`, replacement);
        }
        return value;
    }

    // 应用翻译到页面
    function applyTranslations() {
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = i18n(key);
        });

        // 更新MAIN_TYPE_MAP
        Object.keys(MAIN_TYPE_MAP).forEach(key => {
            MAIN_TYPE_MAP[key] = i18n(`prompt_config.types.${key}`);
        });

        // 重新渲染表格
        filterPromptsByMainType();
    }

    // 页面加载时应用翻译
    document.addEventListener('DOMContentLoaded', function() {
        applyTranslations();
    });
</script>
{% endblock %}
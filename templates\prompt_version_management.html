{% extends "base.html" %}

{% block title %}提示词版本管理 - {{ SYSTEM_NAME }}{% endblock %}
{% block page_title %}提示词版本管理{% endblock %}

{% block extra_css %}
<style>
    .analysis-type-selector {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        gap: 16px;
    }
    
    .analysis-type-selector label {
        font-weight: 600;
        color: #374151;
    }
    
    .analysis-type-selector select {
        padding: 8px 12px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        min-width: 200px;
    }
    
    .version-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 12px;
    }
    
    .current-version {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .version-badge {
        background: #2563eb;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 0.9em;
    }
    
    .versions-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 16px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .versions-table th {
        background: #f8f9fa;
        padding: 16px;
        text-align: left;
        font-weight: 600;
        color: #374151;
        border-bottom: 2px solid #e5e7eb;
    }
    
    .versions-table td {
        padding: 16px;
        border-bottom: 1px solid #e5e7eb;
        vertical-align: top;
    }
    
    .versions-table tr:hover {
        background: #f8f9fa;
    }
    
    .version-row.active {
        background: #dbeafe;
    }
    
    .version-row.active:hover {
        background: #bfdbfe;
    }
    
    .version-number {
        font-weight: 700;
        color: #2563eb;
        font-size: 1.1em;
    }
    
    .version-description {
        color: #6b7280;
        font-size: 0.9em;
        margin-top: 4px;
    }
    
    .version-meta {
        font-size: 0.85em;
        color: #9ca3af;
    }
    
    .version-actions {
        display: flex;
        gap: 8px;
    }
    
    .btn-small {
        padding: 6px 12px;
        font-size: 0.85em;
    }
    
    .active-badge {
        background: #10b981;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75em;
        font-weight: 600;
    }
    
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.6);
        backdrop-filter: blur(8px);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }
    
    .modal-content {
        background: white;
        border-radius: 16px;
        max-width: 600px;
        width: 90%;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }
    
    .modal-header {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        padding: 20px;
        border-radius: 16px 16px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-title {
        font-size: 1.3em;
        font-weight: 700;
    }
    
    .modal-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.5em;
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
    }
    
    .modal-close:hover {
        background: rgba(255,255,255,0.2);
    }
    
    .modal-body {
        padding: 24px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #374151;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e5e7eb;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #2563eb;
    }
    
    .form-group textarea {
        min-height: 120px;
        resize: vertical;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
    }
    
    .modal-footer {
        padding: 20px 24px;
        border-top: 1px solid #e5e7eb;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }
    
    .loading {
        text-align: center;
        padding: 40px;
        color: #6b7280;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6b7280;
    }
    
    .empty-state-icon {
        font-size: 3em;
        margin-bottom: 16px;
    }
    
    /* 骨架屏样式 */
    .skeleton-line {
        height: 20px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 4px;
        margin: 8px 0;
    }
    
    @keyframes loading {
        0% {
            background-position: 200% 0;
        }
        100% {
            background-position: -200% 0;
        }
    }
    
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top-color: #2563eb;
        animation: spin 1s ease-in-out infinite;
    }
    
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 分析类型选择器 -->
    <div class="analysis-type-selector">
        <label for="analysisTypeSelect">分析类型:</label>
        <select id="analysisTypeSelect" onchange="loadVersions()" class="form-select">
            <option value="">请选择分析类型</option>
            <option value="开户文件解析">开户文件解析</option>
            <option value="理财产品说明书">理财产品说明书</option>
            <option value="券商账户计息变更">券商账户计息变更</option>
            <option value="非标交易确认单">非标交易确认单</option>
            <option value="期货交易会员">期货交易会员</option>
            <option value="宁银费用变更">宁银费用变更</option>
        </select>
    </div>
    
    <!-- 版本控制区域 -->
    <div class="version-controls" id="versionControls" style="display: none;">
        <div class="current-version">
            <span>当前版本:</span>
            <span class="version-badge" id="currentVersionBadge">-</span>
            <span id="currentVersionInfo" class="version-meta"></span>
        </div>
        <button class="btn" onclick="showCreateVersionModal()">
            <span>+</span>
            创建新版本
        </button>
    </div>
    
    <!-- 版本列表 -->
    <div id="versionsContainer" class="card position-relative">
        <!-- 加载中遮罩层 -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner"></div>
        </div>
        <div class="card-body p-0">
            <!-- 初始提示 -->
            <div class="loading">
                <div style="font-size: 2em; margin-bottom: 16px;">⏳</div>
                <div>请选择分析类型查看版本历史</div>
            </div>
        </div>
    </div>
</div>

<!-- 创建版本模态框 -->
<div id="createVersionModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">创建新版本</h3>
            <button class="modal-close" onclick="closeCreateVersionModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="createVersionForm">
                <div class="form-group">
                    <label for="versionName">版本名称</label>
                    <input type="text" id="versionName" required placeholder="例如: 优化识别准确率版本" class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="versionNumber">版本号</label>
                    <input type="text" id="versionNumber" required placeholder="例如: v2.1" class="form-control">
                </div>
                
                <div class="form-group">
                    <label for="versionType">提示词类型</label>
                    <select id="versionType" required class="form-select">
                        <option value="">请选择类型</option>
                        <option value="system_prompt">系统提示词</option>
                        <option value="img_prompt_1">图像提示词1</option>
                        <option value="img_prompt_2">图像提示词2</option>
                        <option value="img_prompt">图像提示词</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="versionDescription">版本说明</label>
                    <textarea id="versionDescription" required placeholder="描述此版本的改进内容和特点" class="form-control"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="promptContent">提示词内容</label>
                    <textarea id="promptContent" required placeholder="输入提示词内容" style="min-height: 200px;" class="form-control"></textarea>
                </div>
                
                <div class="form-group">
                    <div class="checkbox-group">
                        <input type="checkbox" id="setAsActive" class="form-check-input">
                        <label for="setAsActive" class="form-check-label">设为当前激活版本</label>
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeCreateVersionModal()">取消</button>
            <button type="button" class="btn btn-success" id="createVersionBtn" onclick="createVersion()">创建版本</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentAnalysisType = '';
    let versionsData = null;
    
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化模态框外部点击关闭
        document.getElementById('createVersionModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCreateVersionModal();
            }
        });
    });
    
    // 加载版本列表
    async function loadVersions() {
        const analysisType = document.getElementById('analysisTypeSelect').value;
        if (!analysisType) {
            document.getElementById('versionControls').style.display = 'none';
            document.getElementById('versionsContainer').innerHTML = `
                <div class="loading">
                    <div style="font-size: 2em; margin-bottom: 16px;">⏳</div>
                    <div>请选择分析类型查看版本历史</div>
                </div>
            `;
            return;
        }
        
        currentAnalysisType = analysisType;
        
        try {
            // 显示加载遮罩
            document.getElementById('loadingOverlay').style.display = 'flex';
            
            // 添加骨架屏
            document.getElementById('versionsContainer').innerHTML = `
                <div class="card-body p-0">
                    <table class="versions-table">
                        <thead>
                            <tr>
                                <th>版本信息</th>
                                <th>类型</th>
                                <th>创建信息</th>
                                <th>使用统计</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                            </tr>
                            <tr>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                                <td><div class="skeleton-line"></div></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            `;
            
            const response = await fetch(`/api/prompt-versions/${encodeURIComponent(analysisType)}`);
            const result = await response.json();
            
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            if (result.success) {
                versionsData = result.data;
                updateVersionDisplay();
            } else {
                throw new Error(result.message || '加载失败');
            }
        } catch (error) {
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            console.error('加载版本失败:', error);
            document.getElementById('versionsContainer').innerHTML = `
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-state-icon">❌</div>
                        <div>加载版本历史失败</div>
                        <div style="margin-top: 8px; font-size: 0.9em;">${error.message}</div>
                    </div>
                </div>
            `;
        }
    }
    
    // 更新版本显示
    function updateVersionDisplay() {
        // 更新当前版本信息
        const currentVersionBadge = document.getElementById('currentVersionBadge');
        const currentVersionInfo = document.getElementById('currentVersionInfo');
        const versionControls = document.getElementById('versionControls');
        
        if (versionsData.current_version) {
            currentVersionBadge.textContent = versionsData.current_version;
            const currentVersion = versionsData.versions.find(v => v.is_current);
            if (currentVersion) {
                currentVersionInfo.textContent = `创建于 ${currentVersion.created_at} | 使用次数: ${currentVersion.usage_count || 0}`;
            }
        } else {
            currentVersionBadge.textContent = '无';
            currentVersionInfo.textContent = '暂无激活版本';
        }
        
        versionControls.style.display = 'flex';
        
        // 更新版本列表
        const container = document.getElementById('versionsContainer');
        
        if (versionsData.versions.length === 0) {
            container.innerHTML = `
                <div class="card-body">
                    <div class="empty-state">
                        <div class="empty-state-icon">📝</div>
                        <div>暂无版本历史</div>
                        <div style="margin-top: 8px; font-size: 0.9em;">点击"创建新版本"开始管理提示词版本</div>
                    </div>
                </div>
            `;
            return;
        }
        
        let tableHtml = `
            <div class="card-body p-0">
                <table class="versions-table">
                    <thead>
                        <tr>
                            <th>版本信息</th>
                            <th>类型</th>
                            <th>创建信息</th>
                            <th>使用统计</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        versionsData.versions.forEach(version => {
            const isActive = version.is_current;
            tableHtml += `
                <tr class="version-row ${isActive ? 'active' : ''}">
                    <td>
                        <div class="version-number">${version.version}</div>
                        <div class="version-description">${version.version_description || version.name}</div>
                        ${isActive ? '<span class="active-badge">当前版本</span>' : ''}
                    </td>
                    <td>${version.type}</td>
                    <td>
                        <div class="version-meta">
                            创建时间: ${version.created_at}<br>
                            创建者: ${version.created_by || '系统'}
                        </div>
                    </td>
                    <td>
                        <div class="version-meta">
                            使用次数: ${version.usage_count || 0}<br>
                            最后使用: ${version.last_used_at || '从未使用'}
                        </div>
                    </td>
                    <td>
                        <div class="version-actions">
                            ${!isActive ? `<button class="btn btn-sm btn-success" onclick="switchVersion(${version.id}, '${version.version}')">激活</button>` : ''}
                            <button class="btn btn-sm" onclick="viewVersion(${version.id})">查看</button>
                            ${!isActive ? `<button class="btn btn-sm btn-danger" onclick="deleteVersion(${version.id}, '${version.version}')">删除</button>` : ''}
                        </div>
                    </td>
                </tr>
            `;
        });
        
        tableHtml += `
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = tableHtml;
    }
    
    // 显示创建版本模态框
    function showCreateVersionModal() {
        document.getElementById('createVersionModal').style.display = 'flex';
    }
    
    // 关闭创建版本模态框
    function closeCreateVersionModal() {
        document.getElementById('createVersionModal').style.display = 'none';
        document.getElementById('createVersionForm').reset();
    }
    
    // 创建新版本
    async function createVersion() {
        const createButton = document.getElementById('createVersionBtn');
        createButton.disabled = true;
        createButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 创建中...';
        
        const data = {
            name: document.getElementById('versionName').value,
            version: document.getElementById('versionNumber').value,
            type: document.getElementById('versionType').value,
            version_description: document.getElementById('versionDescription').value,
            prompt: document.getElementById('promptContent').value,
            set_as_active: document.getElementById('setAsActive').checked
        };
        
        // 验证必填字段
        if (!data.name || !data.version || !data.type || !data.version_description || !data.prompt) {
            showMessage('请填写所有必填字段', 'error');
            createButton.disabled = false;
            createButton.textContent = '创建版本';
            return;
        }
        
        try {
            const response = await fetch(`/api/prompt-versions/${encodeURIComponent(currentAnalysisType)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            createButton.disabled = false;
            createButton.textContent = '创建版本';
            
            if (result.success) {
                showMessage('版本创建成功!', 'success');
                closeCreateVersionModal();
                loadVersions(); // 重新加载版本列表
            } else {
                showMessage('创建失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('创建版本失败:', error);
            showMessage('创建失败: ' + error.message, 'error');
            createButton.disabled = false;
            createButton.textContent = '创建版本';
        }
    }
    
    // 切换版本
    async function switchVersion(versionId, versionNumber) {
        if (!confirm(`确定要切换到版本 ${versionNumber} 吗？`)) {
            return;
        }
        
        // 显示加载遮罩
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        try {
            const response = await fetch(`/api/prompt-versions/${encodeURIComponent(currentAnalysisType)}/activate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version_id: versionId,
                    reason: '手动切换版本'
                })
            });
            
            const result = await response.json();
            
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            if (result.success) {
                showMessage('版本切换成功!', 'success');
                loadVersions(); // 重新加载版本列表
            } else {
                showMessage('切换失败: ' + result.message, 'error');
            }
        } catch (error) {
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            console.error('切换版本失败:', error);
            showMessage('切换失败: ' + error.message, 'error');
        }
    }
    
    // 查看版本详情
    function viewVersion(versionId) {
        const version = versionsData.versions.find(v => v.id === versionId);
        if (version) {
            // 创建一个模态框来展示详情，而不是使用alert
            const detailsHTML = `
                <div class="modal-header">
                    <h3 class="modal-title">版本详情 - ${version.version}</h3>
                    <button class="modal-close" onclick="closeDetailsModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>版本号</label>
                        <div class="form-control">${version.version}</div>
                    </div>
                    <div class="form-group">
                        <label>名称</label>
                        <div class="form-control">${version.name}</div>
                    </div>
                    <div class="form-group">
                        <label>类型</label>
                        <div class="form-control">${version.type}</div>
                    </div>
                    <div class="form-group">
                        <label>说明</label>
                        <div class="form-control" style="min-height: 60px;">${version.version_description || '无'}</div>
                    </div>
                    <div class="form-group">
                        <label>提示词内容</label>
                        <div class="form-control" style="min-height: 150px; white-space: pre-wrap;">${version.prompt}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeDetailsModal()">关闭</button>
                </div>
            `;
            
            // 创建一个新的模态框元素
            const detailsModal = document.createElement('div');
            detailsModal.className = 'modal';
            detailsModal.id = 'versionDetailsModal';
            detailsModal.style.display = 'flex';
            
            const modalContent = document.createElement('div');
            modalContent.className = 'modal-content';
            modalContent.innerHTML = detailsHTML;
            
            detailsModal.appendChild(modalContent);
            document.body.appendChild(detailsModal);
            
            // 添加点击外部关闭
            detailsModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDetailsModal();
                }
            });
        }
    }
    
    function closeDetailsModal() {
        const detailsModal = document.getElementById('versionDetailsModal');
        if (detailsModal) {
            detailsModal.remove();
        }
    }
    
    // 删除版本
    async function deleteVersion(versionId, versionNumber) {
        if (!confirm(`确定要删除版本 ${versionNumber} 吗？此操作不可恢复！`)) {
            return;
        }
        
        // 显示加载遮罩
        document.getElementById('loadingOverlay').style.display = 'flex';
        
        try {
            const response = await fetch(`/api/prompt-versions/${encodeURIComponent(currentAnalysisType)}/${versionId}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            if (result.success) {
                showMessage('版本删除成功!', 'success');
                loadVersions(); // 重新加载版本列表
            } else {
                showMessage('删除失败: ' + result.message, 'error');
            }
        } catch (error) {
            // 隐藏加载遮罩
            document.getElementById('loadingOverlay').style.display = 'none';
            
            console.error('删除版本失败:', error);
            showMessage('删除失败: ' + error.message, 'error');
        }
    }
</script>
{% endblock %}

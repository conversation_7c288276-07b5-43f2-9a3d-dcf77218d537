{% extends "base.html" %}

{% block title %}记录详情{% endblock %}

{% block extra_css %}
<style>
    .record-detail-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }
    
    .record-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .record-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .info-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 4px solid #667eea;
    }
    
    .info-card h6 {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 10px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .info-card .value {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-completed { background: #d4edda; color: #155724; }
    .status-failed { background: #f8d7da; color: #721c24; }
    .status-processing { background: #fff3cd; color: #856404; }
    .status-pending { background: #e2e3e5; color: #383d41; }
    
    .results-section {
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }
    
    .results-tabs {
        border-bottom: 2px solid #f8f9fa;
        margin-bottom: 20px;
    }
    
    .results-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #666;
        font-weight: 600;
        padding: 15px 20px;
    }
    
    .results-tabs .nav-link.active {
        border-bottom-color: #667eea;
        color: #667eea;
        background: none;
    }
    
    .result-content {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 20px;
    }
    
    .json-viewer {
        background: #2d3748;
        color: #e2e8f0;
        padding: 20px;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        font-size: 0.9rem;
        overflow-x: auto;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .action-buttons {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 30px;
    }
    
    .btn-custom {
        padding: 12px 24px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-primary-custom {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }
    
    .btn-primary-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .btn-secondary-custom {
        background: #6c757d;
        color: white;
        border: none;
    }
    
    .btn-secondary-custom:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
    }
    
    .loading-spinner {
        display: none;
        text-align: center;
        padding: 40px;
    }
    
    .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin: 20px 0;
        border-left: 4px solid #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="record-detail-container">
    <!-- 记录头部信息 -->
    <div class="record-header">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h2 class="mb-2">记录详情</h2>
                <p class="mb-0 opacity-75">记录ID: <span id="recordId">{{ record_id }}</span></p>
            </div>
            <div>
                <a href="/records" class="btn btn-light btn-sm">
                    <i class="bi bi-arrow-left"></i> 返回列表
                </a>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div class="loading-spinner" id="loadingSpinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-3">正在加载记录详情...</p>
    </div>

    <!-- 错误信息 -->
    <div class="error-message" id="errorMessage" style="display: none;">
        <i class="bi bi-exclamation-triangle"></i>
        <span id="errorText"></span>
    </div>

    <!-- 记录详情内容 -->
    <div id="recordContent" style="display: none;">
        <!-- 基本信息网格 -->
        <div class="record-info-grid">
            <div class="info-card">
                <h6>文件名</h6>
                <div class="value" id="filename">-</div>
            </div>
            <div class="info-card">
                <h6>分析类型</h6>
                <div class="value" id="analysisType">-</div>
            </div>
            <div class="info-card">
                <h6>状态</h6>
                <div class="value">
                    <span class="status-badge" id="status">-</span>
                </div>
            </div>
            <div class="info-card">
                <h6>准确率</h6>
                <div class="value" id="accuracyScore">-</div>
            </div>
            <div class="info-card">
                <h6>创建时间</h6>
                <div class="value" id="createdAt">-</div>
            </div>
            <div class="info-card">
                <h6>更新时间</h6>
                <div class="value" id="updatedAt">-</div>
            </div>
        </div>

        <!-- 结果展示区域 -->
        <div class="results-section">
            <h4 class="mb-4">
                <i class="bi bi-file-text"></i> 分析结果
            </h4>
            
            <!-- 结果标签页 -->
            <ul class="nav nav-tabs results-tabs" id="resultTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="ai-result-tab" data-bs-toggle="tab" data-bs-target="#ai-result" type="button" role="tab">
                        <i class="bi bi-robot"></i> AI识别结果
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="expected-result-tab" data-bs-toggle="tab" data-bs-target="#expected-result" type="button" role="tab">
                        <i class="bi bi-check-circle"></i> 预期结果
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="comparison-result-tab" data-bs-toggle="tab" data-bs-target="#comparison-result" type="button" role="tab">
                        <i class="bi bi-bar-chart"></i> 对比结果
                    </button>
                </li>
            </ul>

            <!-- 结果内容 -->
            <div class="tab-content" id="resultTabContent">
                <div class="tab-pane fade show active" id="ai-result" role="tabpanel">
                    <div class="result-content">
                        <h6>AI识别结果</h6>
                        <div class="json-viewer" id="aiResultContent">暂无数据</div>
                    </div>
                </div>
                <div class="tab-pane fade" id="expected-result" role="tabpanel">
                    <div class="result-content">
                        <h6>预期正确结果</h6>
                        <div class="json-viewer" id="expectedResultContent">暂无数据</div>
                    </div>
                </div>
                <div class="tab-pane fade" id="comparison-result" role="tabpanel">
                    <div class="result-content">
                        <h6>对比分析结果</h6>
                        <div class="json-viewer" id="comparisonResultContent">暂无数据</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <button class="btn btn-primary-custom" onclick="openResultComparison()">
                <i class="bi bi-bar-chart-line"></i> 查看结果对比
            </button>
            <button class="btn btn-secondary-custom" onclick="reanalyzeRecord()">
                <i class="bi bi-arrow-clockwise"></i> 重新分析
            </button>
            <a href="/records" class="btn btn-secondary-custom">
                <i class="bi bi-list"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<!-- 引入结果对比模态框 -->
{% include 'result_comparison_modal.html' %}
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/result_comparison.js') }}"></script>
<script>
    let currentRecord = null;
    
    // 页面加载时获取记录详情
    document.addEventListener('DOMContentLoaded', function() {
        loadRecordDetail();
    });
    
    // 加载记录详情
    function loadRecordDetail() {
        const recordId = {{ record_id }};
        const loadingSpinner = document.getElementById('loadingSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const recordContent = document.getElementById('recordContent');
        
        // 显示加载状态
        loadingSpinner.style.display = 'block';
        errorMessage.style.display = 'none';
        recordContent.style.display = 'none';
        
        // 获取HTTP客户端
        const httpClient = window.axios || {
            get: (url) => fetch(url).then(r => r.json().then(data => ({data})))
        };
        
        // 请求记录详情
        httpClient.get(`/api/records/${recordId}`)
            .then(response => {
                if (response.data.success) {
                    currentRecord = response.data.data;
                    displayRecordDetail(currentRecord);
                    
                    // 隐藏加载状态，显示内容
                    loadingSpinner.style.display = 'none';
                    recordContent.style.display = 'block';
                } else {
                    showError(response.data.message || '获取记录详情失败');
                }
            })
            .catch(error => {
                console.error('获取记录详情失败:', error);
                showError('获取记录详情失败，请稍后重试');
            });
    }
    
    // 显示记录详情
    function displayRecordDetail(record) {
        // 基本信息
        document.getElementById('filename').textContent = record.filename || '-';
        document.getElementById('analysisType').textContent = getAnalysisTypeName(record.analysis_type) || '-';
        document.getElementById('accuracyScore').textContent = record.accuracy_score ? 
            (record.accuracy_score * 100).toFixed(1) + '%' : '-';
        document.getElementById('createdAt').textContent = formatDateTime(record.created_at) || '-';
        document.getElementById('updatedAt').textContent = formatDateTime(record.updated_at) || '-';
        
        // 状态
        const statusElement = document.getElementById('status');
        statusElement.textContent = getStatusText(record.status);
        statusElement.className = `status-badge status-${record.status}`;
        
        // 结果内容
        displayResult('aiResultContent', record.ai_result);
        displayResult('expectedResultContent', record.expected_result);
        displayResult('comparisonResultContent', record.comparison_result);
    }
    
    // 显示结果内容
    function displayResult(elementId, result) {
        const element = document.getElementById(elementId);
        if (result) {
            element.textContent = JSON.stringify(result, null, 2);
        } else {
            element.textContent = '暂无数据';
        }
    }
    
    // 显示错误信息
    function showError(message) {
        const loadingSpinner = document.getElementById('loadingSpinner');
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        loadingSpinner.style.display = 'none';
        errorText.textContent = message;
        errorMessage.style.display = 'block';
    }
    
    // 获取分析类型名称
    function getAnalysisTypeName(type) {
        const types = {
            'broker_interest': '券商账户计息变更',
            'wealth_management': '理财产品说明书',
            'futures_member': '期货交易会员解析',
            'ningyin_fee': '宁银理财费用变更',
            'futures_account': '期货账户分析'
        };
        return types[type] || type;
    }
    
    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'failed': '失败',
            'processing': '处理中',
            'pending': '待处理'
        };
        return statusMap[status] || status;
    }
    
    // 格式化日期时间
    function formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }
    
    // 打开结果对比弹窗
    function openResultComparison() {
        if (currentRecord) {
            viewResultComparison(currentRecord.id);
        }
    }
    
    // 重新分析记录
    function reanalyzeRecord() {
        if (!currentRecord) return;
        
        if (confirm('确定要重新分析这条记录吗？这将更新AI识别结果。')) {
            // 这里可以添加重新分析的逻辑
            showMessage('重新分析功能开发中...', 'info');
        }
    }
    
    // 显示消息
    function showMessage(message, type = 'info') {
        // 简单的消息显示，可以后续优化
        alert(message);
    }
</script>
{% endblock %}

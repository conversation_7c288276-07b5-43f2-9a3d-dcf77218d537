{% extends "base.html" %}

{% block title %}复核管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .review-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .review-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .priority-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
    }
    
    .priority-high {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .priority-normal {
        background-color: #fef3c7;
        color: #92400e;
    }
    
    .priority-low {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .accuracy-score {
        font-size: 1.25rem;
        font-weight: bold;
    }
    
    .accuracy-excellent {
        color: #059669;
    }
    
    .accuracy-good {
        color: #0891b2;
    }
    
    .accuracy-fair {
        color: #d97706;
    }
    
    .accuracy-poor {
        color: #dc2626;
    }
    
    .review-form {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
    }
    
    .comparison-table {
        font-size: 0.875rem;
    }
    
    .field-match {
        background-color: #d1fae5;
    }
    
    .field-mismatch {
        background-color: #fee2e2;
    }
    
    .field-missing {
        background-color: #fef3c7;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
    }
    
    .filter-section {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-clipboard-check me-2"></i>
        复核管理
    </h2>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="loadReviewStats()">
            <i class="bi bi-bar-chart me-2"></i>
            统计报告
        </button>
        <button class="btn btn-primary" onclick="loadPendingReviews()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            刷新列表
        </button>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4" id="statsOverview" style="display: none;">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="totalRecords">-</div>
                <div>总记录数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="pendingReviews">-</div>
                <div>待复核</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="approvedReviews">-</div>
                <div>已通过</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="reviewRate">-</div>
                <div>复核率</div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-3">
            <label class="form-label">分析类型</label>
            <select class="form-select" id="analysisTypeFilter">
                <option value="">全部类型</option>
                <option value="futures_account">期货账户</option>
                <option value="wealth_management">理财产品</option>
                <option value="broker_interest">券商计息</option>
                <option value="account_opening">账户开户场景</option>
                <option value="ningxia_bank_fee">宁银费用</option>
                <option value="non_standard_trade">非标交易</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">优先级</label>
            <select class="form-select" id="priorityFilter">
                <option value="">全部优先级</option>
                <option value="high">高优先级</option>
                <option value="normal">普通优先级</option>
                <option value="low">低优先级</option>
            </select>
        </div>
        <div class="col-md-3">
            <label class="form-label">准确率范围</label>
            <select class="form-select" id="accuracyFilter">
                <option value="">全部范围</option>
                <option value="excellent">优秀 (≥95%)</option>
                <option value="good">良好 (80-95%)</option>
                <option value="fair">一般 (60-80%)</option>
                <option value="poor">较差 (<60%)</option>
            </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
            <button class="btn btn-outline-primary me-2" onclick="loadPendingReviews()">
                <i class="bi bi-search me-2"></i>
                搜索
            </button>
            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                <i class="bi bi-arrow-clockwise me-2"></i>
                重置
            </button>
        </div>
    </div>
</div>

<!-- 待复核记录列表 -->
<div class="row" id="reviewList">
    <div class="col-12 text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载待复核记录...</p>
    </div>
</div>

<!-- 分页 -->
<nav id="paginationContainer" style="display: none;">
    <ul class="pagination justify-content-center" id="pagination">
    </ul>
</nav>

<!-- 复核模态框 -->
<div class="modal fade" id="reviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-clipboard-check me-2"></i>
                    复核记录
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- 记录基本信息 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr>
                                <td width="30%">文件名:</td>
                                <td id="reviewFileName">-</td>
                            </tr>
                            <tr>
                                <td>分析类型:</td>
                                <td id="reviewAnalysisType">-</td>
                            </tr>
                            <tr>
                                <td>创建时间:</td>
                                <td id="reviewCreatedAt">-</td>
                            </tr>
                            <tr>
                                <td>创建人:</td>
                                <td id="reviewCreatedBy">-</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>分析结果</h6>
                        <table class="table table-sm">
                            <tr>
                                <td width="30%">准确率:</td>
                                <td>
                                    <span id="reviewAccuracyScore" class="accuracy-score">-</span>
                                </td>
                            </tr>
                            <tr>
                                <td>优先级:</td>
                                <td>
                                    <span id="reviewPriority" class="priority-badge">-</span>
                                </td>
                            </tr>
                            <tr>
                                <td>状态:</td>
                                <td>
                                    <span id="reviewStatus" class="badge">-</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- 对比结果 -->
                <div class="mb-4">
                    <h6>字段对比结果</h6>
                    <div class="table-responsive">
                        <table class="table table-sm comparison-table" id="comparisonTable">
                            <thead>
                                <tr>
                                    <th>字段名称</th>
                                    <th>AI识别值</th>
                                    <th>系统值</th>
                                    <th>相似度</th>
                                    <th>匹配状态</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- 复核表单 -->
                <div class="review-form">
                    <h6 class="mb-3">复核意见</h6>
                    <form id="reviewForm">
                        <input type="hidden" id="reviewRecordId" name="record_id">
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">复核结果 *</label>
                                <select class="form-select" id="reviewResult" name="review_status" required>
                                    <option value="">请选择复核结果</option>
                                    <option value="approved">通过</option>
                                    <option value="rejected">拒绝</option>
                                    <option value="needs_revision">需要修正</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">复核耗时(分钟)</label>
                                <input type="number" class="form-control" id="reviewTime" name="review_time" 
                                       min="0" step="0.1" placeholder="可选">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">复核意见</label>
                            <textarea class="form-control" id="reviewComment" name="review_comment" 
                                      rows="4" placeholder="请输入复核意见..."></textarea>
                        </div>
                        
                        <div class="mb-3" id="correctionsSection" style="display: none;">
                            <label class="form-label">修正建议 (JSON格式)</label>
                            <textarea class="form-control" id="corrections" name="corrections" 
                                      rows="6" placeholder='{"field_name": "corrected_value"}'></textarea>
                            <div class="form-text">当选择"需要修正"时，可以提供具体的修正建议</div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitReviewBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    提交复核
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const perPage = 12;
    let currentRecordId = null;

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadPendingReviews();
        loadReviewStats();
        initEventListeners();
    });

    // 初始化事件监听器
    function initEventListeners() {
        // 提交复核按钮
        document.getElementById('submitReviewBtn').addEventListener('click', submitReview);

        // 复核结果变化
        document.getElementById('reviewResult').addEventListener('change', function() {
            const correctionsSection = document.getElementById('correctionsSection');
            if (this.value === 'needs_revision') {
                correctionsSection.style.display = 'block';
            } else {
                correctionsSection.style.display = 'none';
            }
        });

        // 筛选器变化
        document.getElementById('analysisTypeFilter').addEventListener('change', loadPendingReviews);
        document.getElementById('priorityFilter').addEventListener('change', loadPendingReviews);
        document.getElementById('accuracyFilter').addEventListener('change', loadPendingReviews);
    }

    // 加载待复核记录
    function loadPendingReviews(page = 1) {
        currentPage = page;

        const analysisType = document.getElementById('analysisTypeFilter').value;
        const priority = document.getElementById('priorityFilter').value;
        const accuracy = document.getElementById('accuracyFilter').value;

        const params = {
            page: page,
            per_page: perPage
        };

        if (analysisType) params.analysis_type = analysisType;
        if (priority) params.priority = priority;

        API.get('/api/review/pending', params)
            .then(response => {
                if (response.success) {
                    renderReviewList(response.data.records, accuracy);
                    renderPagination(response.data.pagination);
                } else {
                    Utils.showMessage('加载待复核记录失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载待复核记录失败:', error);
                Utils.showMessage('加载待复核记录失败', 'error');
            });
    }

    // 渲染复核列表
    function renderReviewList(records, accuracyFilter) {
        const container = document.getElementById('reviewList');

        // 根据准确率筛选
        let filteredRecords = records;
        if (accuracyFilter) {
            filteredRecords = records.filter(record => {
                const accuracy = record.accuracy_score || 0;
                switch (accuracyFilter) {
                    case 'excellent': return accuracy >= 0.95;
                    case 'good': return accuracy >= 0.8 && accuracy < 0.95;
                    case 'fair': return accuracy >= 0.6 && accuracy < 0.8;
                    case 'poor': return accuracy < 0.6;
                    default: return true;
                }
            });
        }

        if (filteredRecords.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-clipboard-check display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无待复核记录</h5>
                    <p class="text-muted">所有记录都已完成复核</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';

        filteredRecords.forEach(record => {
            const reviewCard = createReviewCard(record);
            container.appendChild(reviewCard);
        });
    }

    // 创建复核卡片
    function createReviewCard(record) {
        const col = document.createElement('div');
        col.className = 'col-lg-6 col-xl-4 mb-4';

        const priorityClass = getPriorityClass(record.review_priority);
        const priorityText = getPriorityText(record.review_priority);
        const accuracyClass = getAccuracyClass(record.accuracy_score);
        const accuracyPercent = ((record.accuracy_score || 0) * 100).toFixed(1);

        col.innerHTML = `
            <div class="card review-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 text-truncate" title="${record.filename}">
                        ${record.filename}
                    </h6>
                    <span class="priority-badge ${priorityClass}">${priorityText}</span>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <small class="text-muted">分析类型:</small>
                        <div class="small">${getAnalysisTypeName(record.analysis_type)}</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">准确率:</small>
                        <div class="accuracy-score ${accuracyClass}">${accuracyPercent}%</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">创建时间:</small>
                        <div class="small">${Utils.formatDateTime(record.created_at)}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">创建人:</small>
                        <div class="small">${record.created_by_name || 'N/A'}</div>
                    </div>

                    <div class="d-grid">
                        <button class="btn btn-primary" onclick="openReviewModal(${record.id})">
                            <i class="bi bi-clipboard-check me-2"></i>
                            开始复核
                        </button>
                    </div>
                </div>
            </div>
        `;

        return col;
    }

    // 获取优先级样式类
    function getPriorityClass(priority) {
        switch (priority) {
            case 'high': return 'priority-high';
            case 'normal': return 'priority-normal';
            case 'low': return 'priority-low';
            default: return 'priority-normal';
        }
    }

    // 获取优先级文本
    function getPriorityText(priority) {
        switch (priority) {
            case 'high': return '高优先级';
            case 'normal': return '普通';
            case 'low': return '低优先级';
            default: return '普通';
        }
    }

    // 获取准确率样式类
    function getAccuracyClass(accuracy) {
        const score = accuracy || 0;
        if (score >= 0.95) return 'accuracy-excellent';
        if (score >= 0.8) return 'accuracy-good';
        if (score >= 0.6) return 'accuracy-fair';
        return 'accuracy-poor';
    }

    // 获取分析类型名称
    function getAnalysisTypeName(type) {
        const names = {
            'futures_account': '期货账户',
            'wealth_management': '理财产品',
            'broker_interest': '券商计息',
            'account_opening': '账户开户场景',
            'ningxia_bank_fee': '宁银费用',
            'non_standard_trade': '非标交易'
        };
        return names[type] || type;
    }
</script>
{% endblock %}

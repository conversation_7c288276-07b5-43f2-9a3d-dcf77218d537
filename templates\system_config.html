{% extends "base.html" %}

{% block title %}系统配置 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .config-section {
        background-color: #f8fafc;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .config-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .config-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .config-item {
        border-bottom: 1px solid #f3f4f6;
        padding: 1rem 0;
    }
    
    .config-item:last-child {
        border-bottom: none;
    }
    
    .config-key {
        font-weight: 600;
        color: #374151;
        font-family: 'Courier New', monospace;
    }
    
    .config-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    
    .config-value {
        font-family: 'Courier New', monospace;
        background-color: #f3f4f6;
        border-radius: 6px;
        padding: 0.5rem;
        border: 1px solid #d1d5db;
    }
    
    .config-value:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    }
    
    .system-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: 500;
        opacity: 0.9;
    }
    
    .info-value {
        font-family: 'Courier New', monospace;
        font-weight: 600;
    }
    
    .resource-bar {
        height: 8px;
        background-color: rgba(255, 255, 255, 0.3);
        border-radius: 4px;
        overflow: hidden;
        margin-top: 0.5rem;
    }
    
    .resource-fill {
        height: 100%;
        background-color: #10b981;
        transition: width 0.3s ease;
    }
    
    .resource-fill.warning {
        background-color: #f59e0b;
    }
    
    .resource-fill.danger {
        background-color: #ef4444;
    }
    
    .log-viewer {
        background-color: #1f2937;
        color: #f9fafb;
        border-radius: 8px;
        padding: 1rem;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        max-height: 400px;
        overflow-y: auto;
        white-space: pre-wrap;
        word-break: break-all;
    }
    
    .nav-pills .nav-link {
        border-radius: 20px;
        margin-right: 0.5rem;
    }
    
    .nav-pills .nav-link.active {
        background-color: #2563eb;
    }
    
    .config-group {
        margin-bottom: 2rem;
    }
    
    .config-group-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e5e7eb;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-gear me-2"></i>
        系统配置
    </h2>
    <div>
        <button class="btn btn-outline-primary me-2" onclick="refreshSystemInfo()">
            <i class="bi bi-arrow-clockwise me-2"></i>
            刷新信息
        </button>
        <button class="btn btn-success" onclick="saveAllConfigs()">
            <i class="bi bi-check-circle me-2"></i>
            保存配置
        </button>
    </div>
</div>

<!-- 导航标签 -->
<ul class="nav nav-pills mb-4" id="configTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" 
                type="button" role="tab">
            <i class="bi bi-info-circle me-2"></i>
            系统信息
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="config-tab" data-bs-toggle="pill" data-bs-target="#config" 
                type="button" role="tab">
            <i class="bi bi-sliders me-2"></i>
            配置管理
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="logs-tab" data-bs-toggle="pill" data-bs-target="#logs" 
                type="button" role="tab">
            <i class="bi bi-file-text me-2"></i>
            系统日志
        </button>
    </li>
</ul>

<!-- 标签内容 -->
<div class="tab-content" id="configTabContent">
    <!-- 系统信息 -->
    <div class="tab-pane fade show active" id="system" role="tabpanel">
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card system-info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-server me-2"></i>
                            系统信息
                        </h5>
                    </div>
                    <div class="card-body" id="systemInfo">
                        <div class="text-center py-3">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="card system-info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-cpu me-2"></i>
                            资源使用情况
                        </h5>
                    </div>
                    <div class="card-body" id="resourceInfo">
                        <div class="text-center py-3">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="card system-info-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-database me-2"></i>
                            数据库统计
                        </h5>
                    </div>
                    <div class="card-body" id="databaseInfo">
                        <div class="text-center py-3">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 配置管理 -->
    <div class="tab-pane fade" id="config" role="tabpanel">
        <div class="config-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>系统配置项</h4>
                <div>
                    <button class="btn btn-outline-primary btn-sm me-2" onclick="addConfigItem()">
                        <i class="bi bi-plus-circle me-1"></i>
                        添加配置
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="loadSystemConfig()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新
                    </button>
                </div>
            </div>
            
            <div id="configItems">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载配置项...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 系统日志 -->
    <div class="tab-pane fade" id="logs" role="tabpanel">
        <div class="config-section">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4>系统日志</h4>
                <div>
                    <select class="form-select form-select-sm me-2" id="logTypeSelect" style="width: auto; display: inline-block;">
                        <option value="application">应用日志</option>
                        <option value="error">错误日志</option>
                        <option value="access">访问日志</option>
                    </select>
                    <select class="form-select form-select-sm me-2" id="logLinesSelect" style="width: auto; display: inline-block;">
                        <option value="50">50行</option>
                        <option value="100" selected>100行</option>
                        <option value="200">200行</option>
                        <option value="500">500行</option>
                    </select>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadSystemLogs()">
                        <i class="bi bi-arrow-clockwise me-1"></i>
                        刷新日志
                    </button>
                </div>
            </div>
            
            <div class="log-viewer" id="logViewer">
                <div class="text-center py-3">
                    <div class="spinner-border text-light" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载日志...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加配置项模态框 -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>
                    添加配置项
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="mb-3">
                        <label class="form-label">配置键名 *</label>
                        <input type="text" class="form-control" id="configKey" name="config_key" required>
                        <div class="form-text">使用点号分隔的键名，如：app.name</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">配置类型</label>
                        <select class="form-select" id="configType" name="config_type">
                            <option value="string">字符串</option>
                            <option value="integer">整数</option>
                            <option value="float">浮点数</option>
                            <option value="boolean">布尔值</option>
                            <option value="json">JSON对象</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">配置值 *</label>
                        <textarea class="form-control config-value" id="configValue" name="config_value" 
                                  rows="3" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <input type="text" class="form-control" id="configDescription" name="description">
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isPublic" name="is_public">
                        <label class="form-check-label" for="isPublic">
                            公开配置（前端可访问）
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveConfigBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    保存配置
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let systemConfigs = {};
    let configChanges = {};

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadSystemInfo();
        loadSystemConfig();
        initEventListeners();
    });

    // 初始化事件监听器
    function initEventListeners() {
        // 保存配置按钮
        document.getElementById('saveConfigBtn').addEventListener('click', saveNewConfig);

        // 日志类型和行数选择器
        document.getElementById('logTypeSelect').addEventListener('change', loadSystemLogs);
        document.getElementById('logLinesSelect').addEventListener('change', loadSystemLogs);

        // 标签切换时加载对应内容
        document.getElementById('logs-tab').addEventListener('shown.bs.tab', loadSystemLogs);
    }

    // 加载系统信息
    function loadSystemInfo() {
        API.get('/api/system/info')
            .then(response => {
                if (response.success) {
                    renderSystemInfo(response.data);
                } else {
                    Utils.showMessage('加载系统信息失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载系统信息失败:', error);
                Utils.showMessage('加载系统信息失败', 'error');
            });
    }

    // 渲染系统信息
    function renderSystemInfo(data) {
        // 系统基本信息
        const systemInfoHtml = `
            <div class="info-item">
                <span class="info-label">系统平台:</span>
                <span class="info-value">${data.system.platform} ${data.system.platform_release}</span>
            </div>
            <div class="info-item">
                <span class="info-label">主机名:</span>
                <span class="info-value">${data.system.hostname}</span>
            </div>
            <div class="info-item">
                <span class="info-label">处理器:</span>
                <span class="info-value">${data.system.processor || 'N/A'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Python版本:</span>
                <span class="info-value">${data.python.version}</span>
            </div>
            <div class="info-item">
                <span class="info-label">应用版本:</span>
                <span class="info-value">${data.application.version}</span>
            </div>
            <div class="info-item">
                <span class="info-label">运行时间:</span>
                <span class="info-value">${data.application.uptime}</span>
            </div>
        `;
        document.getElementById('systemInfo').innerHTML = systemInfoHtml;

        // 资源使用情况
        const resources = data.resources;
        const resourceInfoHtml = `
            <div class="mb-3">
                <div class="info-item">
                    <span class="info-label">CPU使用率:</span>
                    <span class="info-value">${resources.cpu_percent.toFixed(1)}%</span>
                </div>
                <div class="resource-bar">
                    <div class="resource-fill ${getResourceClass(resources.cpu_percent)}"
                         style="width: ${resources.cpu_percent}%"></div>
                </div>
            </div>
            <div class="mb-3">
                <div class="info-item">
                    <span class="info-label">内存使用率:</span>
                    <span class="info-value">${resources.memory_percent.toFixed(1)}%</span>
                </div>
                <div class="resource-bar">
                    <div class="resource-fill ${getResourceClass(resources.memory_percent)}"
                         style="width: ${resources.memory_percent}%"></div>
                </div>
            </div>
            <div class="mb-3">
                <div class="info-item">
                    <span class="info-label">磁盘使用率:</span>
                    <span class="info-value">${resources.disk_usage.percent.toFixed(1)}%</span>
                </div>
                <div class="resource-bar">
                    <div class="resource-fill ${getResourceClass(resources.disk_usage.percent)}"
                         style="width: ${resources.disk_usage.percent}%"></div>
                </div>
            </div>
            <div class="info-item">
                <span class="info-label">CPU核心数:</span>
                <span class="info-value">${resources.cpu_count}</span>
            </div>
            <div class="info-item">
                <span class="info-label">总内存:</span>
                <span class="info-value">${Utils.formatFileSize(resources.memory_total)}</span>
            </div>
            <div class="info-item">
                <span class="info-label">可用内存:</span>
                <span class="info-value">${Utils.formatFileSize(resources.memory_available)}</span>
            </div>
        `;
        document.getElementById('resourceInfo').innerHTML = resourceInfoHtml;

        // 数据库统计
        const database = data.database;
        const databaseInfoHtml = `
            <div class="row">
                <div class="col-md-3">
                    <div class="info-item">
                        <span class="info-label">总记录数:</span>
                        <span class="info-value">${database.total_records}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <span class="info-label">已完成记录:</span>
                        <span class="info-value">${database.completed_records}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <span class="info-label">总用户数:</span>
                        <span class="info-value">${database.total_users}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <span class="info-label">活跃用户:</span>
                        <span class="info-value">${database.active_users}</span>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="info-item">
                        <span class="info-label">模型配置数:</span>
                        <span class="info-value">${database.total_models}</span>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <span class="info-label">激活模型数:</span>
                        <span class="info-value">${database.active_models}</span>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('databaseInfo').innerHTML = databaseInfoHtml;
    }

    // 获取资源使用率样式类
    function getResourceClass(percent) {
        if (percent >= 90) return 'danger';
        if (percent >= 70) return 'warning';
        return '';
    }

    // 加载系统配置
    function loadSystemConfig() {
        API.get('/api/system/config')
            .then(response => {
                if (response.success) {
                    systemConfigs = response.data;
                    renderSystemConfig(response.data);
                } else {
                    Utils.showMessage('加载系统配置失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载系统配置失败:', error);
                Utils.showMessage('加载系统配置失败', 'error');
            });
    }

    // 刷新系统信息
    function refreshSystemInfo() {
        loadSystemInfo();
        Utils.showMessage('系统信息已刷新', 'success');
    }

    // 保存所有配置
    function saveAllConfigs() {
        Utils.showMessage('配置保存功能待完善', 'info');
    }

    // 添加配置项
    function addConfigItem() {
        document.getElementById('configForm').reset();
        new bootstrap.Modal(document.getElementById('configModal')).show();
    }

    // 保存新配置项
    function saveNewConfig() {
        Utils.showMessage('新配置项保存功能待完善', 'info');
    }

    // 加载系统日志
    function loadSystemLogs() {
        const logType = document.getElementById('logTypeSelect').value;
        const lines = document.getElementById('logLinesSelect').value;

        const params = {
            type: logType,
            lines: lines
        };

        API.get('/api/system/logs', params)
            .then(response => {
                if (response.success) {
                    renderSystemLogs(response.data);
                } else {
                    Utils.showMessage('加载系统日志失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载系统日志失败:', error);
                Utils.showMessage('加载系统日志失败', 'error');
            });
    }

    // 渲染系统日志
    function renderSystemLogs(data) {
        const logViewer = document.getElementById('logViewer');

        if (data.logs.length === 0) {
            logViewer.innerHTML = `
                <div class="text-center py-3">
                    <p>暂无日志内容</p>
                    <p class="small">${data.message || ''}</p>
                </div>
            `;
            return;
        }

        const logContent = data.logs.join('\n');
        logViewer.innerHTML = `
            <div class="mb-2 small opacity-75">
                日志文件: ${data.log_file} |
                显示行数: ${data.returned_lines} / ${data.total_lines}
            </div>
            <div>${logContent}</div>
        `;

        // 滚动到底部
        logViewer.scrollTop = logViewer.scrollHeight;
    }

    // 渲染系统配置
    function renderSystemConfig(configData) {
        const configItems = document.getElementById('configItems');
        let html = '';

        // 按组渲染配置项
        for (const [groupName, configs] of Object.entries(configData)) {
            if (configs.length === 0) continue;

            // 显示分组标题
            const groupTitle = getGroupDisplayName(groupName);
            html += `
                <div class="config-group">
                    <h5 class="config-group-title">
                        <i class="bi ${getGroupIcon(groupName)} me-2"></i>
                        ${groupTitle}
                    </h5>
                    <div class="card config-card">
                        <div class="card-body">
            `;

            // 渲染组内配置项
            configs.forEach(config => {
                const configId = `config-${config.key.replace(/\./g, '-')}`;
                const inputType = getInputTypeForDataType(config.data_type);

                html += `
                    <div class="config-item">
                        <div class="mb-2">
                            <span class="config-key">${config.key}</span>
                            <small class="text-muted ms-2">(${config.data_type})</small>
                            ${config.is_public ? '<span class="badge bg-primary ms-2">公开</span>' : ''}
                        </div>
                        <div class="row">
                            <div class="col-md-8">
                                <div class="input-group mb-2">
            `;

                // 根据数据类型创建不同的输入控件
                if (config.data_type === 'boolean') {
                    const checked = config.value === 'true' || config.value === true ? 'checked' : '';
                    html += `
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="${configId}" 
                                   data-key="${config.key}" ${checked} onchange="updateConfig('${config.key}', this.checked)">
                            <label class="form-check-label" for="${configId}">
                                ${checked ? '是' : '否'}
                            </label>
                        </div>
                    `;
                } else if (config.data_type === 'json') {
                    html += `
                        <textarea class="form-control config-value" id="${configId}" rows="3" 
                                  data-key="${config.key}" onchange="updateConfig('${config.key}', this.value)">${config.value}</textarea>
                    `;
                } else {
                    html += `
                        <input type="${inputType}" class="form-control config-value" id="${configId}" 
                               value="${config.value}" data-key="${config.key}" 
                               onchange="updateConfig('${config.key}', this.value)">
                    `;
                }

                html += `
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" 
                                            onclick="resetConfig('${config.key}')">
                                        <i class="bi bi-arrow-counterclockwise"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDeleteConfig('${config.key}')">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="config-description">${config.description || ''}</div>
                    </div>
                `;
            });

            html += `
                        </div>
                    </div>
                </div>
            `;
        }

        // 如果没有配置项
        if (html === '') {
            html = `
                <div class="text-center py-5">
                    <i class="bi bi-gear-wide-connected display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无配置项</h5>
                    <p class="text-muted">点击"添加配置"按钮创建第一个配置项</p>
                </div>
            `;
        }

        configItems.innerHTML = html;
    }

    // 更新配置值
    function updateConfig(key, value) {
        configChanges[key] = value;
        console.log(`配置项 ${key} 已更改为 ${value}`);
    }

    // 重置配置项
    function resetConfig(key) {
        const originalValue = getOriginalValue(key);
        const configId = `config-${key.replace(/\./g, '-')}`;
        const element = document.getElementById(configId);
        
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = originalValue === 'true' || originalValue === true;
            } else {
                element.value = originalValue;
            }
            
            // 清除更改
            delete configChanges[key];
            Utils.showMessage(`配置项 ${key} 已重置`, 'info');
        }
    }

    // 获取原始配置值
    function getOriginalValue(key) {
        // 查找原始配置值
        for (const groupName in systemConfigs) {
            const configItem = systemConfigs[groupName].find(item => item.key === key);
            if (configItem) {
                return configItem.value;
            }
        }
        return '';
    }

    // 确认删除配置
    function confirmDeleteConfig(key) {
        if (confirm(`确定要删除配置项 ${key} 吗？此操作不可撤销。`)) {
            Utils.showMessage('删除配置功能待完善', 'info');
        }
    }

    // 获取输入类型
    function getInputTypeForDataType(dataType) {
        switch (dataType) {
            case 'integer':
            case 'float':
                return 'number';
            case 'boolean':
                return 'checkbox';
            default:
                return 'text';
        }
    }

    // 获取分组显示名称
    function getGroupDisplayName(groupName) {
        const groupNames = {
            'system': '系统设置',
            'api': 'API设置',
            'ui': '界面设置',
            'storage': '存储设置',
            'security': '安全设置',
            'other': '其他设置'
        };
        return groupNames[groupName] || groupName;
    }

    // 获取分组图标
    function getGroupIcon(groupName) {
        const groupIcons = {
            'system': 'bi-gear',
            'api': 'bi-cloud',
            'ui': 'bi-palette',
            'storage': 'bi-hdd',
            'security': 'bi-shield-lock',
            'other': 'bi-three-dots'
        };
        return groupIcons[groupName] || 'bi-gear';
    }
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}用户管理 - {{ SYSTEM_NAME }}{% endblock %}

{% block extra_css %}
<style>
    .user-card {
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        transition: all 0.3s ease;
    }
    
    .user-card:hover {
        border-color: #2563eb;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .role-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-weight: 500;
    }
    
    .role-admin {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .role-analyst {
        background-color: #dbeafe;
        color: #1e40af;
    }
    
    .role-user {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-active {
        background-color: #d1fae5;
        color: #065f46;
    }
    
    .status-inactive {
        background-color: #fee2e2;
        color: #991b1b;
    }
    
    .user-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="bi bi-people me-2"></i>
        用户管理
    </h2>
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#userModal">
        <i class="bi bi-person-plus me-2"></i>
        添加用户
    </button>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="totalUsers">-</div>
                <div>总用户数</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="activeUsers">-</div>
                <div>活跃用户</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="adminUsers">-</div>
                <div>管理员</div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="analystUsers">-</div>
                <div>分析师</div>
            </div>
        </div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">角色筛选</label>
                <select class="form-select" id="roleFilter">
                    <option value="">全部角色</option>
                    <option value="admin">管理员</option>
                    <option value="analyst">分析师</option>
                    <option value="user">普通用户</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">状态筛选</label>
                <select class="form-select" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="active">活跃</option>
                    <option value="inactive">禁用</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">搜索用户</label>
                <input type="text" class="form-control" id="searchInput" placeholder="输入用户名或邮箱">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button class="btn btn-outline-primary w-100" onclick="loadUsers()">
                    <i class="bi bi-search me-2"></i>
                    搜索
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="row" id="usersList">
    <div class="col-12 text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p class="mt-2 text-muted">正在加载用户列表...</p>
    </div>
</div>

<!-- 分页 -->
<nav id="paginationContainer" style="display: none;">
    <ul class="pagination justify-content-center" id="pagination">
    </ul>
</nav>

<!-- 用户模态框 -->
<div class="modal fade" id="userModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus me-2"></i>
                    <span id="modalTitle">添加用户</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="userForm">
                    <input type="hidden" id="userId" name="user_id">
                    
                    <div class="mb-3">
                        <label class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3" id="passwordField">
                        <label class="form-label">密码 *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">密码长度至少8位，包含字母和数字</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">手机号</label>
                        <input type="tel" class="form-control" id="phone" name="phone">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">角色 *</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">请选择角色</option>
                            <option value="user">普通用户</option>
                            <option value="analyst">分析师</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            激活用户
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">
                    <i class="bi bi-check-circle me-2"></i>
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除用户 "<span id="deleteUserName"></span>" 吗？</p>
                <p class="text-muted small">此操作不可撤销，用户的所有数据将被删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-2"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>
</div>
</div> <!-- 关闭 container-fluid -->
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    const perPage = 12;
    let currentUserId = null;
    let isEditMode = false;
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadUsers();
        // loadUserStats(); // 移除旧的统计加载
        initEventListeners();
    });
    
    // 初始化事件监听器
    function initEventListeners() {
        // 保存用户按钮
        document.getElementById('saveUserBtn').addEventListener('click', saveUser);
        
        // 确认删除按钮
        document.getElementById('confirmDeleteBtn').addEventListener('click', deleteUser);
        
        // 模态框重置
        document.getElementById('userModal').addEventListener('hidden.bs.modal', resetForm);
        
        // 筛选器变化
        document.getElementById('roleFilter').addEventListener('change', loadUsers);
        document.getElementById('statusFilter').addEventListener('change', loadUsers);
        document.getElementById('searchInput').addEventListener('input', 
            Utils.debounce(loadUsers, 500));
    }
    
    // 加载用户列表
    function loadUsers(page = 1) {
        currentPage = page;
        
        const role = document.getElementById('roleFilter').value;
        const status = document.getElementById('statusFilter').value;
        const search = document.getElementById('searchInput').value.trim();
        
        const params = {
            page: page,
            per_page: perPage
        };
        
        if (role) params.role = role;
        if (status) params.status = status;
        if (search) params.search = search;
        
        API.get('/api/users', params)
            .then(response => {
                if (response.success) {
                    renderUsers(response.data); // 直接使用返回的用户数据
                    renderPagination(response.pagination);
                    
                    // 更新统计数据
                    if (response.stats) {
                        document.getElementById('totalUsers').textContent = response.stats.total;
                        document.getElementById('activeUsers').textContent = response.stats.active;
                        document.getElementById('adminUsers').textContent = response.stats.admin;
                        document.getElementById('analystUsers').textContent = response.stats.analyst;
                    }
                } else {
                    Utils.showMessage('加载用户列表失败: ' + response.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载用户列表失败:', error);
                Utils.showMessage('加载用户列表失败', 'error');
            });
    }
    
    // 加载用户统计 - 可以删除这个函数，因为统计数据已经在loadUsers中处理了
    // function loadUserStats() {
    //     API.get('/api/users/stats')
    //         .then(response => {
    //             if (response.success) {
    //                 const stats = response.data;
    //                 document.getElementById('totalUsers').textContent = stats.total;
    //                 document.getElementById('activeUsers').textContent = stats.active;
    //                 document.getElementById('adminUsers').textContent = stats.admin;
    //                 document.getElementById('analystUsers').textContent = stats.analyst;
    //             }
    //         })
    //         .catch(error => {
    //             console.error('加载用户统计失败:', error);
    //         });
    // }
    
    // 渲染用户列表
    function renderUsers(users) {
        const container = document.getElementById('usersList');
        
        if (users.length === 0) {
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="bi bi-people display-4 text-muted"></i>
                    <h5 class="mt-3 text-muted">暂无用户</h5>
                    <p class="text-muted">点击"添加用户"按钮创建第一个用户</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = '';
        
        users.forEach(user => {
            const userCard = createUserCard(user);
            container.appendChild(userCard);
        });
    }
    
    // 创建用户卡片
    function createUserCard(user) {
        const col = document.createElement('div');
        col.className = 'col-lg-6 col-xl-4 mb-4';
        
        const roleClass = getRoleClass(user.role);
        const roleText = getRoleText(user.role);
        const statusClass = user.status === 'active' ? 'status-active' : 'status-inactive';
        const statusText = user.status === 'active' ? '活跃' : '禁用';
        
        col.innerHTML = `
            <div class="card user-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="user-avatar me-3">
                            ${user.username.charAt(0).toUpperCase()}
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="mb-1">${user.username}</h6>
                            <div class="d-flex gap-2">
                                <span class="role-badge ${roleClass}">${roleText}</span>
                                <span class="role-badge ${statusClass}">${statusText}</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <small class="text-muted">邮箱:</small>
                        <div class="small">${user.email || '未设置'}</div>
                    </div>
                    <div class="mb-2">
                        <small class="text-muted">手机:</small>
                        <div class="small">${user.phone || '未设置'}</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">最后登录:</small>
                        <div class="small">${user.last_login ? Utils.formatDateTime(user.last_login) : '从未登录'}</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-primary" onclick="editUser(${user.id})">
                            <i class="bi bi-pencil me-1"></i>编辑
                        </button>
                        <button class="btn btn-sm btn-${user.status === 'active' ? 'warning' : 'success'}" 
                                onclick="toggleUserStatus(${user.id}, '${user.status}')">
                            <i class="bi bi-${user.status === 'active' ? 'pause' : 'play'} me-1"></i>
                            ${user.status === 'active' ? '禁用' : '启用'}
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="confirmDeleteUser(${user.id}, '${user.username}')">
                            <i class="bi bi-trash me-1"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    // 获取角色样式类
    function getRoleClass(role) {
        switch (role) {
            case 'admin': return 'role-admin';
            case 'analyst': return 'role-analyst';
            case 'user': return 'role-user';
            default: return 'role-user';
        }
    }
    
    // 获取角色文本
    function getRoleText(role) {
        switch (role) {
            case 'admin': return '管理员';
            case 'analyst': return '分析师';
            case 'user': return '普通用户';
            default: return role;
        }
    }
    
    // 编辑用户
    function editUser(userId) {
        API.get(`/api/users/${userId}`)
            .then(response => {
                if (response.success) {
                    fillForm(response.data);
                    isEditMode = true;
                    currentUserId = userId;
                    document.getElementById('modalTitle').textContent = '编辑用户';
                    document.getElementById('passwordField').style.display = 'none';
                    new bootstrap.Modal(document.getElementById('userModal')).show();
                }
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                Utils.showMessage('获取用户信息失败', 'error');
            });
    }
    
    // 填充表单
    function fillForm(user) {
        document.getElementById('userId').value = user.id;
        document.getElementById('username').value = user.username;
        document.getElementById('email').value = user.email || '';
        document.getElementById('phone').value = user.phone || '';
        document.getElementById('role').value = user.role;
        document.getElementById('isActive').checked = user.status === 'active';
    }
    
    // 重置表单
    function resetForm() {
        document.getElementById('userForm').reset();
        document.getElementById('userId').value = '';
        document.getElementById('modalTitle').textContent = '添加用户';
        document.getElementById('passwordField').style.display = 'block';
        document.getElementById('password').required = true;
        isEditMode = false;
        currentUserId = null;
    }
    
    // 保存用户
    function saveUser() {
        const form = document.getElementById('userForm');
        const formData = new FormData(form);
        
        const data = {
            username: formData.get('username'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            role: formData.get('role'),
            status: formData.get('is_active') === 'on' ? 'active' : 'inactive'
        };
        
        if (!isEditMode) {
            data.password = formData.get('password');
        }
        
        const saveBtn = document.getElementById('saveUserBtn');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>保存中...';
        saveBtn.disabled = true;
        
        const apiCall = isEditMode ? 
            API.put(`/api/users/${currentUserId}`, data) : 
            API.post('/api/users', data);
        
        apiCall
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('userModal')).hide();
                    loadUsers(currentPage);
                    // loadUserStats(); // 移除旧的统计加载
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('保存用户失败:', error);
                Utils.showMessage('保存用户失败', 'error');
            })
            .finally(() => {
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;
            });
    }
    
    // 切换用户状态
    function toggleUserStatus(userId, currentStatus) {
        const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
        const action = newStatus === 'active' ? '启用' : '禁用';
        
        if (confirm(`确定要${action}此用户吗？`)) {
            API.put(`/api/users/${userId}/status`, { status: newStatus })
                .then(response => {
                    if (response.success) {
                        Utils.showMessage(response.message, 'success');
                        loadUsers(currentPage);
                        // loadUserStats(); // 移除旧的统计加载
                    } else {
                        Utils.showMessage(response.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('更新用户状态失败:', error);
                    Utils.showMessage('更新用户状态失败', 'error');
                });
        }
    }
    
    // 确认删除用户
    function confirmDeleteUser(userId, username) {
        currentUserId = userId;
        document.getElementById('deleteUserName').textContent = username;
        new bootstrap.Modal(document.getElementById('deleteModal')).show();
    }
    
    // 删除用户
    function deleteUser() {
        API.delete(`/api/users/${currentUserId}`)
            .then(response => {
                if (response.success) {
                    Utils.showMessage(response.message, 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    loadUsers(currentPage);
                    // loadUserStats(); // 移除旧的统计加载
                } else {
                    Utils.showMessage(response.message, 'error');
                }
            })
            .catch(error => {
                console.error('删除用户失败:', error);
                Utils.showMessage('删除用户失败', 'error');
            });
    }
    
    // 渲染分页
    function renderPagination(pagination) {
        const container = document.getElementById('paginationContainer');
        const paginationList = document.getElementById('pagination');
        
        if (pagination.pages <= 1) {
            container.style.display = 'none';
            return;
        }
        
        container.style.display = 'block';
        paginationList.innerHTML = '';
        
        // 上一页
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${!pagination.has_prev ? 'disabled' : ''}`;
        prevItem.innerHTML = `
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page - 1})">
                <i class="bi bi-chevron-left"></i>
            </a>
        `;
        paginationList.appendChild(prevItem);
        
        // 页码
        const startPage = Math.max(1, pagination.page - 2);
        const endPage = Math.min(pagination.pages, pagination.page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === pagination.page ? 'active' : ''}`;
            pageItem.innerHTML = `
                <a class="page-link" href="#" onclick="loadUsers(${i})">${i}</a>
            `;
            paginationList.appendChild(pageItem);
        }
        
        // 下一页
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${!pagination.has_next ? 'disabled' : ''}`;
        nextItem.innerHTML = `
            <a class="page-link" href="#" onclick="loadUsers(${pagination.page + 1})">
                <i class="bi bi-chevron-right"></i>
            </a>
        `;
        paginationList.appendChild(nextItem);
    }
</script>
{% endblock %}

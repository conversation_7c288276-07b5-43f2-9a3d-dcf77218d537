{% extends "base.html" %}

{% block title %}版本数据查看 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">版本数据查看</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="analysisType">分析类型:</label>
                        <select id="analysisType" class="form-control">
                            <option value="future">未来分析</option>
                            <option value="past">历史分析</option>
                            <option value="present">现状分析</option>
                        </select>
                    </div>
                    <button class="btn btn-primary" onclick="loadVersionData()">加载版本数据</button>
                    <div id="versionData" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadVersionData() {
    const analysisType = document.getElementById('analysisType').value;
    
    fetch(`/api/test-versions/${analysisType}`)
        .then(response => response.json())
        .then(data => {
            let html = '<h6>API返回数据:</h6>';
            html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success && data.versions) {
                html += '<h6>版本列表:</h6>';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th>ID</th><th>版本号</th><th>名称</th><th>激活状态</th></tr></thead>';
                html += '<tbody>';
                data.versions.forEach(version => {
                    html += `<tr>
                        <td>${version.id}</td>
                        <td>${version.version}</td>
                        <td>${version.name}</td>
                        <td>${version.is_active ? '是' : '否'}</td>
                    </tr>`;
                });
                html += '</tbody></table>';
            }
            
            document.getElementById('versionData').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('versionData').innerHTML = 
                '<div class="alert alert-danger">错误: ' + error.message + '</div>';
        });
}
</script>
{% endblock %}
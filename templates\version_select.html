{% extends "base.html" %}

{% block title %}版本选择测试 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">版本选择测试</h5>
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="analysisType">分析类型:</label>
                        <select id="analysisType" class="form-control">
                            <option value="future">未来分析</option>
                            <option value="past">历史分析</option>
                            <option value="present">现状分析</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="versionSelect">版本选择:</label>
                        <select id="versionSelect" class="form-control">
                            <option value="">加载中...</option>
                        </select>
                    </div>
                    <div id="versionInfo" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="{{ url_for('static', filename='js/override_functions.js') }}"></script>
<script src="{{ url_for('static', filename='js/hardcoded_versions.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 监听分析类型变化
    document.getElementById('analysisType').addEventListener('change', function() {
        loadPromptConfigWithHardcoded();
    });
    
    // 监听版本选择变化
    document.getElementById('versionSelect').addEventListener('change', function() {
        const selectedVersion = allVersions.find(v => v.id == this.value);
        if (selectedVersion) {
            document.getElementById('versionInfo').innerHTML = 
                '<div class="alert alert-info"><pre>' + JSON.stringify(selectedVersion, null, 2) + '</pre></div>';
        }
    });
    
    // 初始加载
    loadPromptConfigWithHardcoded();
});
</script>
{% endblock %}
{% extends "base.html" %}

{% block title %}版本测试 - {{ SYSTEM_NAME }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">版本测试页面</h5>
                </div>
                <div class="card-body">
                    <p>这是一个测试页面，用于验证版本管理功能。</p>
                    <button class="btn btn-primary" onclick="testLoadVersions()">测试加载版本</button>
                    <div id="testResults" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testLoadVersions() {
    fetch('/api/test-versions/future')
        .then(response => response.json())
        .then(data => {
            document.getElementById('testResults').innerHTML = 
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('testResults').innerHTML = 
                '<div class="alert alert-danger">错误: ' + error.message + '</div>';
        });
}
</script>
{% endblock %}
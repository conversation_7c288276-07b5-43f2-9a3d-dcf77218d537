{"summary": {"first_batch": {"file": "./test_output/batch_results\\test_summary_1.json", "total_transactions": 1, "total_files": 1}, "second_batch": {"file": "./test_output/batch_results\\test_summary_2.json", "total_transactions": 2, "total_files": 2}, "differences": {"transaction_count_diff": 1, "file_count_diff": 1}}, "file_level_comparison": {"test1.pdf": {"first_batch_transactions": 1, "second_batch_transactions": 1, "difference": 0}}, "new_files": [{"file_name": "test2.pdf", "transaction_count": 1}], "removed_files": [], "modified_files": []}
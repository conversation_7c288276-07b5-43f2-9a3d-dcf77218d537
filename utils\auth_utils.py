# -*- coding: utf-8 -*-
"""
认证和权限工具函数
"""
from functools import wraps
from flask import jsonify, request
from flask_login import current_user

# 角色权限映射
ROLE_PERMISSIONS = {
    'user': [
        'upload', 'view_own_records', 'view_dashboard', 'edit_records'
    ],
    'analyst': [
        'upload', 'view_own_records', 'view_all_records',
        'tag_files', 'reanalyze', 'view_dashboard',
        'export_data', 'manage_tags', 'edit_records'
    ],
    'admin': [
        'all_permissions'  # 管理员拥有所有权限
    ]
}

def has_permission(user, permission):
    """检查用户是否有指定权限"""
    if not user or not user.is_authenticated:
        return False
    
    # 管理员拥有所有权限
    if user.role == 'admin':
        return True
    
    # 检查角色权限
    user_permissions = ROLE_PERMISSIONS.get(user.role, [])
    return permission in user_permissions

def require_permission(permission):
    """权限装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'success': False, 'message': '需要登录'}), 401
                return jsonify({'success': False, 'message': '需要登录'}), 401
            
            if not has_permission(current_user, permission):
                if request.is_json:
                    return jsonify({'success': False, 'message': '权限不足'}), 403
                return jsonify({'success': False, 'message': '权限不足'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def require_role(role):
    """角色装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                if request.is_json:
                    return jsonify({'success': False, 'message': '需要登录'}), 401
                return jsonify({'success': False, 'message': '需要登录'}), 401
            
            if current_user.role != role and current_user.role != 'admin':
                if request.is_json:
                    return jsonify({'success': False, 'message': '权限不足'}), 403
                return jsonify({'success': False, 'message': '权限不足'}), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({'success': False, 'message': '需要登录'}), 401
            return jsonify({'success': False, 'message': '需要登录'}), 401
        
        if current_user.role != 'admin':
            if request.is_json:
                return jsonify({'success': False, 'message': '需要管理员权限'}), 403
            return jsonify({'success': False, 'message': '需要管理员权限'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def analyst_required(f):
    """分析师权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            if request.is_json:
                return jsonify({'success': False, 'message': '需要登录'}), 401
            return jsonify({'success': False, 'message': '需要登录'}), 401
        
        if current_user.role not in ['analyst', 'admin']:
            if request.is_json:
                return jsonify({'success': False, 'message': '需要分析师权限'}), 403
            return jsonify({'success': False, 'message': '需要分析师权限'}), 403
        
        return f(*args, **kwargs)
    return decorated_function

def get_user_permissions(user):
    """获取用户权限列表"""
    if not user or not user.is_authenticated:
        return []
    
    if user.role == 'admin':
        # 管理员拥有所有权限
        all_permissions = set()
        for permissions in ROLE_PERMISSIONS.values():
            if permissions != ['all_permissions']:
                all_permissions.update(permissions)
        return list(all_permissions)
    
    return ROLE_PERMISSIONS.get(user.role, [])

def can_access_record(user, record):
    """检查用户是否可以访问指定记录"""
    if not user or not user.is_authenticated:
        return False
    
    # 管理员和分析师可以访问所有记录
    if user.role in ['admin', 'analyst']:
        return True
    
    # 普通用户只能访问自己创建的记录
    return record.created_by == user.id

def can_modify_record(user, record):
    """检查用户是否可以修改指定记录"""
    if not user or not user.is_authenticated:
        return False
    
    # 管理员可以修改所有记录
    if user.role == 'admin':
        return True
    
    # 分析师可以修改所有记录（除了删除）
    if user.role == 'analyst':
        return True
    
    # 普通用户只能修改自己创建的记录
    return record.created_by == user.id

def can_delete_record(user, record):
    """检查用户是否可以删除指定记录"""
    if not user or not user.is_authenticated:
        return False
    
    # 只有管理员可以删除记录
    return user.role == 'admin'

def can_review_record(user):
    """检查用户是否可以复核记录"""
    if not user or not user.is_authenticated:
        return False
    
    # 只有管理员可以复核
    return user.role == 'admin'

def get_accessible_analysis_types(user):
    """获取用户可访问的分析类型"""
    from flask import current_app
    
    if not user or not user.is_authenticated:
        return []
    
    # 所有登录用户都可以访问所有分析类型
    return list(current_app.config.get('ANALYSIS_TYPES', {}).keys())

def log_user_activity(user, action, resource=None, details=None):
    """记录用户活动"""
    if not user or not user.is_authenticated:
        return
    
    try:
        from models import UserActivity, db
        from flask import request
        import uuid
        
        activity = UserActivity(
            user_id=user.id,
            session_id=request.cookies.get('session'),
            request_id=str(uuid.uuid4()),
            action=action,
            resource=resource,
            details=details,
            ip_address=request.remote_addr,
            user_agent=request.headers.get('User-Agent')
        )
        
        db.session.add(activity)
        db.session.commit()
    except Exception:
        # 记录活动失败不应该影响主要功能
        pass

def validate_password_strength(password):
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    
    # 可以添加更多密码强度检查
    # 如：包含大小写字母、数字、特殊字符等
    
    return True, "密码强度合格"

def generate_session_token():
    """生成会话令牌"""
    import secrets
    return secrets.token_urlsafe(32)

def is_safe_url(target):
    """检查URL是否安全"""
    from urllib.parse import urlparse, urljoin
    from flask import request
    
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    
    return test_url.scheme in ('http', 'https') and ref_url.netloc == test_url.netloc

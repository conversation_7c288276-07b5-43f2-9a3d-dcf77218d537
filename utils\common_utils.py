# -*- coding: utf-8 -*-
"""
通用工具函数模块
"""

from datetime import datetime, timedelta
import os
import hashlib
import uuid
import re


class Utils:
    """工具类"""
    
    @staticmethod
    def format_datetime(dt):
        """格式化日期时间"""
        if not dt:
            return ''
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def format_date(dt):
        """格式化日期"""
        if not dt:
            return ''
        return dt.strftime('%Y-%m-%d')
    
    @staticmethod
    def format_time_ago(dt):
        """格式化相对时间"""
        if not dt:
            return ''
        
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            if diff.days == 1:
                return '1天前'
            elif diff.days < 30:
                return f'{diff.days}天前'
            elif diff.days < 365:
                months = diff.days // 30
                return f'{months}个月前'
            else:
                years = diff.days // 365
                return f'{years}年前'
        
        seconds = diff.seconds
        if seconds < 60:
            return '刚刚'
        elif seconds < 3600:
            minutes = seconds // 60
            return f'{minutes}分钟前'
        else:
            hours = seconds // 3600
            return f'{hours}小时前'
    
    @staticmethod
    def format_file_size(size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return '0 B'
        
        size_names = ['B', 'KB', 'MB', 'GB', 'TB']
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f'{size_bytes:.1f} {size_names[i]}'
    
    @staticmethod
    def generate_uuid():
        """生成UUID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_short_id(length=8):
        """生成短ID"""
        return str(uuid.uuid4()).replace('-', '')[:length]
    
    @staticmethod
    def hash_password(password, salt=None):
        """密码哈希"""
        if salt is None:
            salt = os.urandom(32)
        
        pwdhash = hashlib.pbkdf2_hmac('sha256',
                                      password.encode('utf-8'),
                                      salt,
                                      100000)
        return salt + pwdhash
    
    @staticmethod
    def verify_password(stored_password, provided_password):
        """验证密码"""
        salt = stored_password[:32]
        stored_hash = stored_password[32:]
        pwdhash = hashlib.pbkdf2_hmac('sha256',
                                      provided_password.encode('utf-8'),
                                      salt,
                                      100000)
        return pwdhash == stored_hash
    
    @staticmethod
    def is_valid_email(email):
        """验证邮箱格式"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def is_valid_phone(phone):
        """验证手机号格式"""
        pattern = r'^1[3-9]\d{9}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def sanitize_filename(filename):
        """清理文件名"""
        # 移除或替换不安全的字符
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # 限制长度
        if len(filename) > 255:
            name, ext = os.path.splitext(filename)
            filename = name[:255-len(ext)] + ext
        return filename
    
    @staticmethod
    def get_file_extension(filename):
        """获取文件扩展名"""
        return os.path.splitext(filename)[1].lower()
    
    @staticmethod
    def is_allowed_file(filename, allowed_extensions):
        """检查文件扩展名是否允许"""
        return Utils.get_file_extension(filename) in allowed_extensions
    
    @staticmethod
    def truncate_text(text, max_length=100, suffix='...'):
        """截断文本"""
        if len(text) <= max_length:
            return text
        return text[:max_length - len(suffix)] + suffix
    
    @staticmethod
    def parse_bool(value):
        """解析布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on')
        return bool(value)
    
    @staticmethod
    def safe_int(value, default=0):
        """安全转换为整数"""
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def safe_float(value, default=0.0):
        """安全转换为浮点数"""
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    
    @staticmethod
    def get_client_ip(request):
        """获取客户端IP地址"""
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr
    
    @staticmethod
    def mask_sensitive_data(data, mask_char='*'):
        """掩码敏感数据"""
        if not data or len(data) < 4:
            return mask_char * len(data) if data else ''
        
        # 保留前2位和后2位
        return data[:2] + mask_char * (len(data) - 4) + data[-2:]
    
    @staticmethod
    def calculate_accuracy(correct, total):
        """计算准确率"""
        if total == 0:
            return 0.0
        return round((correct / total) * 100, 2)
    
    @staticmethod
    def get_percentage_color(percentage):
        """根据百分比获取颜色"""
        if percentage >= 90:
            return 'success'
        elif percentage >= 70:
            return 'warning'
        else:
            return 'danger'

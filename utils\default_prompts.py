# -*- coding: utf-8 -*-
"""
默认提示词管理模块
从各个Notebook文件中提取的默认提示词
"""

# 期货账户/期货交易会员解析默认提示词
FUTURES_ACCOUNT_PROMPT = """你是一名期货开户文件解析专家。请严格区分"会员号"（固定 4 位数字）和"交易编码"（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。

=====================
【必须提取的字段】
1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称
2. 资金账号：资金账户号码
3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填"/"）
4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为"投机"）
5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填"/"）
6. 结束时间：文件内表明的截止日期(如果有)，取不到则为"/"（YYYY-MM-DD；缺失填"/"）

=====================
【交易所名称映射】
- 上期所＝上海期货交易所／上海交易所
- 大商所＝大连商品交易所／大连交易所
- 郑商所＝郑州商品交易所／郑州交易所
- 中金所＝中国金融期货交易所／金融交易所
- 上能所＝上海能源交易所／能源中心
- 广期所＝广州期货交易所／广州交易所

=====================
【账户用途映射】
- 投机＝投机交易账户
- 套利＝套利交易账户
- 套保＝套期保值交易账户

若文档未写明用途，默认"投机"。

=====================
【关键区别提醒】
- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。
- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。
- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。
- 发现长度不符（5–7 位或 9 位等）则忽略该数字。

=====================
【输出 JSON 格式示例】
```json
{
  "产品名称": "金瑞同进尊享1号FOF单一资产管理计划",
  "资金账号": "2120061",
  "会员号": {
    "上期所": "0121",
    "大商所": "/",
    "郑商所": "0059",
    "中金所": "0170",
    "上能所": "8059",
    "广期所": "0021"
  },
  "交易编码": {
    "上期所": {"投机": "81010373", "套利": "/", "套保": "/"},
    "大商所": {"投机": "/", "套利": "/", "套保": "/"},
    "郑商所": {"投机": "99871700", "套利": "/", "套保": "/"},
    "中金所": {"投机": "00185013", "套利": "/", "套保": "/"},
    "上能所": {"投机": "81010376", "套利": "/", "套保": "/"},
    "广期所": {"投机": "04471686", "套利": "/", "套保": "/"}
  },
  "开始时间": "2025-01-01",
  "结束时间": "/"
}
```"""

# 券商账户计息变更默认提示词
BROKER_INTEREST_PROMPT = """请从图片中提取券商账户计息变更的相关信息，严格按照以下格式输出JSON数组：

=====================【必须提取的字段】=====================
1. **产品名称**：具体的产品或资产名称
2. **产品类别**：区分是"单产品"还是"全公司产品"等
3. **利率(年化)**：
   • 如果是统一利率，格式为 `{"all": "X.XX%"}`
   • 如果按客户类型分段，格式为 `{"个人": "X.XX%", "非个人": "X.XX%"}`
   • 如果按时间分段，格式为 `{"START:YYYY-MM-DD": "X.XX%", "YYYY-MM-DD:END": "X.XX%"}`
4. **开始时间**：变更生效的开始日期（YYYY-MM-DD格式）
5. **截止时间**：变更的截止日期，如无明确截止则填""  
6. **计息天数**：年化计息的天数基础（如360天、365天等）
7. **备注**：其他重要信息

=====================【输出JSON数组示例】=====================
```json
[
  {
    "产品名称": "汇添富远景成长一年持有期混合型基金",
    "产品类别": "单产品",
    "利率(年化)": {"all": "1.4%"},
    "开始时间": "2025-01-02",
    "截止时间": "",
    "计息天数": 360,
    "备注": "按月20日前结息至期货账户"
  }
]
```"""

# 宁银理财费用变更默认提示词
NINGYIN_FEE_PROMPT = """请从图片中提取宁银理财费用变更的相关信息，严格按照JSON格式输出：

请以JSON格式输出，格式如下：
```json
{
  "产品信息": {
    "产品名称": "产品名称",
    "产品代码": "产品代码"
  },
  "费用变更信息": {
    "变更类型": "费用类型",
    "原费率": "原费率",
    "新费率": "新费率",
    "生效日期": "生效日期",
    "执行说明": "执行说明"
  }
}
```"""

# 理财产品说明书默认提示词
FINANCIAL_PRODUCT_PROMPT = """请从图片中提取理财产品说明书的相关信息，严格按照JSON格式输出：

请以JSON格式输出，格式如下：
```json
{
  "产品信息": {
    "产品名称": "产品名称",
    "产品代码": "产品代码",
    "产品类型": "产品类型",
    "管理人": "管理人",
    "托管人": "托管人",
    "登记备案": "登记备案",
    "成立日期": "成立日期",
    "产品期限": "产品期限",
    "投资范围": "投资范围",
    "预警线": "预警线",
    "止损线": "止损线"
  },
  "费率结构": {
    "认购费": "认购费",
    "申购费": "申购费",
    "赎回费": "赎回费",
    "销售服务费": "销售服务费",
    "固定管理费": "固定管理费",
    "浮动管理费": "浮动管理费",
    "托管费": "托管费"
  },
  "业绩比较基准": "业绩比较基准",
  "估值方法": "估值方法",
  "开放期安排": "开放期安排"
}
```"""

# 账户开户场景默认提示词
ACCOUNT_OPENING_PROMPT = """你是一名账户开户场景解析专家。请从用户提供的开户文件中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 客户名称：开户客户的真实姓名或机构名称
2. 客户类型：个人/机构/产品
3. 证件类型：身份证/护照/营业执照等
4. 证件号码：客户提供的证件号码
5. 联系方式：手机号码或联系电话
6. 联系地址：客户的联系地址
7. 账户类型：需要区分是哪种类型的账户
8. 开户银行：办理开户的银行名称
9. 银行卡号：客户的银行卡号码
10. 开户日期：格式为 YYYY-MM-DD"""

# 非标交易确认单解析默认提示词
NON_STANDARD_TRADE_PROMPT = """你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 投资者名称：通常指代客户姓名，一般是资管计划的名称
2. 投资者账号：通常指代客户的资金账号
3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填"/"）
4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回
5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等
6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填"/"）
7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填"/"）
8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填"/"）"""

# 产品手册默认提示词
PRODUCT_MANUAL_PROMPT = """你是一名产品手册解析专家。请从用户提供的产品手册文档中提取以下信息，并用JSON格式返回。

=====================
【必须提取的字段】
1. 产品名称：产品的正式名称
2. 产品代码：产品的唯一标识码
3. 产品管理人：负责管理产品的机构名称
4. 产品托管人：负责托管产品资产的机构
5. 产品类型：如固定收益、权益类、混合类等
6. 投资期限：产品的投资周期长度
7. 风险等级：产品的风险评级
8. 募集规模：计划募集的资金规模
9. 投资门槛：最低投资金额要求
10. 业绩比较基准：产品业绩的参考标准"""

# 分析类型与默认提示词映射
DEFAULT_PROMPTS = {
    # 标准分析类型
    'futures_account': FUTURES_ACCOUNT_PROMPT,
    'wealth_management': FINANCIAL_PRODUCT_PROMPT,
    'broker_interest': BROKER_INTEREST_PROMPT,
    'account_opening': ACCOUNT_OPENING_PROMPT,
    'ningyin_fee': NINGYIN_FEE_PROMPT,
    'non_standard_trade': NON_STANDARD_TRADE_PROMPT,

    # 兼容旧类型（已废弃）
    # 'future': FUTURES_ACCOUNT_PROMPT,  # 已统一为futures_account
    # 'futures_member': FUTURES_ACCOUNT_PROMPT,  # 已统一为futures_account
    # 'financial': FINANCIAL_PRODUCT_PROMPT,  # 已统一为wealth_management
    # 'product_manual': PRODUCT_MANUAL_PROMPT  # 未使用的类型
}

def get_default_prompt(analysis_type):
    """获取指定分析类型的默认提示词"""
    return DEFAULT_PROMPTS.get(analysis_type, f"这是{analysis_type}类型的默认系统提示词")

def get_all_default_prompts():
    """获取所有默认提示词"""
    return DEFAULT_PROMPTS
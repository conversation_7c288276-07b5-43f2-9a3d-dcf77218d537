# -*- coding: utf-8 -*-
"""
文件处理工具函数
"""
import os
import uuid
import hashlib
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'})
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def get_upload_folder(analysis_type):
    """根据分析类型获取对应的上传目录"""
    upload_base = current_app.config.get('UPLOAD_BASE', 'uploads')
    return os.path.join(upload_base, analysis_type)

def get_processed_folder(analysis_type):
    """根据分析类型获取对应的处理目录"""
    processed_base = current_app.config.get('PROCESSED_BASE', 'processed')
    return os.path.join(processed_base, analysis_type)

def generate_unique_filename(original_filename, analysis_type):
    """生成唯一的文件名"""
    # 获取文件扩展名
    if '.' in original_filename:
        name, ext = original_filename.rsplit('.', 1)
        ext = ext.lower()
    else:
        name = original_filename
        ext = ''
    
    # 生成时间戳和随机字符串
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    random_str = str(uuid.uuid4()).replace('-', '')[:8]
    
    # 构造新文件名
    if ext:
        new_filename = f"{timestamp}_{random_str}_{secure_filename(name)}.{ext}"
    else:
        new_filename = f"{timestamp}_{random_str}_{secure_filename(name)}"
    
    return new_filename

def save_uploaded_file(file, analysis_type):
    """保存上传的文件"""
    if not file or not allowed_file(file.filename):
        raise ValueError("不支持的文件类型")
    
    # 生成唯一文件名
    new_filename = generate_unique_filename(file.filename, analysis_type)
    
    # 获取保存路径
    upload_folder = get_upload_folder(analysis_type)
    os.makedirs(upload_folder, exist_ok=True)
    
    file_path = os.path.join(upload_folder, new_filename)
    
    # 保存文件
    file.save(file_path)
    
    return {
        'filename': new_filename,
        'filepath': file_path,
        'original_filename': file.filename,
        'file_size': os.path.getsize(file_path)
    }

def calculate_file_hash(file_path):
    """计算文件哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return None

def get_file_info(file_path):
    """获取文件信息"""
    if not os.path.exists(file_path):
        return None

    stat = os.stat(file_path)
    file_info = {
        'file_size': stat.st_size,
        'created_time': datetime.fromtimestamp(stat.st_ctime).strftime('%Y-%m-%d %H:%M:%S'),
        'modified_time': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
        'file_hash': calculate_file_hash(file_path)
    }
    
    # 如果是PDF文件，获取页数
    if file_path.lower().endswith('.pdf'):
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(file_path)
            file_info['page_count'] = len(doc)
            file_info['file_type'] = 'pdf'
            doc.close()
        except Exception:
            file_info['page_count'] = 0
            file_info['file_type'] = 'pdf'
    else:
        file_info['file_type'] = 'image'
        file_info['page_count'] = 1
    
    return file_info

def delete_file(file_path):
    """删除文件"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
    except Exception:
        pass
    return False

def get_file_extension(filename):
    """获取文件扩展名"""
    if '.' in filename:
        return filename.rsplit('.', 1)[1].lower()
    return ''

def is_pdf_file(filename):
    """判断是否为PDF文件"""
    return get_file_extension(filename) == 'pdf'

def is_image_file(filename):
    """判断是否为图片文件"""
    image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}
    return get_file_extension(filename) in image_extensions

def ensure_directory_exists(directory):
    """确保目录存在"""
    if not os.path.exists(directory):
        os.makedirs(directory, exist_ok=True)

def get_relative_path(file_path, base_path=None):
    """获取相对路径"""
    if base_path is None:
        base_path = os.getcwd()
    
    try:
        return os.path.relpath(file_path, base_path)
    except ValueError:
        return file_path

def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"

def check_file_duplicate(file_hash, analysis_type, db_service):
    """检查文件是否重复"""
    from models import AnalysisRecord
    
    existing_record = AnalysisRecord.query.filter_by(
        file_hash=file_hash,
        analysis_type=analysis_type,
        file_status='active'
    ).first()
    
    return existing_record

def cleanup_old_files(analysis_type, days_old=30):
    """清理旧文件"""
    from datetime import timedelta
    from models import AnalysisRecord
    
    cutoff_date = datetime.utcnow() - timedelta(days=days_old)
    
    old_records = AnalysisRecord.query.filter(
        AnalysisRecord.analysis_type == analysis_type,
        AnalysisRecord.file_status == 'archived',
        AnalysisRecord.created_at < cutoff_date
    ).all()
    
    deleted_count = 0
    for record in old_records:
        try:
            # 删除文件
            upload_folder = get_upload_folder(analysis_type)
            file_path = os.path.join(upload_folder, record.filename)
            if delete_file(file_path):
                deleted_count += 1
        except Exception:
            continue
    
    return deleted_count

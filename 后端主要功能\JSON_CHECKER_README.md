# JSON文件批量校对系统

## 🚀 系统简介

这是一个专为智能体门户系统设计的JSON文件批量校对工具，采用现代化的金融量化交易平台设计风格，提供高效、精确的JSON文件比较与分析功能。

## ✨ 主要功能

### 📊 第一个界面 - 统计概览
- **文件正确率**: 显示完全匹配的文件占比
- **各个字段正确率**: 展示每个字段的准确率统计
- **全字段正确率**: 显示正确字段总数与全部字段总数的比例
- **可视化图表**: 直观展示各字段准确率分析

### 🔍 第二个界面 - 详细对比
- **文件列表**: 快速浏览所有JSON文件的校对状态
- **点击展开**: 点击任意文件查看详细对比内容
- **并排对比**: 标准答案与测试内容的并排显示
- **差异高亮**: 自动高亮不匹配的字段和值
- **联动滚动**: 标准答案和测试结果面板同步滚动
- **自动定位**: 点击错误文件时自动滚动到第一个错误位置

### 🕒 时间目录管理
- **时间目录选择**: 支持按时间目录组织的测试结果
- **自动选择最新**: 默认选择最新的时间目录
- **时间格式化**: 友好显示时间目录（YYYYMMDDHHMMSS格式）

## 🛠 技术特性

- ✅ **嵌套结构支持**: 完美处理多层嵌套的JSON对象
- ✅ **通用性设计**: 适用于任何JSON文件结构
- ✅ **时间目录支持**: 支持按时间组织的测试结果文件
- ✅ **联动滚动**: 双面板同步滚动查看
- ✅ **智能定位**: 自动定位到第一个错误字段
- ✅ **实时搜索**: 支持文件名快速搜索过滤
- ✅ **数据导出**: 支持校对报告的JSON格式导出
- ✅ **响应式设计**: 适配不同屏幕尺寸的设备

## 📁 文件结构

```
项目根目录/
├── json_checker_app.py      # Flask后端应用
├── start_json_checker.sh    # 启动脚本
├── requirements.txt         # Python依赖包
├── templates/
│   └── index.html          # 主页面模板
├── static/
│   ├── css/
│   │   └── style.css       # 样式文件
│   └── js/
│       └── app.js          # JavaScript应用逻辑
├── answer/                  # 标准答案文件夹
│   └── [文件夹名]/
│       ├── *.json          # 标准答案JSON文件
│       └── fn_md5_dict.json # (自动排除)
└── check/                   # 测试文件文件夹
    └── [文件夹名]/         # 与answer中的文件夹名相同
        └── [时间目录]/      # 格式: YYYYMMDDHHMMSS
            └── *.json       # 待校对JSON文件
```

## 🚀 快速开始

### 方法一：使用启动脚本（推荐）

1. **给启动脚本添加执行权限**：
   ```bash
   chmod +x start_json_checker.sh
   ```

2. **运行启动脚本**：
   ```bash
   ./start_json_checker.sh
   ```

### 方法二：手动启动

1. **创建虚拟环境**：
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

2. **安装依赖包**：
   ```bash
   pip install -r requirements.txt \
     --trusted-host pypi.tuna.tsinghua.edu.cn \
     --trusted-host pypi.org \
     --trusted-host files.pythonhosted.org
   ```

3. **启动应用**：
   ```bash
   python3 json_checker_app.py
   ```

4. **访问系统**：
   打开浏览器访问 `http://localhost:5106`

## 📋 使用说明

### 1. 准备数据文件

确保您的文件结构如下：
```
answer/
└── 您的文件夹名/
    ├── file1.json
    ├── file2.json
    └── ...

check/
└── 您的文件夹名/          # 与answer中的文件夹名相同
    ├── 20241201120000/    # 时间目录（YYYYMMDDHHMMSS格式）
    │   ├── file1.json
    │   ├── file2.json
    │   └── ...
    └── 20241201130000/    # 另一个时间目录
        ├── file1.json
        └── ...
```

### 2. 操作步骤

1. **选择文件夹**: 在左侧边栏选择要校对的文件夹
2. **选择时间目录**: 系统会自动加载时间目录列表，默认选择最新的
3. **开始校对**: 点击"开始校对"按钮执行批量比较
4. **查看统计**: 在统计界面查看整体校对结果
5. **详细分析**: 点击"查看详情"进入详细对比界面
6. **查看文件差异**: 点击任意文件查看具体对比，系统会自动滚动到第一个错误位置
7. **导出报告**: 点击"导出报告"保存校对结果

### 3. 界面功能说明

#### 统计概览界面
- **文件正确率卡片**: 显示完全匹配的文件数量和占比
- **全字段正确率卡片**: 显示所有文件中正确字段与总字段的比例
- **字段统计卡片**: 显示检测到的不同字段类型数量
- **字段准确率图表**: 柱状图显示每个字段的准确率

#### 详细对比界面
- **文件列表**: 左侧显示所有文件及其校对状态
- **搜索功能**: 支持按文件名搜索过滤
- **对比面板**: 右侧并排显示标准答案和测试内容
- **联动滚动**: 两个面板同步滚动，方便查看对应内容
- **自动定位**: 点击错误文件时自动滚动到第一个错误字段
- **差异高亮**: 不同颜色标记匹配、不匹配和缺失的字段
- **动画效果**: 错误字段有脉冲动画提示，首次定位有高亮效果

## 🎨 设计风格

系统采用高端金融量化交易平台的视觉设计：

- **主色调**: 深邃钢铁蓝（#0A1F44）、午夜灰（#121925）
- **点缀色**: 金属银（#C0C0C0）、闪耀金（#FFD700）
- **玻璃质感**: 半透明卡片设计，支持毛玻璃效果
- **现代排版**: Inter字体，简洁大气的布局
- **响应式**: 适配桌面、平板、手机等各种设备
- **动画效果**: 流畅的过渡动画和交互反馈

## 🔧 配置说明

### 支持的JSON特性
- ✅ 基本数据类型 (string, number, boolean, null)
- ✅ 数组类型
- ✅ 嵌套对象（无限层级）
- ✅ 混合类型结构

### 比较算法
- **扁平化处理**: 将嵌套JSON转换为扁平结构进行比较
- **字段级对比**: 逐字段比较，支持缺失和多余字段检测
- **准确率计算**: 基于字段匹配数量计算准确率
- **容错处理**: 优雅处理格式错误的JSON文件

### 时间目录格式
- **目录命名**: 必须严格按照 `YYYYMMDDHHMMSS` 格式命名
- **自动排序**: 系统按时间倒序显示，最新的在前
- **自动选择**: 默认选择最新的时间目录

## 📊 输出格式

### 统计数据结构
```json
{
  "summary": {
    "total_files": 15,
    "correct_files": 12,
    "file_accuracy": 80.0,
    "field_accuracy_stats": {
      "产品名称": 100.0,
      "资金账号": 95.5,
      "会员号.上期所": 90.0
    },
    "all_field_accuracy": 85.2
  },
  "time_dir": "20241201120000"
}
```

### 详细对比数据
```json
{
  "filename": "example.json",
  "is_identical": false,
  "field_accuracy": 85.5,
  "field_comparisons": {
    "产品名称": {
      "standard": "标准值",
      "check": "测试值",
      "is_match": true
    }
  }
}
```

## 🚨 注意事项

1. **文件排除**: 系统会自动排除 `fn_md5_dict.json` 文件
2. **文件对应**: answer和check文件夹中必须有相同的子文件夹名
3. **时间目录格式**: check目录下的时间目录必须按 `YYYYMMDDHHMMSS` 格式命名
4. **编码格式**: JSON文件必须使用UTF-8编码
5. **内存使用**: 大文件比较时注意内存占用
6. **网络连接**: 首次安装需要网络连接下载依赖包
7. **浏览器兼容**: 建议使用现代浏览器以获得最佳体验

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案: 修改json_checker_app.py中的端口号
   app.run(debug=True, host='0.0.0.0', port=5001)
   ```

2. **依赖安装失败**
   ```bash
   # 尝试使用清华镜像源
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

3. **JSON解析错误**
   ```
   检查JSON文件格式是否正确，使用在线JSON验证工具检查
   ```

4. **时间目录未识别**
   ```
   确保时间目录严格按照YYYYMMDDHHMMSS格式命名（14位数字）
   ```

5. **联动滚动失效**
   ```
   刷新页面重新加载，或检查浏览器控制台是否有JavaScript错误
   ```

## 🔄 版本更新

### v1.1.0 (当前版本)
- ✅ 支持时间目录选择功能
- ✅ 联动滚动功能
- ✅ 自动定位第一个错误
- ✅ 动画效果增强
- ✅ 滚动条样式优化

### v1.0.0
- ✅ 基础JSON文件比较功能
- ✅ 统计概览界面
- ✅ 详细对比界面
- ✅ 嵌套结构支持
- ✅ 金融量化交易平台设计风格
- ✅ 响应式设计
- ✅ 数据导出功能

## 📝 开发说明

### 扩展功能建议
- [ ] 支持更多文件格式 (YAML, XML)
- [ ] 添加差异统计图表
- [ ] 支持批量时间目录处理
- [ ] 添加定时任务功能
- [ ] 集成邮件通知
- [ ] 支持自定义时间目录格式

### 技术栈
- **后端**: Flask 2.3.3
- **前端**: HTML5 + CSS3 + JavaScript ES6+
- **UI框架**: 自定义CSS（金融量化交易平台风格）
- **字体**: Inter, Font Awesome 6.0
- **Python版本**: 3.7+

## 📧 联系方式

如有问题或建议，请通过以下方式联系：
- 项目维护: 智能体门户系统团队
- 系统版本: v1.1.0
- 更新时间: 2024年

---

**© 2024 智能体门户系统 - JSON文件批量校对系统** 
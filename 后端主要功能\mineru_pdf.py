import os
import requests
from PIL import Image

# 后端服务URL
BACKEND_URL = "http://**************:30000"


def trans_pdf_to_markdown(file_path, 
               server_url=BACKEND_URL,
               return_md=True,
               return_images=True,
               return_middle_json=True,
               return_model_output=False,
               return_content_list=False,
               start_page_id=0,
               end_page_id=99999,
               parse_method='auto',
               lang_list='ch',
               output_dir='./output',
               backend='pipeline',
               table_enable=True,
               formula_enable=True,
               convert_to_scanned=False) -> dict:
    """
    封装文件解析接口
    
    :param file_path: 要上传的文件路径
    :param server_url: 接口URL，默认为'http://**************:30000/file_parse'
    :param return_md: 是否返回markdown格式，默认为True
    :param return_images: 是否返回图片，默认为False
    :param return_middle_json: 是否返回中间JSON，默认为False
    :param return_model_output: 是否返回模型输出，默认为False
    :param return_content_list: 是否返回内容列表，默认为False
    :param start_page_id: 起始页码，默认为0
    :param end_page_id: 结束页码，默认为99999
    :param parse_method: 解析方法，默认为'auto'，可选值为'auto'、'ocr'、'text'
    :param lang_list: 语言列表，默认为'ch'
    :param output_dir: 输出目录，默认为'./output'
    :param backend: 后端类型，默认为'pipeline'，可选值为'pipeline'、'vlm-transformers'、'vlm-sglang-engine'、'vlm-sglang-client'
    :param table_enable: 是否启用表格解析，默认为True
    :param formula_enable: 是否启用公式解析，默认为True
    :param convert_to_scanned: 是否将PDF转换为扫描PDF，默认为False
    :return: 接口响应结果
    """
    if 'file_parse' not in server_url:
        server_url = f"{server_url}/file_parse"
    try:
        file_path, is_image = detect_and_convert_to_pdf(file_path)    # 判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
        with open(file_path, 'rb') as f:
            files = {
                'files': (file_path.split('/')[-1], f, 'application/pdf'),
                'return_md': (None, str(return_md).lower()),
                'return_images': (None, str(return_images).lower()),
                'return_middle_json': (None, str(return_middle_json).lower()),
                'return_model_output': (None, str(return_model_output).lower()),
                'return_content_list': (None, str(return_content_list).lower()),
                'start_page_id': (None, str(start_page_id)),
                'end_page_id': (None, str(end_page_id)),
                'parse_method': (None, parse_method),
                'lang_list': (None, lang_list),
                'output_dir': (None, output_dir),
                'server_url': (None, 'string'),
                'backend': (None, backend),
                'table_enable': (None, str(table_enable).lower()),
                'formula_enable': (None, str(formula_enable).lower()),
                'convert_to_scanned': (None, str(convert_to_scanned).lower())
            }
            
            headers = {
                'accept': 'application/json'
            }
            
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
        
        if is_image:
            os.remove(file_path) # 删除临时生成的PDF文件
        return response.json()
        
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def convert_images_to_pdf(input_path, output_path=None):
    """
    将图片文件转换为PDF文档
    
    参数:
    - input_path: 输入路径，可以是单个图片文件路径或包含图片的文件夹路径
    - output_path: 可选的输出PDF文件路径。如果未提供，则使用输入文件/文件夹的名称
    
    支持格式:
    - 图片: JPEG, PNG, BMP, GIF, TIFF, WebP等Pillow支持的格式
    - 多页PDF: 支持多张图片合并到同一个PDF文件中
    
    返回:
    - 成功时返回PDF文件路径，失败时返回None
    """
    try:
        # 确定输出路径
        if output_path is None:
            if os.path.isfile(input_path):
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_path = base_name + ".pdf"
            elif os.path.isdir(input_path):
                folder_name = os.path.basename(input_path.rstrip(os.sep))
                output_path = folder_name + ".pdf"
        
        # 处理单个文件的情况
        if os.path.isfile(input_path):
            with Image.open(input_path) as img:
                if img.mode == 'RGBA':
                    img = img.convert('RGB')
                img.save(output_path, "PDF", resolution=100.0)
            return output_path
        
        # 处理文件夹的情况
        elif os.path.isdir(input_path):
            images = []
            valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
            
            # 收集所有支持的图片文件
            for filename in sorted(os.listdir(input_path)):
                if any(filename.lower().endswith(ext) for ext in valid_extensions):
                    filepath = os.path.join(input_path, filename)
                    try:
                        with Image.open(filepath) as img:
                            if img.mode == 'RGBA':
                                img = img.convert('RGB')
                            images.append(img.copy())
                    except Exception as e:
                        print(f"警告: 无法处理图片 {filename}: {e}")
            
            # 如果没有找到图片，返回None
            if not images:
                print("错误: 没有找到支持的图片文件")
                return None
            
            # 保存为多页PDF
            images[0].save(
                output_path,
                "PDF",
                resolution=100.0,
                save_all=True,
                append_images=images[1:]
            )
            return output_path
        
        else:
            print(f"错误: 路径 '{input_path}' 不存在或无法访问")
            return None
    
    except Exception as e:
        print(f"图片转PDF失败: {e}")
        return None


def detect_and_convert_to_pdf(input_path):
    """
    判断用户传入的是图片还是PDF，如果是图片则转换为PDF，否则直接返回PDF路径。
    支持单张图片、图片文件夹、PDF文件。
    返回：元组，第一个元素是PDF文件路径或None，第二个元素是是否是图片，是图片则返回True，否则返回False
    """
    # 支持的图片扩展名
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tif', '.tiff', '.webp']
    if os.path.isfile(input_path):
        ext = os.path.splitext(input_path)[1].lower()
        if ext == '.pdf':
            return input_path, False  # 已经是PDF
        elif ext in image_extensions:
            # 单张图片转PDF
            return convert_images_to_pdf(input_path), True
        else:
            print(f"不支持的文件类型: {input_path}")
            return None, False
    elif os.path.isdir(input_path):
        # 文件夹，假设里面是图片
        return convert_images_to_pdf(input_path), True
    else:
        print(f"路径不存在: {input_path}")
        return None, False


def get_seal_info(file_path, return_seal_img=False, server_url=BACKEND_URL, img_w=1, img_h=1, full_page=False):
    """
    获取印章信息，调用后端/seal_parse接口
    :param file_path: 输入PDF或图片文件路径
    :param return_seal_img: 是否返回公章图片的base64编码，默认为False
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'seal_parse' not in server_url:
        server_url = f"{server_url}/seal_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'return_seal_img': (None, str(return_seal_img).lower()),
                'img_w': (None, str(img_w)),
                'img_h': (None, str(img_h)),
                'full_page': (None, str(full_page).lower())
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def get_ocr_info(file_path, limit_score=None, server_url=BACKEND_URL):
    """
    获取OCR文本识别信息，调用后端/ocr_parse接口
    :param file_path: 输入PDF或图片文件路径
    :param limit_score: 分数阈值，用来过滤低于阈值分数的识别结果，默认为None
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'ocr_parse' not in server_url:
        server_url = f"{server_url}/ocr_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            
            # 如果提供了limit_score参数，则添加到files中
            if limit_score is not None:
                files['limit_score'] = (None, str(limit_score))
            
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None
    

def get_ocr_table_info(file_path, server_url=BACKEND_URL):
    """
    表格识别API，支持对上传的图片文件或PDF进行表格结构识别。
    :param file_path: 输入PDF或图片文件路径
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'ocr_table_parse' not in server_url:
        server_url = f"{server_url}/ocr_table_parse"
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream')
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None


def get_page_id_by_content(file_path, footer_threshold=0.85, server_url=BACKEND_URL):
    """
    根据内容获取页码，调用后端/structure_ocr_pages接口进行页面结构OCR识别
    :param file_path: 输入PDF或图片文件路径
    :param footer_threshold: 页脚阈值，用于识别页码位置，默认为0.85
    :param server_url: 后端服务URL，默认为BACKEND_URL
    :return: API响应内容（dict），失败时返回None
    """
    if 'structure_ocr_pages' not in server_url:
        server_url = f"{server_url}/structure_ocr_pages"
    
    try:
        with open(file_path, 'rb') as f:
            files = {
                'file': (os.path.basename(file_path), f, 'application/octet-stream'),
                'footer_threshold': (None, str(footer_threshold))
            }
            headers = {
                'accept': 'application/json'
            }
            response = requests.post(server_url, files=files, headers=headers)
            response.raise_for_status()
            return response.json()
    except IOError as e:
        print(f"文件读取错误: {e}")
        return None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {e}")
        return None




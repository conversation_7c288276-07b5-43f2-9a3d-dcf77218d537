from openai import OpenAI
# 导入图片处理函数
from util_img_process import process_multiple_images

class ChatBot:
    def __init__(self, model="InternVL3-38B", system_prompt=""):
        self.client = OpenAI(
            api_key="sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553",
            base_url="http://**************:23000/v1"
            )
        self.model = model
        self.history = []
        if system_prompt != "":
            self.history.append({"role": "system", "content": system_prompt})

    def chat(self, messages, temperature=0.8, max_tokens=8192, top_p=0.8) -> str:
        """
        与大模型对话
        
        Args:
            messages: list[dict]
            temperature: float
            max_tokens: int
            top_p: float

        Returns:
            str: 大模型的回答
        """
        if isinstance(messages, str):
            messages = [{"role": "user", "content": messages}]
        elif isinstance(messages, list):
            if not isinstance(messages[0], dict):
                messages = [{"role": "user", "content": messages}]
        else:
            raise ValueError("messages must be a string or a list of dict")
        response = self.client.chat.completions.create(
            model=self.model,
            messages=self.history + messages,
            temperature=temperature,
            max_tokens=max_tokens,
            top_p=top_p)
        return response.choices[0].message.content
    
    def chat_with_img(self, messages, img_url, temperature=0.8, max_tokens=8192, top_p=0.8) -> str:
        """
        与大模型进行图文对话
        
        Args:
            messages: str 或 list[dict] - 用户消息
            img_url: str 或 list - 图片输入，支持URL、本地路径、base64格式，支持多图输入
            temperature: float - 温度参数
            max_tokens: int - 最大输出token数
            top_p: float - top_p参数

        Returns:
            str: 大模型的回答
        """
        
        # 参数验证
        if not messages:
            raise ValueError("messages不能为空")
        
        if img_url is None or (isinstance(img_url, list) and len(img_url) == 0):
            raise ValueError("图片输入不能为空")
        
        if isinstance(img_url, str) and not img_url.strip():
            raise ValueError("图片输入不能为空")
        
        # 处理messages参数
        if isinstance(messages, str):
            prompt = messages
        elif isinstance(messages, list):
            if len(messages) == 1 and isinstance(messages[0], dict):
                prompt = messages[0].get("content", "")
            else:
                # 如果是多条消息，取最后一条作为prompt
                prompt = messages[-1].get("content", "") if isinstance(messages[-1], dict) else str(messages[-1])
        else:
            prompt = str(messages)
        
        # 处理图片
        img_data = process_multiple_images(img_url, max_pixels=36000000, fixed_height=1024)
        if len(img_data) == 0:
            raise ValueError("图片处理失败，可能尺寸过小或无法访问")
        
        
        # 构建消息内容
        user_content = [
            {
                "type": "text",
                "text": prompt
            },
        ]
        
        # 添加所有处理后的图片
        for img_base64 in img_data:
            user_content.append({
                "type": "image_url",
                "image_url": {
                    "url": img_base64
                }
            })
        
        # 构建完整消息
        messages_with_img = [
            {
                'role': 'user',
                'content': user_content
            }
        ]
        
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=self.history + messages_with_img,
                temperature=temperature,
                max_tokens=max_tokens,
                top_p=top_p
            )
            return response.choices[0].message.content
            
        except Exception as e:
            raise ValueError(f"与AI服务通信时发生错误: {str(e)}")
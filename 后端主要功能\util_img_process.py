from openai import OpenAI
import traceback
from PIL import Image
from io import BytesIO
import base64
import requests
import traceback
import json
import re
import uuid
import os
import tqdm
import pandas as pd
from io import StringIO
import os
import fitz  # PyMuPDF
from PIL import Image, ImageOps
import numpy as np

def pdf_to_images(pdf_path, dpi=200):
    """将PDF每页转为JPEG图像"""
    doc = fitz.open(pdf_path)
    images = []
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap(matrix=fitz.Matrix(dpi/72, dpi/72))
        img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
        images.append(img)
    return images

def crop_white_margin(image, border=10):
    """裁切图像空白边缘，保留小边界"""
    # 转为灰度图处理
    gray = image.convert("L")
    gray = np.array(gray)
    
    # 查找非白色区域边界
    mask = gray < 255
    coords = np.argwhere(mask)
    if coords.size == 0:
        return image  # 纯白页直接返回
    
    y0, x0 = coords.min(axis=0)
    y1, x1 = coords.max(axis=0) + 1
    
    # 添加边界填充
    w, h = image.size
    x0 = max(0, x0 - border)
    y0 = max(0, y0 - border)
    x1 = min(w, x1 + border)
    y1 = min(h, y1 + border)
    
    return image.crop((x0, y0, x1, y1))

def concat_images_vertically(images):
    """垂直拼接图像，保持相同宽度"""
    # 计算最大宽度和总高度
    widths, heights = zip(*(i.size for i in images))
    max_width = max(widths)
    total_height = sum(heights)
    
    # 创建画布
    result = Image.new("RGB", (max_width, total_height), "white")
    
    # 粘贴每张图片（居中对齐）
    y_offset = 0
    for img in images:
        # 居中处理：计算水平偏移量
        x_offset = (max_width - img.width) // 2
        result.paste(img, (x_offset, y_offset))
        y_offset += img.height
    
    return result

def process_pdf(pdf_path, output_path, dpi=200, border=10):
    """处理全流程：PDF转图->裁切->拼接->保存"""
    if pdf_path.endswith('.pdf') or pdf_path.endswith('.PDF'):
        # 核心处理流程
        images = pdf_to_images(pdf_path, dpi)
        cropped = [crop_white_margin(img, border) for img in images]
        result_image = concat_images_vertically(cropped)
        
        # 保存结果
        result_image.save(output_path, "JPEG", quality=95)
        print(f"处理完成：结果已保存至 {output_path}")
    elif pdf_path.endswith('.jpg') or pdf_path.endswith('.JPG'):
        # 转为jpg
        img = Image.open(pdf_path)
        img.save(output_path, "JPEG", quality=95)
    elif pdf_path.endswith('.png') or pdf_path.endswith('.PNG'):
        # 转为jpg
        img = Image.open(pdf_path)
        img.save(output_path, "JPEG", quality=95)
    else:
        raise ValueError("不支持的文件类型")

def img_to_base64(img):
    """
    将图片转换为base64

    Args:
        img: 图片对象

    Returns:
        str: base64编码的图片
    """
    # 如果图片是RGBA模式，转换为RGB模式
    if img.mode == 'RGBA':
        img = img.convert('RGB')
    
    # 将图片转换为base64
    buffered = BytesIO()
    img.save(buffered, format="JPEG", quality=85)
    img_str = base64.b64encode(buffered.getvalue()).decode()
    return f"data:image/jpeg;base64,{img_str}"

def process_image(url, max_pixels=36000000, fixed_height=1080, max_height_ratio=2) -> list:
    """
    检查图片尺寸并处理：
    1. 固定高度缩放宽度
    2. 对超高图片进行切片处理
    
    Args:
        url: 图片URL或本地路径
        max_pixels: 最大像素数
        fixed_height: 固定的高度值
        max_height_ratio: 判断超高图片的高宽比阈值
    
    Returns:
        处理后的图片base64或路径列表（切片情况下）
    """
    try:
        # 判断url是网络图片还是本地图片
        if url.startswith('http'):
            response = requests.get(url)
            img = Image.open(BytesIO(response.content))
            is_local = False
        else:
            img = Image.open(url)
            is_local = True
        
        # 如果图片是RGBA模式，转换为RGB模式
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        width, height = img.size
        
        # 检查图片尺寸是否小于 50x50
        if width < 50 or height < 50:
            return []
        
        # 判断是否为超高图片
        if height / width > max_height_ratio and height > fixed_height:
            print(f"图片超高，进行切片处理，高宽比：{height / width}")
            # 切片处理
            return process_tall_image(img, fixed_height, is_local)
        
        # 正常图片处理：固定高度，等比缩放宽度
        if height > fixed_height or width * height > max_pixels:
            # 计算新宽度，保持比例
            new_width = int(width * (fixed_height / height))
            img = img.resize((new_width, fixed_height), Image.Resampling.LANCZOS)
        
        return [img_to_base64(img)]
            
    except Exception as e:
        print(f"处理图片时出错: {e}")
        traceback.print_exc()
        return []

def process_tall_image(img, slice_height, is_local) -> list:
    """
    处理超高图片，将其切片处理
    
    Args:
        img: 图片对象
        slice_height: 每个切片的高度
        is_local: 是否为本地图片
    
    Returns:
        列表: 切片图片的base64或路径列表
    """
    width, height = img.size
    
    # 计算需要的切片数
    num_slices = (height + slice_height - 1) // slice_height
    
    slices = []
    for i in range(num_slices):
        # 计算当前切片的上下边界
        top = i * slice_height
        bottom = min((i + 1) * slice_height, height)
        
        # 切出当前切片
        slice_img = img.crop((0, top, width, bottom))
        
        # 如果切片是RGBA模式，转换为RGB模式
        if slice_img.mode == 'RGBA':
            slice_img = slice_img.convert('RGB')
            
        slices.append(img_to_base64(slice_img))
    print(f"切片处理完成，切片数：{len(slices)}")
    
    return slices

def process_base64_image(base64_str, max_pixels=36000000, fixed_height=1080, max_height_ratio=2) -> list:
    """
    处理base64格式的图片
    
    Args:
        base64_str: base64格式的图片字符串
        max_pixels: 最大像素数
        fixed_height: 固定的高度值
        max_height_ratio: 判断超高图片的高宽比阈值
    
    Returns:
        处理后的图片base64列表
    """
    try:
        # 处理base64字符串，移除可能的前缀
        if base64_str.startswith('data:image'):
            # 提取实际的base64数据部分
            base64_data = base64_str.split(',', 1)[1]
        else:
            base64_data = base64_str
        
        # 解码base64数据
        image_data = base64.b64decode(base64_data)
        img = Image.open(BytesIO(image_data))
        
        # 如果图片是RGBA模式，转换为RGB模式
        if img.mode == 'RGBA':
            img = img.convert('RGB')
        
        width, height = img.size
        
        # 检查图片尺寸是否小于 50x50
        if width < 50 or height < 50:
            return []
        
        # 判断是否为超高图片
        if height / width > max_height_ratio and height > fixed_height:
            print(f"base64图片超高，进行切片处理，高宽比：{height / width}")
            # 切片处理
            return process_tall_image(img, fixed_height, True)
        
        # 正常图片处理：固定高度，等比缩放宽度
        if height > fixed_height or width * height > max_pixels:
            # 计算新宽度，保持比例
            new_width = int(width * (fixed_height / height))
            img = img.resize((new_width, fixed_height), Image.Resampling.LANCZOS)
        
        return [img_to_base64(img)]
            
    except Exception as e:
        print(f"处理base64图片时出错: {e}")
        traceback.print_exc()
        return []

def process_multiple_images(img_inputs, max_pixels=36000000, fixed_height=1080, max_height_ratio=2) -> list:
    """
    处理多张图片输入，支持URL、本地路径和base64格式
    
    Args:
        img_inputs: 图片输入，可以是单个图片或图片列表，支持URL/本地路径/base64格式
        max_pixels: 最大像素数
        fixed_height: 固定的高度值
        max_height_ratio: 判断超高图片的高宽比阈值
    
    Returns:
        处理后的图片base64列表
    """
    # 如果输入是单个图片，转换为列表
    if isinstance(img_inputs, str):
        img_inputs = [img_inputs]
    
    all_processed_images = []
    
    for img_input in img_inputs:
        if not isinstance(img_input, str) or not img_input.strip():
            print("跳过无效的图片输入")
            continue
            
        # 判断是否为base64格式
        if img_input.startswith('data:image') or (len(img_input) > 100 and not img_input.startswith('http') and '/' not in img_input and '\\' not in img_input):
            # base64格式
            processed = process_base64_image(img_input, max_pixels, fixed_height, max_height_ratio)
        else:
            # URL或本地路径格式
            processed = process_image(img_input, max_pixels, fixed_height, max_height_ratio)
        
        all_processed_images.extend(processed)
    
    return all_processed_images

def chat_bot_img(prompt, img_url=None, system_prompt="", model='qwen2.5vl:3b', max_tokens=8192, top_p=0.8, temperature=0.8, max_pixels=36000000, fixed_height=1024):
    """
    使用自定义提示词进行图片转录/分析，支持多图输入和base64格式，支持超高图片的切片处理
    
    Args:
        prompt (str): 用户提示词，用于指导AI如何处理图片
        img_url (str or list): 图片输入，可以是：
            - 单个图片URL地址
            - 单个本地图片路径
            - 单个base64格式图片字符串
            - 上述格式的图片列表（支持混合格式）
        system_prompt (str): 系统提示词
        model (str, optional): 使用的视觉模型. 默认为 'qwen2.5vl:3b'
        max_tokens (int, optional): 最大输出token数. 默认为 8192
        max_pixels (int, optional): 图片最大像素数. 默认为 36000000
        top_p (float, optional): 温度参数. 默认为 0.8
        temperature (float, optional): 温度参数. 默认为 0.8
        fixed_height (int, optional): 固定高度值. 默认为 1024
    
    Returns:
        str: 处理结果。如果发生错误，返回错误信息
    """
    # 参数验证
    if not isinstance(prompt, str) or not prompt.strip():
        raise ValueError("错误：提示词不能为空")
    
    if img_url is None or (isinstance(img_url, list) and len(img_url) == 0):
        raise ValueError("错误：图片输入不能为空")
    
    if isinstance(img_url, str) and not img_url.strip():
        raise ValueError("错误：图片输入不能为空")
        
    client = OpenAI(
        base_url='http://**************:23000/v1',
        api_key='sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553'
    )
    
    # 处理多张图片
    img_data = process_multiple_images(img_url, max_pixels, fixed_height)
    if len(img_data) == 0:
        raise ValueError("图片处理失败，可能尺寸过小或无法访问")
    temp_his = []
    if system_prompt != "":
        temp_his.append({
            'role': 'system',
            'content': system_prompt
        })
    
    user_content = [
        {
            "type": "text",
            "text": prompt
        },
    ]
    
    # 添加所有处理后的图片
    for img_base64 in img_data:
        user_content.append({
            "type": "image_url",
            "image_url": {
                "url": img_base64
            }
        })
    
    temp_his.append({
        'role': 'user',
        'content': user_content
    })
    
    try:
        chat_completion = client.chat.completions.create(
            messages=temp_his,
            model=model,
            top_p=top_p,
            temperature=temperature,
            max_tokens=max_tokens
        )
        return chat_completion.choices[0].message.content    
            
    except Exception as api_error:
        traceback.print_exc()
        raise ValueError(f"与AI服务通信时发生错误: {str(api_error)}")

# 保持向后兼容，添加一个别名函数
def chat_bot_img_single(prompt, img_url=None, system_prompt="", model='qwen2.5vl:3b', max_tokens=8192, top_p=0.8, temperature=0.8, max_pixels=36000000, fixed_height=1024):
    """
    向后兼容的单图片处理函数
    """
    return chat_bot_img(prompt, img_url, system_prompt, model, max_tokens, top_p, temperature, max_pixels, fixed_height)

def image_to_pdf(image_path, output_pdf_path):
    """
    将图片文件转换为PDF
    
    Args:
        image_path: 输入图片文件路径
        output_pdf_path: 输出PDF文件路径
    
    Returns:
        str: 生成的PDF文件路径
    """
    try:
        # 支持的图片格式
        supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff']
        
        # 检查文件格式
        _, ext = os.path.splitext(image_path.lower())
        if ext not in supported_formats:
            raise ValueError(f"不支持的图片格式: {ext}")
        
        # 打开图片
        image = Image.open(image_path)
        
        # 如果图片是RGBA模式，转换为RGB模式
        if image.mode == 'RGBA':
            # 创建白色背景
            rgb_image = Image.new('RGB', image.size, (255, 255, 255))
            rgb_image.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
            image = rgb_image
        elif image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 保存为PDF
        image.save(output_pdf_path, "PDF", resolution=100.0)
        print(f"图片转PDF完成：{image_path} -> {output_pdf_path}")
        
        return output_pdf_path
        
    except Exception as e:
        print(f"图片转PDF失败: {e}")
        traceback.print_exc()
        raise

def images_to_pdf(image_paths, output_pdf_path):
    """
    将多张图片合并为一个PDF文件
    
    Args:
        image_paths: 图片文件路径列表
        output_pdf_path: 输出PDF文件路径
    
    Returns:
        str: 生成的PDF文件路径
    """
    try:
        images = []
        
        for image_path in image_paths:
            # 打开图片
            image = Image.open(image_path)
            
            # 如果图片是RGBA模式，转换为RGB模式
            if image.mode == 'RGBA':
                # 创建白色背景
                rgb_image = Image.new('RGB', image.size, (255, 255, 255))
                rgb_image.paste(image, mask=image.split()[-1])  # 使用alpha通道作为mask
                image = rgb_image
            elif image.mode != 'RGB':
                image = image.convert('RGB')
            
            images.append(image)
        
        # 保存为PDF，第一张图片作为主页，其余作为附加页
        if images:
            images[0].save(output_pdf_path, "PDF", resolution=100.0, save_all=True, append_images=images[1:])
            print(f"多张图片转PDF完成：{len(images)}张图片 -> {output_pdf_path}")
        
        return output_pdf_path
        
    except Exception as e:
        print(f"多张图片转PDF失败: {e}")
        traceback.print_exc()
        raise

def base64_to_img(base64_str):
    """
    将base64格式的图片字符串转换为PIL Image对象，支持jupyter显示
    
    Args:
        base64_str (str): base64格式的图片字符串，可以包含或不包含data:image前缀
    
    Returns:
        PIL.Image: PIL Image对象，可在jupyter中直接显示
    """
    try:
        # 处理base64字符串，移除可能的前缀
        if base64_str.startswith('data:image'):
            # 提取实际的base64数据部分
            base64_data = base64_str.split(',', 1)[1]
        else:
            base64_data = base64_str
        
        # 解码base64数据
        image_data = base64.b64decode(base64_data)
        
        # 创建PIL Image对象
        img = Image.open(BytesIO(image_data))
        
        # 如果是RGBA模式，转换为RGB（可选，根据需要）
        if img.mode == 'RGBA':
            # 创建白色背景
            rgb_img = Image.new('RGB', img.size, (255, 255, 255))
            rgb_img.paste(img, mask=img.split()[-1])
            img = rgb_img
        
        return img
        
    except Exception as e:
        print(f"base64转图片失败: {e}")
        traceback.print_exc()
        raise

def enhance_image_quality(image, scale_factor=2.0, sharpen_factor=1.5):
    """
    提升图片清晰度
    
    Args:
        image: PIL Image对象
        scale_factor: 放大倍数，默认2.0
        sharpen_factor: 锐化强度，默认1.5
    
    Returns:
        PIL Image: 处理后的图片
    """
    try:
        from PIL import ImageFilter, ImageEnhance
        
        # 确保图片是RGB模式
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 使用LANCZOS算法进行高质量放大
        new_size = (int(image.width * scale_factor), int(image.height * scale_factor))
        enhanced_image = image.resize(new_size, Image.Resampling.LANCZOS)
        
        # 应用锐化滤镜
        sharpness_enhancer = ImageEnhance.Sharpness(enhanced_image)
        enhanced_image = sharpness_enhancer.enhance(sharpen_factor)
        
        # 轻微的对比度增强
        contrast_enhancer = ImageEnhance.Contrast(enhanced_image)
        enhanced_image = contrast_enhancer.enhance(1.1)
        
        print(f"图片清晰度提升完成，原尺寸：{image.size}，新尺寸：{enhanced_image.size}")
        return enhanced_image
        
    except Exception as e:
        print(f"图片清晰度提升失败: {e}")
        traceback.print_exc()
        return image

def enhance_pdf_file(pdf_path, output_path=None, scale_factor=2.0, sharpen_factor=1.5):
    """
    提升PDF文件中图片的清晰度
    
    Args:
        pdf_path: 输入PDF文件路径
        output_path: 输出PDF文件路径，如果为None则覆盖原文件
        scale_factor: 放大倍数，默认2.0
        sharpen_factor: 锐化强度，默认1.5
    
    Returns:
        str: 处理后的PDF文件路径
    """
    try:
        import tempfile
        import uuid
        
        if output_path is None:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            temp_filename = f"enhanced_{uuid.uuid4().hex}.pdf"
            output_path = os.path.join(temp_dir, temp_filename)
        
        # 将PDF转为图片
        images = pdf_to_images(pdf_path, dpi=300)  # 使用高DPI获得更好质量
        
        # 增强每一页的图片
        enhanced_images = []
        for i, img in enumerate(images):
            print(f"正在处理第 {i+1}/{len(images)} 页...")
            enhanced_img = enhance_image_quality(img, scale_factor, sharpen_factor)
            enhanced_images.append(enhanced_img)
        
        # 将增强后的图片保存为PDF
        if enhanced_images:
            enhanced_images[0].save(
                output_path,
                "PDF",
                resolution=300.0,
                save_all=True,
                append_images=enhanced_images[1:] if len(enhanced_images) > 1 else []
            )
            print(f"PDF清晰度提升完成：{pdf_path} -> {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"PDF清晰度提升失败: {e}")
        traceback.print_exc()
        return pdf_path

def enhance_image_file(image_path, output_path=None, scale_factor=2.0, sharpen_factor=1.5):
    """
    提升图片文件的清晰度
    
    Args:
        image_path: 输入图片文件路径
        output_path: 输出图片文件路径，如果为None则覆盖原文件
        scale_factor: 放大倍数，默认2.0
        sharpen_factor: 锐化强度，默认1.5
    
    Returns:
        str: 处理后的图片文件路径
    """
    try:
        import tempfile
        import uuid
        
        if output_path is None:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            file_ext = os.path.splitext(image_path)[1]
            temp_filename = f"enhanced_{uuid.uuid4().hex}{file_ext}"
            output_path = os.path.join(temp_dir, temp_filename)
        
        # 打开图片
        image = Image.open(image_path)
        
        # 增强图片清晰度
        enhanced_image = enhance_image_quality(image, scale_factor, sharpen_factor)
        
        # 保存增强后的图片
        enhanced_image.save(output_path, quality=95)
        print(f"图片清晰度提升完成：{image_path} -> {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"图片清晰度提升失败: {e}")
        traceback.print_exc()
        return image_path

def is_scanned_pdf(pdf_path, text_ratio_threshold=0.1):
    """
    检测PDF是否为扫描式PDF
    
    Args:
        pdf_path: PDF文件路径
        text_ratio_threshold: 文本比例阈值，低于此值认为是扫描式PDF
    
    Returns:
        bool: True表示是扫描式PDF，False表示不是
    """
    try:
        doc = fitz.open(pdf_path)
        total_pages = len(doc)
        text_pages = 0
        
        # 检查前几页来判断
        check_pages = min(3, total_pages)
        
        for page_num in range(check_pages):
            page = doc.load_page(page_num)
            text = page.get_text().strip()
            
            # 如果页面有足够的文本内容，认为不是扫描式
            if len(text) > 100:  # 文本长度阈值
                text_pages += 1
        
        doc.close()
        
        # 如果大部分页面都没有文本，认为是扫描式PDF
        text_ratio = text_pages / check_pages
        is_scanned = text_ratio < text_ratio_threshold
        
        print(f"PDF文本检测：{check_pages}页中有{text_pages}页包含文本，文本比例：{text_ratio:.2f}，{'扫描式' if is_scanned else '文本型'}PDF")
        return is_scanned
        
    except Exception as e:
        print(f"检测PDF类型失败: {e}")
        # 如果检测失败，保守地认为是扫描式PDF
        return True

def enhance_file_quality(file_path, scale_factor=2.0, sharpen_factor=1.5):
    """
    提升文件清晰度的统一接口，支持图片和PDF文件
    
    Args:
        file_path: 输入文件路径
        scale_factor: 放大倍数，默认2.0
        sharpen_factor: 锐化强度，默认1.5
    
    Returns:
        str: 处理后的文件路径
    """
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return file_path
    
    file_ext = os.path.splitext(file_path)[1].lower()
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp']
    
    if file_ext in image_extensions:
        return enhance_image_file(file_path, scale_factor=scale_factor, sharpen_factor=sharpen_factor)
    elif file_ext == '.pdf':
        return enhance_pdf_file(file_path, scale_factor=scale_factor, sharpen_factor=sharpen_factor)
    else:
        print(f"不支持的文件格式: {file_ext}")
        return file_path
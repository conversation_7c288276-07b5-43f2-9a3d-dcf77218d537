import cv2
import numpy as np
from PIL import Image
import os
from skimage import morphology, measure
from skimage.restoration import inpaint
import fitz  # PyMuPDF
from util_img_process import pdf_to_images


def detect_red_stamp(image):
    """
    检测图像中的红色印章区域
    
    Args:
        image: PIL Image对象或numpy数组
        
    Returns:
        mask: 印章区域的二值掩码
    """
    # 转换为numpy数组
    if isinstance(image, Image.Image):
        img_array = np.array(image)
    else:
        img_array = image.copy()
    
    # 转换为HSV颜色空间
    hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)
    
    # 定义红色的HSV范围（两个范围，因为红色在HSV中跨越0度）
    # 低红色范围 (0-10)
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    
    # 高红色范围 (160-180)
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    # 创建红色掩码
    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # 形态学操作优化掩码
    kernel = np.ones((3, 3), np.uint8)
    
    # 开运算去除噪声
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # 闭运算填充空洞
    red_mask = cv2.morphologyEx(red_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
    
    return red_mask


def detect_text_regions(image, min_area=100):
    """
    检测图像中的文字区域，用于保护文字不被误删
    
    Args:
        image: PIL Image对象或numpy数组
        min_area: 最小文字区域面积
        
    Returns:
        text_mask: 文字区域的二值掩码
    """
    # 转换为numpy数组
    if isinstance(image, Image.Image):
        img_array = np.array(image)
    else:
        img_array = image.copy()
    
    # 转换为灰度图
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array.copy()
    
    # 二值化
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
    
    # 形态学操作连接文字
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel, iterations=1)
    
    # 查找连通组件
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # 创建文字掩码
    text_mask = np.zeros_like(gray)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > min_area:
            # 计算边界框的宽高比，文字通常比较细长
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = max(w, h) / min(w, h)
            
            # 过滤掉太方的区域（可能是印章）
            if aspect_ratio > 1.5 or area < 1000:  # 文字区域通常比较细长
                cv2.fillPoly(text_mask, [contour], 255)
    
    return text_mask


def refine_stamp_mask(stamp_mask, text_mask):
    """
    优化印章掩码，避免删除文字区域
    
    Args:
        stamp_mask: 印章区域掩码
        text_mask: 文字区域掩码
        
    Returns:
        refined_mask: 优化后的印章掩码
    """
    # 从印章掩码中去除文字区域
    refined_mask = cv2.bitwise_and(stamp_mask, cv2.bitwise_not(text_mask))
    
    # 进一步形态学优化
    kernel = np.ones((3, 3), np.uint8)
    refined_mask = cv2.morphologyEx(refined_mask, cv2.MORPH_OPEN, kernel, iterations=1)
    
    # 膨胀操作，确保印章边缘也被包含
    refined_mask = cv2.dilate(refined_mask, kernel, iterations=1)
    
    return refined_mask


def detect_stamp_by_shape(image, min_area=500, max_area=50000):
    """
    基于形状特征检测印章（圆形、椭圆形等）
    
    Args:
        image: PIL Image对象或numpy数组
        min_area: 最小印章面积
        max_area: 最大印章面积
        
    Returns:
        shape_mask: 形状检测的印章掩码
    """
    # 转换为numpy数组
    if isinstance(image, Image.Image):
        img_array = np.array(image)
    else:
        img_array = image.copy()
    
    # 转换为灰度图
    if len(img_array.shape) == 3:
        gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)
    else:
        gray = img_array.copy()
    
    # 边缘检测
    edges = cv2.Canny(gray, 50, 150, apertureSize=3)
    
    # 查找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    shape_mask = np.zeros_like(gray)
    
    for contour in contours:
        area = cv2.contourArea(contour)
        
        if min_area <= area <= max_area:
            # 计算轮廓的圆形度
            perimeter = cv2.arcLength(contour, True)
            if perimeter > 0:
                circularity = 4 * np.pi * area / (perimeter * perimeter)
                
                # 圆形度大于0.3的认为可能是印章
                if circularity > 0.3:
                    cv2.fillPoly(shape_mask, [contour], 255)
    
    return shape_mask


def inpaint_stamp_region(image, mask):
    """
    修复印章移除后的区域
    
    Args:
        image: 原始图像
        mask: 需要修复的区域掩码
        
    Returns:
        result: 修复后的图像
    """
    # 转换为numpy数组
    if isinstance(image, Image.Image):
        img_array = np.array(image)
    else:
        img_array = image.copy()
    
    # 使用OpenCV的inpaint函数进行修复
    # INPAINT_TELEA 方法更适合文档图像
    result = cv2.inpaint(img_array, mask, inpaintRadius=3, flags=cv2.INPAINT_TELEA)
    
    return result


def remove_stamp_from_image(image, method='combined', preserve_text=True):
    """
    从图像中移除印章
    
    Args:
        image: PIL Image对象或图像路径
        method: 检测方法 ('color', 'shape', 'combined')
        preserve_text: 是否保护文字区域
        
    Returns:
        result_image: PIL Image对象，处理后的图像
        mask_image: PIL Image对象，检测到的印章掩码（用于调试）
    """
    # 读取图像
    if isinstance(image, str):
        pil_image = Image.open(image)
    else:
        pil_image = image
    
    # 转换为RGB模式
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    img_array = np.array(pil_image)
    
    print("开始检测印章区域...")
    
    # 检测印章区域
    if method == 'color':
        stamp_mask = detect_red_stamp(img_array)
    elif method == 'shape':
        stamp_mask = detect_stamp_by_shape(img_array)
    elif method == 'combined':
        red_mask = detect_red_stamp(img_array)
        shape_mask = detect_stamp_by_shape(img_array)
        stamp_mask = cv2.bitwise_or(red_mask, shape_mask)
    else:
        raise ValueError("不支持的检测方法，请选择 'color', 'shape' 或 'combined'")
    
    print(f"检测到印章区域像素数: {np.sum(stamp_mask > 0)}")
    
    # 如果需要保护文字
    if preserve_text:
        print("检测文字区域，保护文字...")
        text_mask = detect_text_regions(img_array)
        stamp_mask = refine_stamp_mask(stamp_mask, text_mask)
        print(f"优化后印章区域像素数: {np.sum(stamp_mask > 0)}")
    
    # 如果没有检测到印章，直接返回原图
    if np.sum(stamp_mask > 0) == 0:
        print("未检测到印章区域")
        return pil_image, Image.fromarray(stamp_mask)
    
    print("开始修复印章区域...")
    
    # 修复印章区域
    result_array = inpaint_stamp_region(img_array, stamp_mask)
    
    # 转换回PIL Image
    result_image = Image.fromarray(result_array)
    mask_image = Image.fromarray(stamp_mask)
    
    print("印章移除完成")
    
    return result_image, mask_image


def remove_stamp_from_pdf(pdf_path, output_dir="./processed", method='combined', preserve_text=True):
    """
    从PDF文件中移除印章
    
    Args:
        pdf_path: PDF文件路径
        output_dir: 输出目录
        method: 检测方法 ('color', 'shape', 'combined')
        preserve_text: 是否保护文字区域
        
    Returns:
        processed_images: 处理后的图像列表
        output_paths: 输出文件路径列表
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"开始处理PDF文件: {pdf_path}")
    
    # 将PDF转换为图像
    images = pdf_to_images(pdf_path, dpi=200)
    
    processed_images = []
    output_paths = []
    
    for i, img in enumerate(images):
        print(f"处理第 {i+1}/{len(images)} 页...")
        
        # 移除印章
        result_img, mask_img = remove_stamp_from_image(img, method, preserve_text)
        
        # 保存处理后的图像
        base_name = os.path.splitext(os.path.basename(pdf_path))[0]
        output_path = os.path.join(output_dir, f"{base_name}_page_{i+1}_no_stamp.jpg")
        mask_path = os.path.join(output_dir, f"{base_name}_page_{i+1}_mask.jpg")
        
        result_img.save(output_path, "JPEG", quality=95)
        mask_img.save(mask_path, "JPEG", quality=95)
        
        processed_images.append(result_img)
        output_paths.append(output_path)
        
        print(f"第 {i+1} 页处理完成，保存至: {output_path}")
    
    print(f"PDF处理完成，共处理 {len(images)} 页")
    
    return processed_images, output_paths


def remove_stamp_batch(input_dir, output_dir="./processed", file_types=None, method='combined', preserve_text=True):
    """
    批量处理文件夹中的图像和PDF文件
    
    Args:
        input_dir: 输入目录
        output_dir: 输出目录
        file_types: 支持的文件类型列表，None表示所有支持的类型
        method: 检测方法 ('color', 'shape', 'combined')
        preserve_text: 是否保护文字区域
        
    Returns:
        processed_files: 处理完成的文件列表
    """
    if file_types is None:
        file_types = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.pdf']
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    processed_files = []
    
    # 遍历输入目录
    for filename in os.listdir(input_dir):
        file_path = os.path.join(input_dir, filename)
        
        # 检查文件类型
        _, ext = os.path.splitext(filename.lower())
        if ext not in file_types:
            continue
        
        print(f"\n处理文件: {filename}")
        
        try:
            if ext == '.pdf':
                # 处理PDF文件
                processed_images, output_paths = remove_stamp_from_pdf(
                    file_path, output_dir, method, preserve_text
                )
                processed_files.extend(output_paths)
            else:
                # 处理图像文件
                result_img, mask_img = remove_stamp_from_image(
                    file_path, method, preserve_text
                )
                
                # 保存结果
                base_name = os.path.splitext(filename)[0]
                output_path = os.path.join(output_dir, f"{base_name}_no_stamp.jpg")
                mask_path = os.path.join(output_dir, f"{base_name}_mask.jpg")
                
                result_img.save(output_path, "JPEG", quality=95)
                mask_img.save(mask_path, "JPEG", quality=95)
                
                processed_files.append(output_path)
                print(f"处理完成，保存至: {output_path}")
                
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")
            continue
    
    print(f"\n批量处理完成，共处理 {len(processed_files)} 个文件")
    return processed_files


def preview_stamp_detection(image_path, method='combined', preserve_text=True):
    """
    预览印章检测结果，用于调试和参数调整
    
    Args:
        image_path: 图像文件路径
        method: 检测方法 ('color', 'shape', 'combined')
        preserve_text: 是否保护文字区域
        
    Returns:
        tuple: (原图, 检测掩码, 处理结果)
    """
    # 读取图像
    pil_image = Image.open(image_path)
    if pil_image.mode != 'RGB':
        pil_image = pil_image.convert('RGB')
    
    img_array = np.array(pil_image)
    
    # 检测印章
    if method == 'color':
        stamp_mask = detect_red_stamp(img_array)
    elif method == 'shape':
        stamp_mask = detect_stamp_by_shape(img_array)
    elif method == 'combined':
        red_mask = detect_red_stamp(img_array)
        shape_mask = detect_stamp_by_shape(img_array)
        stamp_mask = cv2.bitwise_or(red_mask, shape_mask)
    
    # 检测文字（如果需要）
    if preserve_text:
        text_mask = detect_text_regions(img_array)
        original_stamp_mask = stamp_mask.copy()
        stamp_mask = refine_stamp_mask(stamp_mask, text_mask)
        
        # 创建调试图像，显示不同的检测结果
        debug_img = img_array.copy()
        debug_img[original_stamp_mask > 0] = [255, 0, 0]  # 原始印章检测：红色
        debug_img[text_mask > 0] = [0, 255, 0]  # 文字检测：绿色
        debug_img[stamp_mask > 0] = [0, 0, 255]  # 最终印章掩码：蓝色
        
        debug_image = Image.fromarray(debug_img)
    else:
        debug_image = None
    
    # 修复结果
    if np.sum(stamp_mask > 0) > 0:
        result_array = inpaint_stamp_region(img_array, stamp_mask)
        result_image = Image.fromarray(result_array)
    else:
        result_image = pil_image
    
    mask_image = Image.fromarray(stamp_mask)
    
    return pil_image, mask_image, result_image, debug_image


if __name__ == "__main__":
    # 示例用法
    print("印章移除工具")
    print("支持的功能：")
    print("1. 单个图像文件印章移除")
    print("2. PDF文件印章移除")
    print("3. 批量处理")
    print("4. 预览检测效果")
    
    # 示例：处理单个图像
    # result_img, mask_img = remove_stamp_from_image("test_image.jpg")
    # result_img.save("result.jpg")
    
    # 示例：处理PDF
    # processed_images, output_paths = remove_stamp_from_pdf("test.pdf")
    
    # 示例：批量处理
    # processed_files = remove_stamp_batch("input_folder/", "output_folder/") 
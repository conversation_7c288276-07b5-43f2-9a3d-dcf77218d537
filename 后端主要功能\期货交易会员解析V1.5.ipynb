{"cells": [{"cell_type": "code", "execution_count": 30, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from utils import fn_to_markdown, process_file_to_base64\n", "from mineru_pdf import get_ocr_table_info\n", "import re\n"]}, {"cell_type": "code", "execution_count": 43, "id": "cc532feb", "metadata": {}, "outputs": [], "source": ["# 本代码不采用pdf转扫描式pdf的前置步骤\n", "fn = \"/app/宁波银行POC/大模型样例/POC脱敏材料/开户回执档案（脱敏）/开户回执_兴业基金-昆仑银行1号单一.pdf_1745473909820.jpg\"\n", "markdown_content, seal_img_list = fn_to_markdown(fn, convert_to_scanned=True, ai_seal=True, add_seal_info=False, add_ocr_info=False)\n", "table_info = get_ocr_table_info(fn)['results']\n", "\n", "# 表格提取\n", "# markdown_content = re.sub(r'<html><body><table>.*?</table></body></html>', '<表格见下方>', markdown_content, flags=re.DOTALL)\n", "table_first = True\n", "for table_temp in table_info:\n", "    if table_first:\n", "        markdown_content += \"\\n\\n# 表格(当与上方表格产生冲突时，以下方表格为准)\"\n", "        table_first = False\n", "    markdown_content += f\"\\n## 表格{table_temp['table_region_id']}\\n{table_temp['neighbor_texts']}\\n{table_temp['pred_html']}\"\n"]}, {"cell_type": "code", "execution_count": 9, "id": "1978d24f", "metadata": {}, "outputs": [], "source": ["chatbot = ChatBot(\n", "    model=\"qwen3-32b\",\n", "    system_prompt=\"\"\"你是一名期货开户文件解析专家。请严格区分“会员号”（固定 4 位数字）和“交易编码”（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 产品名称：资管计划或基金产品的正式名称，在没有检索到时，使用`交易编码对应名称`作为产品名称\n", "2. 资金账号：资金账户号码\n", "3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填“/”）\n", "4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为“投机”）\n", "5. 开始时间：写明的开始日期或者落款日期，有时也被称为开户日期（YYYY-MM-DD；缺失填“/”）\n", "6. 结束时间：文件内表明的截止日期(如果有)，取不到则为\"/\"（YYYY-MM-DD；缺失填“/”）\n", "\n", "=====================\n", "【交易所名称映射】\n", "- 上期所＝上海期货交易所／上海交易所\n", "- 大商所＝大连商品交易所／大连交易所\n", "- 郑商所＝郑州商品交易所／郑州交易所\n", "- 中金所＝中国金融期货交易所／金融交易所\n", "- 上能所＝上海能源交易所／能源中心\n", "- 广期所＝广州期货交易所／广州交易所\n", "\n", "=====================\n", "【账户用途映射】\n", "- 投机＝投机交易账户\n", "- 套利＝套利交易账户\n", "- 套保＝套期保值交易账户\n", "\n", "若文档未写明用途，默认“投机”。\n", "\n", "=====================\n", "【关键区别提醒】\n", "- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n", "- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n", "- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n", "- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n", "\n", "=====================\n", "【输出 JSON 格式示例】\n", "```json\n", "{\n", "  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "  \"资金账号\": \"2120061\",\n", "  \"会员号\": {\n", "    \"上期所\": \"0121\",\n", "    \"大商所\": \"/\",\n", "    \"郑商所\": \"0059\",\n", "    \"中金所\": \"0170\",\n", "    \"上能所\": \"8059\",\n", "    \"广期所\": \"0021\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"99871700\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"00185013\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"上能所\": {\"投机\": \"81010376\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"04471686\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"2025-01-01\",\n", "  \"结束时间\": \"/\"\n", "}\n", "```\n", "    \"\"\"\n", "    )"]}, {"cell_type": "code", "execution_count": 42, "id": "77801cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "  \"资金账号\": \"2120061\",\n", "  \"会员号\": {\n", "    \"上期所\": \"0059\",\n", "    \"大商所\": \"0195\",\n", "    \"郑商所\": \"0121\",\n", "    \"中金所\": \"0170\",\n", "    \"上能所\": \"8059\",\n", "    \"广期所\": \"0021\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"06837313\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"99871700\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"00185013\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"上能所\": {\"投机\": \"81010376\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"04471686\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"2024-06-12\",\n", "  \"结束时间\": \"/\"\n", "}\n", "```\n"]}], "source": ["response = chatbot.chat(markdown_content)\n", "print(response)"]}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from mineru_pdf import *\n", "from util_ai import ChatBot\n", "from utils import pdf2markdown, rag_search_neighbor"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["fn = \"/app/宁波银行POC/大模型样例/POC脱敏材料/理财产品说明书（脱敏）/白金12M25011产品说明书.pdf\"\n", "\n", "markdown_content = pdf2markdown(fn)\n", "refer_docs, refer_doc_text = rag_search_neighbor(markdown_content, keywords=[\"销售\", \"代销\", \"代理销售\"], keywords2=[\"公司\", \"银行\"])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["['本理财计划为公募理财产品，经长安银行内部评估本理财计划的投资组合、同类产品过往业绩和风险水平等因素后，将本产品的风险等级评定为二级。通过代理销售机构（以下简称“代销机构”）渠道销售的，理财产品评级应当以代销机构最终披露的评级结果为准。代销机构如与长安银行产品风险评级结果不一致，应当采用对应较高风险等级的评级结果。',\n", " '本理财计划面向不特定社会公众公开发行，按照长安银行内部评估，本理财产品适合风险承受能力评级为稳健型、平衡型、成长型、进取型的个人投资者以及适合的机构投资者。通过代销机构购买的，适合投资者及投资者风险承受能力评估结果以代销机构规定为准。',\n", " '本埋财产品的信息披露将通过长安银行官方网站（www.ccabchina.com） 、代销机构（如有）信息披露渠道等进行公告。投资者在购买理财产品后，应随时关注本理财产品的信息披露情况，及时获取相关信息。如果投资者未及时查询而未能及时了解本理财产品信息，因此产生的责任和风险由投资者承担。',\n", " '投资者若对本说明书的内容有任何疑问，请咨询长安银行或代销机构营业网点工作人员；若对该产品和服务有任何意见和建议，可通过长安银行营业网点工作人员、长安银行客户服务热线029-96669或代销机构客户服务热线进行反映，我们将按照相关流程予以受理。',\n", " '<html><body><table><tr><td>风险评级</td><td>评级结果。 二级（本评级为长安银行内部评级，不具备法律效力，仅供参考） 本理财产品通过代理销售机构（以下简称“代销机构”）渠道销售 的，理财产品评级应当以代销机构最终披露的评级结果为准。代销机构 如与长安银行产品风险评级结果不一致，应当采用对应较高风险等级的</td></tr><tr><td>适合投资者类别</td><td>经长安银行内部风险承受能力评级为稳健型、平衡型、成长型、进 取型的个人投资者以及适合的机构投资者。 通过代销机构购买的，适合投资者及投资者风险承受能力评估结果</td></tr><tr><td></td><td>以代销机构规定为准。 人民币</td></tr><tr><td>本金及收益币种</td><td></td></tr><tr><td>发行期</td><td>上限100000元</td></tr><tr><td></td><td>2025年06月09日08:30至2025年06月12日17:00</td></tr><tr><td>产日</td><td>2025年06月13日</td></tr><tr><td></td><td>2026年06月26日</td></tr><tr><td>产品到期日 认购起点</td><td>募集期内，本理财计划1元人民币为1份，投资者认购起点为1万 元，递增单位为1000元的整数倍。投资者认购份额=认购金额/1元。 本理财计划业绩比较基准2.20%-2.90%（年化）。</td></tr><tr><td>业绩比较基准</td><td>基准。 本产品为固定收益类产品，以产品投资存款类及货币市场工具0% 100%，债券类资产40%-100%，非标准化债权类资产0-50%，杠杆率 100%-140%为例，参考中债平均收益率及中债综合财富指数收益率等情 况，考虑资本利得收益并扣除相关税费成本后，综合测算得出业绩比较 业绩比较基准不代表产品的未来表现和实际收益，不构成对产品收</td></tr><tr><td>提前终止</td><td>益的承诺。 长安银行有提前终止权，详见“提前终止” 本理财计划募集期，投资者有权撤单</td></tr><tr><td>撤单权</td><td>本理财计划成立后，投资者无产品赎回权</td></tr><tr><td>赎回权</td><td>0.02%/年</td></tr><tr><td>理财资产托管费</td><td>0.20%/年</td></tr><tr><td>销售服务费</td><td>0.30%/年。若产品的年化投资收益率低于业绩比较基准区间下限，</td></tr><tr><td>固定管理费</td><td>安银行计算决定。 长安银行不收取或按差额收取固定管理费，差额收取的固定管理费由长 本理财计划到期时，扣除理财资产托管费、销售服务费、固定管理</td></tr><tr><td>浮动管理费</td><td>余50%作为浮动管理费。 费后的年化投资收益率若低于产品业绩比较基准上限，长安银行不收取 或按差额收取浮动管理费，差额收取的浮动管理费由长安银行计算决 定：若年化投资收益率超过业绩比较基准的部分，50%归投资者所有，其</td></tr><tr><td></td><td>国家法工作</td></tr><tr><td></td><td>本理财计划运作过程中涉及的各纳税主体，其纳税义务按国家税收 法律、法规执行。理财计划运营过程中发生的应由理财计划承担的增值 税应税行为，由本产品管理人申报和缴纳增值税及附加税费，相关税款</td></tr><tr><td>税款 发行机构名称</td><td>长安银行股份有限公司 从理财计划资产中扣缴。除法律法规特别要求外，投资者应缴纳的税收 由投资者负责，产品管理人不承担代扣代缴或纳税的义务。</td></tr><tr><td>发行机构统一社会信用代码</td><td>91610000691125047W</td></tr><tr><td></td><td>B1027H261010001</td></tr><tr><td>发行机构金融机构代码</td><td>中国陕西</td></tr><tr><td>销售地域</td><td>中国陕西</td></tr><tr><td>服务地域</td><td>投资者可通过长安银行各营业网点、手机银行、微信银行、网上银</td></tr><tr><td>办理渠道</td><td>为准。 行等渠道办理。投资者通过代销机构购买的，办理渠道以代销机构规定</td></tr></table></body></html>',\n", " '2.产品募集期：自2025年06月09日08:30至2025年06月12日17:00。3.认购起点：本理财计划1元人民币为1份，投资者认购起点为1万元，递增单位为1000元的整数倍。为合理控制理财计划投资者集中度，长安银行有权设置单一投资者认购上限，审慎确认大额认购申请。产品募集期内，投资者有权进行认购撤单。4.发行规模：本理财计划规模上限为100000万元。如本理财计划募集期届满，募集资金总额未达到人民币1000万元，本理财计划不能有效设立。长安银行将在募集期限届满后2个工作日内将募集资金予以解冻。5.投资者应在长安银行开立一般结算户，该结算账户是投资者认购理财计划时，长安银行冻结认购资金的资金账户。投资者应在资金账户中预留足够的认购资金，预留资金不足的，视为认购无效。投资者向长安银行申请认购理财计划并取得长安银行确认后，长安银行从投资者资金账户中冻结相应认购资金。投资者通过代销机构认购理财的，应在代销机构开立一般结算户，扣款规则以代销机构规定为准。6.长安银行于认购日冻结投资者资金账户中认购资金，于本理财计划成立日扣划该资金。本理财计划认购期间投资本金按照原账户活期存款规则计息，该利息不计入投资本金。由代销机构销售的，认购申请日（含）至成立日（不含）或认购撤销日（不含）之间，投资者认购资金按照代销机构与投资人约定的计息方式执行。',\n", " '4.浮动管理费：本理财计划到期时，扣除理财资产托管费、销售服务费、固定官理贫后的年化投资收益率若低于产品业绩比较基准上限，长安银行不收取或按差额收取浮动管理费，差额收取的浮动管理费由长安银行计算决定；若年化投资收益率超过业绩比较基准的部分，50%归投资者所有，其余50%作为浮动管理费。',\n", " '示例一：本理财计划到期时，扣除理财资产托管费、销售服务费、固定管理费后的年化投资收益若高于产品业绩比较基准上限，则长安银行将收取浮动管理费。',\n", " '假设理财计划实际投资天数为371天，公布业绩比较基准为3.5%-4.1%，投资者认购  \\n本理财计划1万元，认购份额=1万元/1元=1万份，本理财计划到期时，扣除理财资产托  \\n管费、销售服务费、固定管理费后理财计划单位份额净值为1.0487，理财计划收益率=$=$   \\n（1.0487-1）×365/371=4.79%>4.1%，长安银行收取浮动管理费率=[（1.0487-1）×365/371-  \\n4.1%]×50%=0.34%，扣除浮动管理费后，理财计划可供投资者分配的单位净值为  \\n1.0452，投资者收益分红=10,000.00×（1.0452-1）=452.00元。（截位保留2位小数）',\n", " '示例二：本理财计划到期时，扣除理财资产托管费、销售服务费、固定管理费后的年化投资收益若低于产品业绩比较基准上限，则长安银行不收取浮动管理费。',\n", " '假设埋财计划实际投资天数为371天，公布业绩比较基准为3.5%-4.1%，投资者认购本理财计划1万元，认购份额=1万元/1元=1万份，本理财计划到期时，扣除理财资产托管费、销售服务费后理财计划单位份额净值为1.0397，理财计划收益率=（1.0397-1）×365/371=3.91%<4.1%，长安银行不收取浮动管理费，投资者收益分红=10，000.00× $0 0 \\\\times$ （1.0397-1）=397.00元（截位保留两位小数）。',\n", " '示例三：本理财计划到期时，扣除理财资产托管费、销售服务费后出现亏损，则长安银行不收取管理费。',\n", " '假设投资者认购本理财计划1万元，认购份额=1万元/1元=1万份，本理财计划到期时，扣除理财资产托管费、销售服务费后理财计划单位份额净值为0.9850，长安银行不收取管理费，投资者本金出现亏损，实际收益=10，000.00×（0.9850-1）=-150.00元（截位保留两位小数）。',\n", " '3.投资者若对本说明书的内容有任何疑问，请咨询长安银行或代销机构营业网点工作人员；若对该产品和服务有任何意见和建议，可通过长安银行营业网点工作人员、长安银行客户服务热线029-96669或代销机构客户服务热线进行反映。']"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["refer_docs"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"销售机构\": []\n", "}\n", "```\n"]}], "source": ["chatbot = ChatBot(\n", "    system_prompt=\"\"\"你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。\n", "    注意：请勿捏造数据，请根据实际情况输出。\n", "    请注意：\n", "    - 输出格式必须为json格式，不要输出其他内容。\n", "    - 如不存在销售机构，则产品的发行银行为销售机构\n", "\n", "    # 示例输出(仅供参考，请根据实际情况输出)\n", "    ```json\n", "    {\n", "      \"销售机构\": [\n", "        \"XX银行股份有限公司\",\n", "        \"XX银行股份有限公司\n", "      ]\n", "    }\n", "    ```\n", "    \"\"\"\n", "    )\n", "response = chatbot.chat(refer_doc_text)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ocr", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 2}
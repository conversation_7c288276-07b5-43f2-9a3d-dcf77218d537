{"cells": [{"cell_type": "markdown", "id": "a1b2c3d4", "metadata": {}, "source": ["# 非标交易确认单文档解析\n", "\n", "## 功能概述\n", "通过大模型对客户发送的非标交易确认单进行解析，支持多种文档格式：\n", "- PDF文件\n", "- Excel文件(.xlsx, .xls)\n", "- 图片文件(.jpg, .png等)\n", "- Word文档(.docx)\n", "\n", "## 解析内容\n", "1. **投资者名称**：通常指代客户姓名，一般是资管计划的名称\n", "2. **投资者账号**：通常指代客户的资金账号\n", "3. **业务日期**：对应某一笔交易的日期\n", "4. **业务类型**：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. **投资标的名称**：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. **投资标的代码**：投资标的的代码，多为数字和字母的组合，也可能为空\n", "7. **投资标的金额**：实际交易的确认金额\n", "8. **投资标的数量**：文档中可能用份额来描述\n", "9. **交易费用**：一般申购、赎回、买入、卖出交易中，会标明交易费用，没有则可以为空\n", "\n", "## 支持的文件格式详情\n", "- **PDF文件**: 使用OCR和VLM技术解析\n", "- **Excel文件**: \n", "  - .xlsx (Excel 2007及以上版本)\n", "  - .xls (Excel 97-2003版本)\n", "- **图片文件**: .jpg, .jpeg, .png, .bmp, .gif, .tiff, .webp\n", "- **Word文档**: .docx格式"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ tabulate 已安装\n", "✓ openpyxl 已安装\n", "✗ xlrd 未安装\n", "✗ python-docx 未安装\n", "✗ PyPDF2 未安装\n", "✓ Pillow 已安装\n", "\n", "需要安装以下包: xlrd, python-docx, PyPDF2\n", "正在自动安装...\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting xlrd\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/1a/62/c8d562e7766786ba6587d09c5a8ba9f718ed3fa8af7f4553e8f91c36f302/xlrd-2.0.2-py2.py3-none-any.whl (96 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m96.6/96.6 kB\u001b[0m \u001b[31m547.7 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: xlrd\n", "Successfully installed xlrd-2.0.2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ xlrd 安装成功\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting python-docx\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/d0/00/1e03a4989fa5795da308cd774f05b704ace555a70f9bf9d3be057b680bcf/python_docx-1.2.0-py3-none-any.whl (252 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m253.0/253.0 kB\u001b[0m \u001b[31m537.4 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hRequirement already satisfied: lxml>=3.1.0 in /root/anaconda3/lib/python3.11/site-packages (from python-docx) (4.9.3)\n", "Requirement already satisfied: typing_extensions>=4.9.0 in /root/anaconda3/lib/python3.11/site-packages (from python-docx) (4.13.2)\n", "Installing collected packages: python-docx\n", "Successfully installed python-docx-1.2.0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ python-docx 安装成功\n", "Looking in indexes: https://pypi.tuna.tsinghua.edu.cn/simple\n", "Collecting PyPDF2\n", "  Downloading https://pypi.tuna.tsinghua.edu.cn/packages/8e/5e/c86a5643653825d3c913719e788e41386bee415c2b87b4f955432f2de6b2/pypdf2-3.0.1-py3-none-any.whl (232 kB)\n", "\u001b[2K     \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m232.6/232.6 kB\u001b[0m \u001b[31m1.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0ma \u001b[36m0:00:01\u001b[0m\n", "\u001b[?25hInstalling collected packages: PyPDF2\n", "Successfully installed PyPDF2-3.0.1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\u001b[33mWARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv\u001b[0m\u001b[33m\n", "\u001b[0m"]}, {"name": "stdout", "output_type": "stream", "text": ["✓ PyPDF2 安装成功\n", "\n", "当前工作目录: /app/宁波银行POC/ipynb\n", "目录内容: ['temp', '券商账户计息变更.ipynb', '券商账户计息变更V1.1.ipynb', '券商账户计息变更V1.3.ipynb', '宁银理财费用变更样例.ipynb']...\n"]}], "source": ["import sys\n", "import os\n", "import json\n", "import pandas as pd\n", "from datetime import datetime\n", "import re\n", "import subprocess\n", "\n", "# 检查和安装必要的依赖库\n", "def check_and_install_dependencies():\n", "    \"\"\"检查并安装必要的依赖库\"\"\"\n", "    required_packages = {\n", "        'tabulate': 'tabulate',\n", "        'openpyxl': 'openpyxl',\n", "        'xlrd': 'xlrd',\n", "        'python-docx': 'docx',\n", "        'PyPDF2': 'PyPDF2',\n", "        'Pillow': 'PIL'\n", "    }\n", "    \n", "    missing_packages = []\n", "    \n", "    for package_name, import_name in required_packages.items():\n", "        try:\n", "            if import_name == 'docx':\n", "                import docx\n", "            elif import_name == 'PIL':\n", "                from PIL import Image\n", "            else:\n", "                __import__(import_name)\n", "            print(f\"✓ {package_name} 已安装\")\n", "        except ImportError:\n", "            print(f\"✗ {package_name} 未安装\")\n", "            missing_packages.append(package_name)\n", "    \n", "    if missing_packages:\n", "        print(f\"\\n需要安装以下包: {', '.join(missing_packages)}\")\n", "        print(\"正在自动安装...\")\n", "        \n", "        for package in missing_packages:\n", "            try:\n", "                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])\n", "                print(f\"✓ {package} 安装成功\")\n", "            except subprocess.CalledProcessError as e:\n", "                print(f\"✗ {package} 安装失败: {e}\")\n", "                print(f\"请手动运行: pip install {package}\")\n", "    else:\n", "        print(\"\\n所有依赖库都已安装完成!\")\n", "\n", "# 运行依赖检查\n", "check_and_install_dependencies()\n", "\n", "# 导入自定义模块\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from mineru_pdf import trans_pdf_to_markdown\n", "\n", "# 导入文档处理相关库\n", "import docx\n", "from openpyxl import load_workbook\n", "import xlrd\n", "import PyPDF2\n", "from PIL import Image\n", "\n", "print(f\"\\n当前工作目录: {os.getcwd()}\")\n", "print(f\"目录内容: {os.listdir('.')[:5]}...\")  # 只显示前5个项目\n"]}, {"cell_type": "code", "execution_count": 4, "id": "chatbot_init", "metadata": {}, "outputs": [], "source": ["# 初始化聊天机器人（已优化数字精度处理）\n", "chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一名非标交易确认单解析专家。请从用户提供的交易确认单文档中提取以下信息，并用JSON格式返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 投资者名称：通常指代客户姓名，一般是资管计划的名称\n", "2. 投资者账号：通常指代客户的资金账号\n", "3. 业务日期：对应某一笔交易的日期（YYYY-MM-DD格式；缺失填\"/\"）\n", "4. 业务类型：需要提取文件中代表当前类型的文字，并映射到以下选项中：分红、红利转投、买入、卖出、认购、申购、赎回\n", "5. 投资标的名称：每笔交易会有一个投资标的，一般是基金、资管计划等\n", "6. 投资标的代码：投资标的的代码，多为数字和字母的组合，也可能为空（缺失填\"/\"）\n", "7. 投资标的金额：实际交易的确认金额（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "8. 投资标的数量：文档中可能用份额来描述（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "9. 交易费用：一般申购、赎回、买入、卖出交易中，会标明交易费用（数字格式，保持原始精度不要四舍五入，缺失填\"/\"）\n", "\n", "=====================\n", "【重要注意事项】\n", "- 所有数字字段（投资标的金额、投资标的数量、交易费用）必须保持原始精度，不要进行四舍五入\n", "- 如果原始数据是31006.5，输出应该是\"31006.5\"，不要变成\"31007\"或\"31006\"\n", "- 如果原始数据是1386040，输出应该是\"1386040\"，不要变成\"1.38604e+06\"\n", "\n", "=====================\n", "【业务类型映射】\n", "- 分红：分红派息、现金分红、股息分红等\n", "- 红利转投：红利再投资、分红转投等\n", "- 买入：买入、购买等\n", "- 卖出：卖出、赎回卖出等\n", "- 认购：认购、新基金认购等\n", "- 申购：申购、基金申购等\n", "- 赎回：赎回、基金赎回等\n", "\n", "=====================\n", "【输出JSON格式】\n", "如果文档包含多笔交易，请返回数组格式。确保数字字段保持原始精度，不要四舍五入。单笔交易示例：\n", "```json\n", "{\n", "  \"投资者名称\": \"某某资产管理计划\",\n", "  \"投资者账号\": \"123456789\",\n", "  \"业务日期\": \"2024-01-15\",\n", "  \"业务类型\": \"申购\",\n", "  \"投资标的名称\": \"某某货币基金\",\n", "  \"投资标的代码\": \"000001\",\n", "  \"投资标的金额\": \"1000000.00\",\n", "  \"投资标的数量\": \"1000000.00\",\n", "  \"交易费用\": \"0.00\"\n", "}\n", "```\n", "\n", "多笔交易示例：\n", "```json\n", "[\n", "  {\n", "    \"投资者名称\": \"某某资产管理计划\",\n", "    \"投资者账号\": \"123456789\",\n", "    \"业务日期\": \"2024-01-15\",\n", "    \"业务类型\": \"申购\",\n", "    \"投资标的名称\": \"某某货币基金A\",\n", "    \"投资标的代码\": \"000001\",\n", "    \"投资标的金额\": \"1000000.00\",\n", "    \"投资标的数量\": \"1000000.00\",\n", "    \"交易费用\": \"0.00\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"某某资产管理计划\",\n", "    \"投资者账号\": \"123456789\",\n", "    \"业务日期\": \"2024-01-16\",\n", "    \"业务类型\": \"赎回\",\n", "    \"投资标的名称\": \"某某货币基金B\",\n", "    \"投资标的代码\": \"000002\",\n", "    \"投资标的金额\": \"500000.00\",\n", "    \"投资标的数量\": \"500000.00\",\n", "    \"交易费用\": \"25.00\"\n", "  }\n", "]\n", "```\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "file_processor", "metadata": {}, "outputs": [], "source": ["def process_file_to_markdown(file_path):\n", "    \"\"\"\n", "    将不同格式的文件转换为markdown格式\n", "    支持：PDF、Excel、图片、Word文档\n", "    \"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.pdf':\n", "            # 处理PDF文件\n", "            result = trans_pdf_to_markdown(\n", "                file_path, \n", "                parse_method='auto', \n", "                backend='vlm-sglang-engine'\n", "            )\n", "            markdown_content = \"\"\n", "            for fn_name, data in result['results'].items():\n", "                markdown_content += data['md_content']\n", "            return markdown_content\n", "            \n", "        elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff', '.webp']:\n", "            # 处理图片文件 - 使用OCR方式\n", "            result = trans_pdf_to_markdown(\n", "                file_path, \n", "                parse_method='ocr', \n", "                backend='vlm-sglang-engine'\n", "            )\n", "            markdown_content = \"\"\n", "            for fn_name, data in result['results'].items():\n", "                markdown_content += data['md_content']\n", "            return markdown_content\n", "            \n", "        elif file_ext in ['.xlsx', '.xls']:\n", "            # 处理Excel文件（支持新旧格式）\n", "            return process_excel_file(file_path)\n", "            \n", "        elif file_ext == '.docx':\n", "            # 处理Word文档\n", "            return process_docx_file(file_path)\n", "            \n", "        else:\n", "            raise ValueError(f\"不支持的文件格式: {file_ext}\")\n", "            \n", "    except Exception as e:\n", "        print(f\"文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_excel_file(file_path):\n", "    \"\"\"\n", "    处理Excel文件，转换为markdown格式\n", "    支持.xlsx和.xls格式\n", "    \"\"\"\n", "    file_ext = os.path.splitext(file_path)[1].lower()\n", "    \n", "    try:\n", "        if file_ext == '.xls':\n", "            # 处理老版Excel文件(.xls)\n", "            return process_xls_file(file_path)\n", "        \n", "        # 处理新版Excel文件(.xlsx) - 默认处理\n", "        # 读取Excel文件\n", "        workbook = load_workbook(file_path, data_only=True)\n", "        markdown_content = \"\"\n", "        \n", "        for sheet_name in workbook.sheetnames:\n", "            worksheet = workbook[sheet_name]\n", "            markdown_content += f\"\\n## {sheet_name}\\n\\n\"\n", "            \n", "            # 转换为DataFrame然后转为markdown表格\n", "            data = []\n", "            for row in worksheet.iter_rows(values_only=True):\n", "                if any(cell is not None for cell in row):\n", "                    data.append([str(cell) if cell is not None else \"\" for cell in row])\n", "            \n", "            if data:\n", "                df = pd.DataFrame(data[1:], columns=data[0] if data else [])\n", "                # 保持数字精度，不使用科学计数法，保留足够的小数位\n", "                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + \"\\n\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Excel文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_xls_file(file_path):\n", "    \"\"\"\n", "    处理.xls文件（老版Excel格式）\n", "    \"\"\"\n", "    try:\n", "        # 读取老版Excel文件\n", "        workbook = xlrd.open_workbook(file_path)\n", "        markdown_content = \"\"\n", "        \n", "        for sheet_index in range(workbook.nsheets):\n", "            worksheet = workbook.sheet_by_index(sheet_index)\n", "            sheet_name = workbook.sheet_names()[sheet_index]\n", "            markdown_content += f\"\\n## {sheet_name}\\n\\n\"\n", "            \n", "            # 转换为DataFrame然后转为markdown表格\n", "            data = []\n", "            for row_idx in range(worksheet.nrows):\n", "                row_data = []\n", "                for col_idx in range(worksheet.ncols):\n", "                    cell_value = worksheet.cell_value(row_idx, col_idx)\n", "                    # 处理不同类型的单元格值，保持数字精度\n", "                    if isinstance(cell_value, float):\n", "                        # 保持浮点数的原始精度，不进行四舍五入\n", "                        if cell_value.is_integer():\n", "                            # 只有当浮点数确实是整数时才转换为int\n", "                            cell_value = int(cell_value)\n", "                        else:\n", "                            # 保持原始浮点数精度，使用高精度字符串格式化\n", "                            # 使用.10g格式保留10位有效数字，避免四舍五入\n", "                            cell_value = f\"{cell_value:.10g}\"\n", "                    row_data.append(str(cell_value) if cell_value != '' else \"\")\n", "                \n", "                if any(cell != \"\" for cell in row_data):\n", "                    data.append(row_data)\n", "            \n", "            if data:\n", "                df = pd.DataFrame(data[1:], columns=data[0] if data else [])\n", "                # 保持数字精度，不使用科学计数法，保留足够的小数位\n", "                markdown_content += df.to_markdown(index=False, floatfmt='.10g') + \"\\n\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\".xls文件处理错误: {e}\")\n", "        return None\n", "\n", "def process_docx_file(file_path):\n", "    \"\"\"\n", "    处理Word文档，转换为markdown格式\n", "    \"\"\"\n", "    try:\n", "        doc = docx.Document(file_path)\n", "        markdown_content = \"\"\n", "        \n", "        # 处理段落\n", "        for paragraph in doc.paragraphs:\n", "            if paragraph.text.strip():\n", "                markdown_content += paragraph.text + \"\\n\"\n", "        \n", "        # 处理表格\n", "        for table in doc.tables:\n", "            markdown_content += \"\\n\"\n", "            for i, row in enumerate(table.rows):\n", "                row_data = []\n", "                for cell in row.cells:\n", "                    row_data.append(cell.text.strip())\n", "                \n", "                if i == 0:\n", "                    # 表头\n", "                    markdown_content += \"| \" + \" | \".join(row_data) + \" |\\n\"\n", "                    markdown_content += \"| \" + \" | \".join([\"---\"] * len(row_data)) + \" |\\n\"\n", "                else:\n", "                    # 数据行\n", "                    markdown_content += \"| \" + \" | \".join(row_data) + \" |\\n\"\n", "            markdown_content += \"\\n\"\n", "        \n", "        return markdown_content\n", "        \n", "    except Exception as e:\n", "        print(f\"Word文档处理错误: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 6, "id": "main_function", "metadata": {}, "outputs": [], "source": ["def parse_transaction_document(file_path, save_result=True, output_dir=\"./output\"):\n", "    \"\"\"\n", "    解析非标交易确认单文档\n", "    \n", "    Args:\n", "        file_path: 文档文件路径\n", "        save_result: 是否保存解析结果\n", "        output_dir: 输出目录\n", "    \n", "    Returns:\n", "        dict: 解析结果\n", "    \"\"\"\n", "    print(f\"开始处理文件: {file_path}\")\n", "    \n", "    # 检查文件是否存在\n", "    if not os.path.exists(file_path):\n", "        print(f\"错误: 文件不存在 - {file_path}\")\n", "        return None\n", "    \n", "    # 转换文件为markdown格式\n", "    print(\"正在转换文件为markdown格式...\")\n", "    markdown_content = process_file_to_markdown(file_path)\n", "    \n", "    if not markdown_content:\n", "        print(\"错误: 无法转换文件为markdown格式\")\n", "        return None\n", "    \n", "    print(f\"文件转换完成，内容长度: {len(markdown_content)} 字符\")\n", "    \n", "    # 使用大模型解析内容\n", "    print(\"正在使用大模型解析交易信息...\")\n", "    try:\n", "        response = chatbot.chat(markdown_content)\n", "        print(\"大模型解析完成\")\n", "        \n", "        # 尝试解析JSON响应\n", "        # 移除可能的markdown代码块标记\n", "        json_str = response.strip()\n", "        if json_str.startswith('```json'):\n", "            json_str = json_str[7:]\n", "        if json_str.endswith('```'):\n", "            json_str = json_str[:-3]\n", "        json_str = json_str.strip()\n", "        \n", "        parsed_result = json.loads(json_str)\n", "        \n", "        # 保存结果\n", "        if save_result:\n", "            os.makedirs(output_dir, exist_ok=True)\n", "            \n", "            # 生成输出文件名\n", "            base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "            timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "            \n", "            # 保存原始markdown内容\n", "            markdown_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_markdown.md\")\n", "            with open(markdown_file, 'w', encoding='utf-8') as f:\n", "                f.write(markdown_content)\n", "            \n", "            # 保存解析结果\n", "            result_file = os.path.join(output_dir, f\"{base_name}_{timestamp}_result.json\")\n", "            with open(result_file, 'w', encoding='utf-8') as f:\n", "                json.dump(parsed_result, f, ensure_ascii=False, indent=2)\n", "            \n", "            print(f\"结果已保存到: {result_file}\")\n", "            print(f\"Markdown内容已保存到: {markdown_file}\")\n", "        \n", "        return {\n", "            'success': True,\n", "            'file_path': file_path,\n", "            'markdown_content': markdown_content,\n", "            'parsed_result': parsed_result,\n", "            'raw_response': response\n", "        }\n", "        \n", "    except json.JSONDecodeError as e:\n", "        print(f\"JSON解析错误: {e}\")\n", "        print(f\"原始响应: {response}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': f'JSON解析错误: {e}',\n", "            'raw_response': response\n", "        }\n", "    except Exception as e:\n", "        print(f\"解析过程中发生错误: {e}\")\n", "        return {\n", "            'success': <PERSON><PERSON><PERSON>,\n", "            'error': str(e)\n", "        }"]}, {"cell_type": "code", "execution_count": 7, "id": "batch_processor", "metadata": {}, "outputs": [], "source": ["def batch_process_documents(input_dir, output_dir=\"./output\", supported_extensions=None):\n", "    \"\"\"\n", "    批量处理文档\n", "    \n", "    Args:\n", "        input_dir: 输入目录\n", "        output_dir: 输出目录\n", "        supported_extensions: 支持的文件扩展名列表\n", "    \n", "    Returns:\n", "        list: 处理结果列表\n", "    \"\"\"\n", "    if supported_extensions is None:\n", "        supported_extensions = ['.pdf', '.xlsx', '.xls', '.jpg', '.jpeg', '.png', '.docx', '.bmp', '.gif', '.tiff', '.webp']\n", "    \n", "    results = []\n", "    \n", "    if not os.path.exists(input_dir):\n", "        print(f\"错误: 输入目录不存在 - {input_dir}\")\n", "        return results\n", "    \n", "    # 获取所有支持的文件\n", "    files_to_process = []\n", "    for filename in os.listdir(input_dir):\n", "        file_ext = os.path.splitext(filename)[1].lower()\n", "        if file_ext in supported_extensions:\n", "            files_to_process.append(os.path.join(input_dir, filename))\n", "    \n", "    print(f\"找到 {len(files_to_process)} 个文件需要处理\")\n", "    \n", "    # 逐个处理文件\n", "    for i, file_path in enumerate(files_to_process, 1):\n", "        print(f\"\\n处理第 {i}/{len(files_to_process)} 个文件: {os.path.basename(file_path)}\")\n", "        \n", "        try:\n", "            result = parse_transaction_document(file_path, save_result=True, output_dir=output_dir)\n", "            results.append(result)\n", "            \n", "            if result and result.get('success'):\n", "                print(f\"✓ 处理成功\")\n", "            else:\n", "                print(f\"✗ 处理失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "                \n", "        except Exception as e:\n", "            print(f\"✗ 处理异常: {e}\")\n", "            results.append({\n", "                'success': <PERSON><PERSON><PERSON>,\n", "                'file_path': file_path,\n", "                'error': str(e)\n", "            })\n", "    \n", "    # 生成批量处理报告\n", "    successful_count = sum(1 for r in results if r and r.get('success'))\n", "    print(f\"\\n批量处理完成: {successful_count}/{len(files_to_process)} 个文件处理成功\")\n", "    \n", "    # 保存批量处理报告\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    report_file = os.path.join(output_dir, f\"batch_report_{timestamp}.json\")\n", "    with open(report_file, 'w', encoding='utf-8') as f:\n", "        json.dump({\n", "            'timestamp': timestamp,\n", "            'input_dir': input_dir,\n", "            'total_files': len(files_to_process),\n", "            'successful_files': successful_count,\n", "            'failed_files': len(files_to_process) - successful_count,\n", "            'results': results\n", "        }, f, ensure_ascii=False, indent=2)\n", "    \n", "    print(f\"批量处理报告已保存到: {report_file}\")\n", "    return results"]}, {"cell_type": "markdown", "id": "usage_examples", "metadata": {}, "source": ["## 使用示例\n", "\n", "### 1. 单个文件处理示例"]}, {"cell_type": "code", "execution_count": 8, "id": "single_file_example", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["当前工作目录: /app/宁波银行POC/ipynb\n", "尝试文件路径: ../大模型样例/非标分类/分类/非标红利转投/百瑞安鑫悦盈项目红利再投数据20241102.xls\n", "文件是否存在: True\n", "\n", "开始解析文档...\n", "开始处理文件: ../大模型样例/非标分类/分类/非标红利转投/百瑞安鑫悦盈项目红利再投数据20241102.xls\n", "正在转换文件为markdown格式...\n", "文件转换完成，内容长度: 603 字符\n", "正在使用大模型解析交易信息...\n", "大模型解析完成\n", "结果已保存到: ./output/百瑞安鑫悦盈项目红利再投数据20241102_20250721_081944_result.json\n", "Markdown内容已保存到: ./output/百瑞安鑫悦盈项目红利再投数据20241102_20250721_081944_markdown.md\n", "\n", "解析成功!\n", "解析结果:\n", "[\n", "  {\n", "    \"投资者名称\": \"百瑞至臻佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"2024-11-02\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"657713.67\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"百瑞至诚佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"2024-11-02\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"1386036.68\",\n", "    \"交易费用\": \"/\"\n", "  },\n", "  {\n", "    \"投资者名称\": \"百瑞至远佳选1号集合资金信托计划\",\n", "    \"投资者账号\": \"/\",\n", "    \"业务日期\": \"2024-11-02\",\n", "    \"业务类型\": \"分红\",\n", "    \"投资标的名称\": \"安鑫悦盈集合资金信托计划A类信托单位\",\n", "    \"投资标的代码\": \"/\",\n", "    \"投资标的金额\": \"/\",\n", "    \"投资标的数量\": \"31006.47\",\n", "    \"交易费用\": \"/\"\n", "  }\n", "]\n"]}], "source": ["import os,sys,json\n", "# 示例1: 处理PDF文件 - 使用正确的相对路径\n", "pdf_file_path = \"../大模型样例/非标分类/分类/非标红利转投/百瑞安鑫悦盈项目红利再投数据20241102.xls\"\n", "\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"尝试文件路径: {pdf_file_path}\")\n", "print(f\"文件是否存在: {os.path.exists(pdf_file_path)}\")\n", "\n", "# 如果文件存在，则处理\n", "if os.path.exists(pdf_file_path):\n", "    print(\"\\n开始解析文档...\")\n", "    result = parse_transaction_document(pdf_file_path)\n", "    \n", "    if result and result['success']:\n", "        print(\"\\n解析成功!\")\n", "        print(\"解析结果:\")\n", "        print(json.dumps(result['parsed_result'], ensure_ascii=False, indent=2))\n", "    else:\n", "        print(f\"\\n解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "else:\n", "    print(f\"\\n文件不存在: {pdf_file_path}\")\n", "    print(\"请检查文件路径是否正确\")\n", "    print(\"提示: 从ipynb目录运行时，需要使用 '../大模型样例/...' 路径\")"]}, {"cell_type": "markdown", "id": "batch_usage", "metadata": {}, "source": ["### 4. 批量处理示例"]}, {"cell_type": "code", "execution_count": 10, "id": "batch_example", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["开始批量处理目录: ../大模型样例/非标分类/分类\n", "找到 0 个文件需要处理\n", "\n", "批量处理完成: 0/0 个文件处理成功\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../大模型样例/非标测试结果/batch_report_20250721_082604.json'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(input_directory):\n\u001b[1;32m      6\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m开始批量处理目录: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00minput_directory\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 7\u001b[0m     batch_results \u001b[38;5;241m=\u001b[39m batch_process_documents(input_directory, output_directory)\n\u001b[1;32m      9\u001b[0m     \u001b[38;5;66;03m# 统计结果\u001b[39;00m\n\u001b[1;32m     10\u001b[0m     successful_results \u001b[38;5;241m=\u001b[39m [r \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m batch_results \u001b[38;5;28;01mif\u001b[39;00m r \u001b[38;5;129;01mand\u001b[39;00m r\u001b[38;5;241m.\u001b[39mget(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124msuccess\u001b[39m\u001b[38;5;124m'\u001b[39m)]\n", "Cell \u001b[0;32mIn[7], line 59\u001b[0m, in \u001b[0;36mbatch_process_documents\u001b[0;34m(input_dir, output_dir, supported_extensions)\u001b[0m\n\u001b[1;32m     57\u001b[0m timestamp \u001b[38;5;241m=\u001b[39m datetime\u001b[38;5;241m.\u001b[39mnow()\u001b[38;5;241m.\u001b[39mstrftime(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mY\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mm\u001b[39m\u001b[38;5;132;01m%d\u001b[39;00m\u001b[38;5;124m_\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mH\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mM\u001b[39m\u001b[38;5;124m%\u001b[39m\u001b[38;5;124mS\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     58\u001b[0m report_file \u001b[38;5;241m=\u001b[39m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mjoin(output_dir, \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mbatch_report_\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtimestamp\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m.json\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 59\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(report_file, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mw\u001b[39m\u001b[38;5;124m'\u001b[39m, encoding\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[1;32m     60\u001b[0m     json\u001b[38;5;241m.\u001b[39mdump({\n\u001b[1;32m     61\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtimestamp\u001b[39m\u001b[38;5;124m'\u001b[39m: timestamp,\n\u001b[1;32m     62\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124minput_dir\u001b[39m\u001b[38;5;124m'\u001b[39m: input_dir,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     66\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mresults\u001b[39m\u001b[38;5;124m'\u001b[39m: results\n\u001b[1;32m     67\u001b[0m     }, f, ensure_ascii\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, indent\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m2\u001b[39m)\n\u001b[1;32m     69\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m批量处理报告已保存到: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mreport_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/anaconda3/lib/python3.11/site-packages/IPython/core/interactiveshell.py:286\u001b[0m, in \u001b[0;36m_modified_open\u001b[0;34m(file, *args, **kwargs)\u001b[0m\n\u001b[1;32m    279\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[38;5;241m0\u001b[39m, \u001b[38;5;241m1\u001b[39m, \u001b[38;5;241m2\u001b[39m}:\n\u001b[1;32m    280\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    281\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIP<PERSON>hon won\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m by default \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    282\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    283\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124myou can use builtins\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m open.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    284\u001b[0m     )\n\u001b[0;32m--> 286\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m io_open(file, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: '../大模型样例/非标测试结果/batch_report_20250721_082604.json'"]}], "source": ["# 示例3: 批量处理文件夹中的所有文档\n", "input_directory = \"../大模型样例/非标分类/分类\"\n", "output_directory = \"../大模型样例/非标测试结果\"\n", "\n", "if os.path.exists(input_directory):\n", "    print(f\"开始批量处理目录: {input_directory}\")\n", "    batch_results = batch_process_documents(input_directory, output_directory)\n", "    \n", "    # 统计结果\n", "    successful_results = [r for r in batch_results if r and r.get('success')]\n", "    failed_results = [r for r in batch_results if not (r and r.get('success'))]\n", "    \n", "    print(f\"\\n批量处理统计:\")\n", "    print(f\"成功处理: {len(successful_results)} 个文件\")\n", "    print(f\"处理失败: {len(failed_results)} 个文件\")\n", "    \n", "    if failed_results:\n", "        print(\"\\n失败的文件:\")\n", "        for result in failed_results:\n", "            if result:\n", "                print(f\"  - {os.path.basename(result.get('file_path', '未知文件'))}: {result.get('error', '未知错误')}\")\n", "else:\n", "    print(f\"目录不存在: {input_directory}\")\n", "    print(\"请修改目录路径为实际的文档目录\")"]}, {"cell_type": "markdown", "id": "utility_functions", "metadata": {}, "source": ["### 5. 实用工具函数"]}, {"cell_type": "code", "execution_count": null, "id": "utility_functions_code", "metadata": {}, "outputs": [], "source": ["def display_parsing_result(result):\n", "    \"\"\"\n", "    美化显示解析结果\n", "    \"\"\"\n", "    if not result or not result.get('success'):\n", "        print(f\"解析失败: {result.get('error', '未知错误') if result else '返回结果为空'}\")\n", "        return\n", "    \n", "    parsed_data = result['parsed_result']\n", "    \n", "    print(\"=\" * 50)\n", "    print(\"交易确认单解析结果\")\n", "    print(\"=\" * 50)\n", "    \n", "    if isinstance(parsed_data, list):\n", "        print(f\"共发现 {len(parsed_data)} 笔交易\\n\")\n", "        for i, transaction in enumerate(parsed_data, 1):\n", "            print(f\"第 {i} 笔交易:\")\n", "            print(\"-\" * 30)\n", "            for key, value in transaction.items():\n", "                print(f\"{key:12}: {value}\")\n", "            print()\n", "    else:\n", "        print(\"单笔交易:\")\n", "        print(\"-\" * 30)\n", "        for key, value in parsed_data.items():\n", "            print(f\"{key:12}: {value}\")\n", "    \n", "    print(\"=\" * 50)\n", "\n", "def export_to_excel(results, output_file):\n", "    \"\"\"\n", "    将解析结果导出到Excel文件\n", "    \"\"\"\n", "    all_transactions = []\n", "    \n", "    for result in results:\n", "        if result and result.get('success'):\n", "            parsed_data = result['parsed_result']\n", "            file_name = os.path.basename(result.get('file_path', '未知文件'))\n", "            \n", "            if isinstance(parsed_data, list):\n", "                for transaction in parsed_data:\n", "                    transaction['源文件'] = file_name\n", "                    all_transactions.append(transaction)\n", "            else:\n", "                parsed_data['源文件'] = file_name\n", "                all_transactions.append(parsed_data)\n", "    \n", "    if all_transactions:\n", "        df = pd.DataFrame(all_transactions)\n", "        # 重新排列列的顺序\n", "        columns_order = ['源文件', '投资者名称', '投资者账号', '业务日期', '业务类型', \n", "                        '投资标的名称', '投资标的代码', '投资标的金额', '投资标的数量', '交易费用']\n", "        \n", "        # 只保留存在的列\n", "        existing_columns = [col for col in columns_order if col in df.columns]\n", "        df = df[existing_columns]\n", "        \n", "        df.to_excel(output_file, index=False)\n", "        print(f\"解析结果已导出到Excel文件: {output_file}\")\n", "        print(f\"共导出 {len(all_transactions)} 笔交易记录\")\n", "    else:\n", "        print(\"没有成功解析的交易记录可以导出\")\n", "\n", "def validate_parsing_result(parsed_data):\n", "    \"\"\"\n", "    验证解析结果的完整性\n", "    \"\"\"\n", "    required_fields = ['投资者名称', '投资者账号', '业务日期', '业务类型', '投资标的名称']\n", "    \n", "    if isinstance(parsed_data, list):\n", "        for i, transaction in enumerate(parsed_data):\n", "            missing_fields = [field for field in required_fields if field not in transaction or transaction[field] == \"/\"]\n", "            if missing_fields:\n", "                print(f\"第 {i+1} 笔交易缺少必要字段: {', '.join(missing_fields)}\")\n", "    else:\n", "        missing_fields = [field for field in required_fields if field not in parsed_data or parsed_data[field] == \"/\"]\n", "        if missing_fields:\n", "            print(f\"交易记录缺少必要字段: {', '.join(missing_fields)}\")\n", "        else:\n", "            print(\"交易记录包含所有必要字段\")"]}, {"cell_type": "markdown", "id": "test_section", "metadata": {}, "source": ["### 6. 测试区域\n", "\n", "在这里可以测试您的文档解析功能："]}, {"cell_type": "code", "execution_count": null, "id": "test_area", "metadata": {}, "outputs": [], "source": ["# 测试区域 - 路径诊断和文件处理\n", "print(\"=== 路径诊断信息 ===\")\n", "print(f\"当前工作目录: {os.getcwd()}\")\n", "print(f\"Python路径: {sys.path[0] if sys.path else 'N/A'}\")\n", "\n", "# 测试文件路径（多种可能的路径）\n", "possible_paths = [\n", "    \"大模型样例/非标红利转投（脱敏）/源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\",\n", "    \"../大模型样例/非标红利转投（脱敏）/源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\",\n", "    os.path.join(os.getcwd(), \"大模型样例\", \"非标红利转投（脱敏）\", \"源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\"),\n", "    os.path.join(os.path.dirname(os.getcwd()), \"大模型样例\", \"非标红利转投（脱敏）\", \"源流1号普通分红确认20250326-国寿养老安心禄享商业养老金产品.pdf_1750154531588.pdf\")\n", "]\n", "\n", "test_file_path = None\n", "print(\"\\n=== 检查可能的文件路径 ===\")\n", "for i, path in enumerate(possible_paths, 1):\n", "    print(f\"{i}. 检查路径: {path}\")\n", "    if os.path.exists(path):\n", "        print(f\"   ✓ 文件存在!\")\n", "        test_file_path = path\n", "        break\n", "    else:\n", "        print(f\"   ✗ 文件不存在\")\n", "\n", "# 如果找到文件，则进行处理\n", "if test_file_path:\n", "    print(f\"\\n=== 开始处理文件 ===\")\n", "    print(f\"使用路径: {test_file_path}\")\n", "    \n", "    try:\n", "        # 解析文档\n", "        result = parse_transaction_document(test_file_path)\n", "        \n", "        # 显示结果\n", "        display_parsing_result(result)\n", "        \n", "        # 验证结果\n", "        if result and result.get('success'):\n", "            validate_parsing_result(result['parsed_result'])\n", "            \n", "            # 导出到Excel（可选）\n", "            # export_to_excel([result], \"test_result.xlsx\")\n", "            \n", "    except Exception as e:\n", "        print(f\"处理文件时发生错误: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        \n", "else:\n", "    print(\"\\n=== 未找到测试文件 ===\")\n", "    print(\"请检查以下事项:\")\n", "    print(\"1. 确认文件确实存在于指定位置\")\n", "    print(\"2. 检查文件路径是否正确（注意中文字符和特殊字符）\")\n", "    print(\"3. 确认当前工作目录是否正确\")\n", "    print(\"4. 尝试使用绝对路径\")\n", "    print(\"\\n支持的文件格式: PDF, Excel(.xlsx), 图片(.jpg, .png等), Word(.docx)\")\n", "    \n", "    # 列出当前目录的内容以供参考\n", "    print(\"\\n=== 当前目录内容 ===\")\n", "    try:\n", "        current_dir_contents = os.listdir('.')\n", "        for item in sorted(current_dir_contents)[:10]:  # 只显示前10个项目\n", "            item_path = os.path.join('.', item)\n", "            item_type = \"[DIR]\" if os.path.isdir(item_path) else \"[FILE]\"\n", "            print(f\"  {item_type} {item}\")\n", "        if len(current_dir_contents) > 10:\n", "            print(f\"  ... 还有 {len(current_dir_contents) - 10} 个项目\")\n", "    except Exception as e:\n", "        print(f\"无法列出目录内容: {e}\")"]}, {"cell_type": "markdown", "id": "dependency_fix", "metadata": {}, "source": ["### 7. 依赖库问题修复\n", "\n", "如果遇到缺少依赖库的错误（如tabulate或xlrd），可以使用以下代码："]}, {"cell_type": "code", "execution_count": null, "id": "install_dependencies", "metadata": {}, "outputs": [], "source": ["# 手动安装缺少的依赖库\n", "import subprocess\n", "import sys\n", "\n", "def install_package(package_name):\n", "    \"\"\"安装指定的Python包\"\"\"\n", "    try:\n", "        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_name])\n", "        print(f\"✓ {package_name} 安装成功\")\n", "        return True\n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"✗ {package_name} 安装失败: {e}\")\n", "        return False\n", "\n", "# 安装常用的依赖库\n", "required_packages = ['tabulate', 'openpyxl', 'xlrd', 'python-docx', 'PyPDF2', 'Pillow']\n", "\n", "print(\"开始安装必要的依赖库...\")\n", "for package in required_packages:\n", "    print(f\"\\n正在安装 {package}...\")\n", "    install_package(package)\n", "\n", "print(\"\\n依赖库安装完成！请重新运行导入代码。\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}
# 多场景智能化文档分析系统 API 接口文档

## 1. 接口概述

本文档详细描述了多场景智能化文档分析系统的所有 REST API 接口。系统主要用于处理各类金融文档的智能分析，包括期货账户、理财产品、券商计息变更、期货交易会员、宁银费用变更、产品说明书等多种业务场景。

## 2. 接口清单

### 2.1 文件上传接口

**接口**: `/api/upload`  
**方法**: POST  
**说明**: 上传分析文件

**请求参数**:

```json
{
  "file": "文件对象(multipart/form-data)",
  "type": "分析类型(future/financial/broker_interest/futures_member/ningyin_fee/product_manual)"
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "文件上传成功",
  "filename": "20240315_123456_abcd1234_example.pdf",
  "filepath": "uploads/future/20240315_123456_abcd1234_example.pdf",
  "type": "future"
}
```

### 2.2 文件分析接口

**接口**: `/api/analyze`  
**方法**: POST  
**说明**: 执行文件分析，支持挡板模式和进度提示

**请求参数**:

```json
{
    "filename": "要分析的文件名",
    "type": "分析类型",
    "use_mock": true/false
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "分析完成",
  "data": {
    "record_id": 123,
    "ai_result": {},
    "system_result": {},
    "comparison_result": {},
    "use_mock": true
  },
  "type": "future"
}
```

### 2.3 批量分析接口

**接口**: `/api/batch-analyze`  
**方法**: POST  
**说明**: 批量处理多个文件，支持进度提示

**请求参数**:

```json
{
    "filenames": ["文件1.pdf", "文件2.pdf"],
    "type": "分析类型",
    "use_mock": true/false
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "批量分析完成，共处理 2 个文件",
  "results": [
    {
      "filename": "文件1.pdf",
      "success": true,
      "data": {
        "record_id": 124,
        "ai_result": {},
        "system_result": {},
        "comparison_result": {},
        "use_mock": true
      }
    },
    {
      "filename": "文件2.pdf",
      "success": true,
      "data": {
        "record_id": 125,
        "ai_result": {},
        "system_result": {},
        "comparison_result": {},
        "use_mock": true
      }
    }
  ]
}
```

### 2.4 分析记录查询接口

**接口**: `/api/records`  
**方法**: GET  
**说明**: 获取分析记录列表

**请求参数**:

```
?type=分析类型&limit=50
```

**响应示例**:

```json
{
  "success": true,
  "records": [
    {
      "id": 123,
      "filename": "example.pdf",
      "analysis_type": "future",
      "ai_result": {},
      "system_result": {},
      "comparison_result": {},
      "created_at": "2024-03-15 12:34:56"
    }
  ]
}
```

### 2.5 单个记录查询接口

**接口**: `/api/records/<record_id>`  
**方法**: GET  
**说明**: 获取单个分析记录详情

**响应示例**:

```json
{
  "success": true,
  "record": {
    "id": 123,
    "filename": "example.pdf",
    "analysis_type": "future",
    "ai_result": {},
    "system_result": {},
    "comparison_result": {},
    "created_at": "2024-03-15 12:34:56"
  }
}
```

### 2.6 挡板模式管理接口

#### 2.6.1 设置挡板模式

**接口**: `/api/mock-mode`  
**方法**: POST  
**说明**: 设置挡板模式

**请求参数**:

```json
{
    "use_mock": true/false
}
```

**响应示例**:

```json
{
  "success": true,
  "message": "挡板模式已设置为: 开启",
  "use_mock": true
}
```

#### 2.6.2 获取挡板状态

**接口**: `/api/mock-status`  
**方法**: GET  
**说明**: 获取当前挡板模式状态

**响应示例**:

```json
{
  "success": true,
  "status": {
    "use_mock": true,
    "enabled_types": ["future", "financial"]
  }
}
```

#### 2.6.3 更新挡板配置

**接口**: `/api/mock-config`  
**方法**: POST  
**说明**: 更新挡板配置（如启用/禁用某类型挡板、更新数据）

**请求参数**:

```json
{
  "query_type": "future",
  "enabled": true,
  "data": {}
}
```

#### 2.6.4 挡板数据自定义接口（补充）

**接口**: `/api/mock-data`  
**方法**: GET/POST  
**说明**: 获取或保存当前挡板数据（支持前端弹窗自定义编辑、格式化、重置、保存，数据持久化到数据库）

- GET: 获取当前所有挡板数据
- POST: 保存/更新指定类型的挡板数据

**GET 响应示例**:

```json
{
  "success": true,
  "data": {
    "future": { ... },
    "financial": { ... }
  }
}
```

**POST 请求示例**:

```json
{
  "query_type": "future",
  "mock_data": { ... }
}
```

**POST 响应示例**:

```json
{
  "success": true,
  "message": "挡板数据已保存"
}
```

### 2.7 文件管理接口

#### 2.7.1 获取文件列表

**接口**: `/api/files`  
**方法**: GET  
**说明**: 获取上传文件列表

**请求参数**:

```
?type=分析类型
```

**响应示例**:

```json
{
  "success": true,
  "files": [
    {
      "filename": "example.pdf",
      "size": 1024,
      "modified": "2024-03-15 12:34:56"
    }
  ],
  "type": "future",
  "type_name": "期货账户分析"
}
```

#### 2.7.2 删除文件

**接口**: `/api/delete/<filename>`  
**方法**: DELETE  
**说明**: 删除指定文件

**请求参数**:

```
?type=分析类型
```

**响应示例**:

```json
{
  "success": true,
  "message": "文件删除成功"
}
```

## 3. 错误码说明

| 状态码 | 说明           |
| ------ | -------------- |
| 200    | 请求成功       |
| 400    | 请求参数错误   |
| 404    | 资源不存在     |
| 500    | 服务器内部错误 |

## 4. 注意事项

1. 文件上传
   - 支持的文件类型: pdf, png, jpg, jpeg, gif, bmp, tiff
   - 单个文件大小限制: 50MB
   - 文件名会自动生成,包含时间戳和随机字符串
2. 分析类型
   - future: 期货账户分析
   - financial: 理财产品分析
   - broker_interest: 券商计息变更
   - futures_member: 期货交易会员
   - ningyin_fee: 宁银费用变更
   - product_manual: 产品说明书
3. 挡板模式
   - 默认开启挡板模式
   - 可通过接口动态切换
   - 支持按业务类型配置挡板数据
   - 挡板数据可前端自定义编辑、格式化、重置、保存，持久化到数据库
4. 分析进度提示
   - 单文件和批量分析均支持进度提示，分析中提示常亮，分析结束后消失
5. AI/系统内容展示
   - 所有分析类型均支持AI识别内容与系统内容对比，前端窗口美观统一，支持卡片式、表格等展示
6. 安全性
   - 所有接口支持 CORS
   - 文件上传有类型和大小限制
   - 详细的错误日志记录

---

## 3. 增量补充接口说明（2024年6月）

### 3.1 用户认证相关接口

#### 3.1.1 用户注册
- **接口**: `/api/register`
- **方法**: POST
- **参数**: `{ "username": "用户名", "password": "密码" }`
- **返回**: `{ "success": true/false, "message": "注册成功/失败原因" }`

#### 3.1.2 用户登录
- **接口**: `/api/login`
- **方法**: POST
- **参数**: `{ "username": "用户名", "password": "密码" }`
- **返回**: `{ "success": true/false, "message": "登录成功/失败原因" }`

#### 3.1.3 用户登出
- **接口**: `/api/logout`
- **方法**: POST
- **返回**: `{ "success": true/false, "message": "已登出" }`

#### 3.1.4 获取当前用户信息
- **接口**: `/api/userinfo`
- **方法**: GET
- **返回**: `{ "success": true, "user": "用户名", "role": "角色" }`

### 3.2 审核与导出相关接口

#### 3.2.1 人工审核分析记录
- **接口**: `/api/records/<record_id>/audit`
- **方法**: POST
- **参数**: `{ "status": "审核状态", "comment": "审核备注" }`
- **返回**: `{ "success": true/false, "message": "审核结果已保存/失败原因" }`

#### 3.2.2 导出分析记录为Excel
- **接口**: `/api/export/excel`
- **方法**: GET
- **参数**: `type`（可选，分析类型），`limit`（可选，导出条数）
- **返回**: Excel文件流

#### 3.2.3 导出分析记录为PDF
- **接口**: `/api/export/pdf`
- **方法**: GET
- **参数**: `type`（可选，分析类型），`limit`（可选，导出条数）
- **返回**: PDF文件流

#### 3.2.4 分析记录统计与趋势
- **接口**: `/api/report/summary`
- **方法**: GET
- **返回**: `{ "success": true, "type_counts": {"future": 10, ...}, "trend": {"2024-06-01": 2, ...} }`

### 3.3 模型管理相关接口

#### 3.3.1 查询/切换当前大模型
- **接口**: `/api/model/active`
- **方法**: GET/POST
- **参数**: `model`（POST时，切换目标模型名）
- **返回**: `{ "success": true, "active_model": "当前模型名", "config": { ... } }`

### 3.4 挡板与Mock管理相关接口

#### 3.4.1 获取指定类型挡板数据
- **接口**: `/api/mock/data/<analysis_type>`
- **方法**: GET
- **返回**: `{ "success": true, "data": { ... } }`

#### 3.4.2 保存挡板数据
- **接口**: `/api/mock/data/save`
- **方法**: POST
- **参数**: `{ "type": "分析类型", "data": { ... } }`
- **返回**: `{ "success": true/false, "message": "挡板数据保存成功/失败原因" }`

#### 3.4.3 添加挡板数据（参数化key）
- **接口**: `/api/mock-data`
- **方法**: POST
- **参数**: `{ "query_key": "参数key", "query_type": "分析类型", "mock_result": { ... } }`
- **返回**: `{ "success": true/false, "message": "挡板数据添加成功/失败原因" }`

---

## 4. 新增功能接口说明（2024年12月）

### 4.1 仪表盘与统计接口

#### 4.1.1 获取仪表盘数据
- **接口**: `/api/dashboard/stats`
- **方法**: GET
- **说明**: 获取首页仪表盘统计数据，包括识别准确率等关键指标
- **返回**:
```json
{
  "success": true,
  "data": {
    "total_files": 1250,
    "total_analyzed": 1180,
    "accuracy_rate": 0.945,
    "today_processed": 45,
    "type_stats": {
      "future": {"total": 500, "accuracy": 0.95},
      "broker_interest": {"total": 300, "accuracy": 0.92},
      "non_standard_trade": {"total": 250, "accuracy": 0.94}
    },
    "recent_trend": [
      {"date": "2024-12-01", "count": 42, "accuracy": 0.94},
      {"date": "2024-12-02", "count": 38, "accuracy": 0.96}
    ]
  }
}
```

#### 4.1.2 获取识别准确率详情
- **接口**: `/api/dashboard/accuracy`
- **方法**: GET
- **参数**: `type`（可选，分析类型），`period`（可选，时间周期：day/week/month）
- **返回**:
```json
{
  "success": true,
  "data": {
    "overall_accuracy": 0.945,
    "period_accuracy": 0.952,
    "accuracy_trend": [
      {"period": "2024-12-01", "accuracy": 0.94, "total": 42},
      {"period": "2024-12-02", "accuracy": 0.96, "total": 38}
    ],
    "type_breakdown": {
      "future": 0.95,
      "broker_interest": 0.92,
      "non_standard_trade": 0.94
    }
  }
}
```

### 4.2 文件管理增强接口

#### 4.2.1 文件状态管理
- **接口**: `/api/files/<file_id>/status`
- **方法**: PUT
- **说明**: 更新文件状态（废弃/恢复）
- **参数**:
```json
{
  "status": "deprecated|active",
  "reason": "废弃原因（可选）"
}
```
- **返回**:
```json
{
  "success": true,
  "message": "文件状态已更新",
  "data": {
    "file_id": 123,
    "status": "deprecated",
    "updated_at": "2024-12-18 10:30:00"
  }
}
```

#### 4.2.2 文件打标管理
- **接口**: `/api/files/<file_id>/tags`
- **方法**: POST/PUT/DELETE
- **说明**: 对已分析文件进行打标管理
- **参数**:
```json
{
  "tags": ["已审核", "高准确率", "标准样本"],
  "color": "#2563eb"
}
```
- **返回**:
```json
{
  "success": true,
  "message": "标签已更新",
  "data": {
    "file_id": 123,
    "tags": ["已审核", "高准确率", "标准样本"],
    "updated_at": "2024-12-18 10:30:00"
  }
}
```

#### 4.2.3 重新分析接口
- **接口**: `/api/files/<file_id>/reanalyze`
- **方法**: POST
- **说明**: 对已分析文件重新进行分析
- **参数**:
```json
{
  "use_mock": false,
  "force_reprocess": true
}
```
- **返回**:
```json
{
  "success": true,
  "message": "重新分析已启动",
  "data": {
    "record_id": 456,
    "ai_result": {},
    "system_result": {},
    "comparison_result": {}
  }
}
```

#### 4.2.4 文件去重检查
- **接口**: `/api/files/duplicate-check`
- **方法**: POST
- **说明**: 检查上传文件是否重复
- **参数**:
```json
{
  "filename": "example.pdf",
  "file_hash": "abc123def456",
  "file_size": 1024000
}
```
- **返回**:
```json
{
  "success": true,
  "is_duplicate": false,
  "existing_files": [],
  "similarity_matches": [
    {
      "file_id": 789,
      "filename": "similar_example.pdf",
      "similarity": 0.85
    }
  ]
}
```

### 4.3 提示词版本管理接口

#### 4.3.1 获取提示词版本列表
- **接口**: `/api/prompts/<analysis_type>/versions`
- **方法**: GET
- **说明**: 获取指定分析类型的提示词版本历史
- **返回**:
```json
{
  "success": true,
  "data": {
    "current_version": "v2.1",
    "versions": [
      {
        "version": "v2.1",
        "created_at": "2024-12-15 14:30:00",
        "created_by": "admin",
        "description": "优化识别准确率",
        "is_active": true,
        "usage_count": 150
      },
      {
        "version": "v2.0",
        "created_at": "2024-12-01 09:00:00",
        "created_by": "admin",
        "description": "初始版本",
        "is_active": false,
        "usage_count": 89
      }
    ]
  }
}
```

#### 4.3.2 切换提示词版本
- **接口**: `/api/prompts/<analysis_type>/switch-version`
- **方法**: POST
- **说明**: 切换到指定版本的提示词
- **参数**:
```json
{
  "version": "v2.0",
  "reason": "回滚测试"
}
```
- **返回**:
```json
{
  "success": true,
  "message": "提示词版本已切换",
  "data": {
    "analysis_type": "future",
    "old_version": "v2.1",
    "new_version": "v2.0",
    "switched_at": "2024-12-18 10:30:00"
  }
}
```

#### 4.3.3 创建新版本提示词
- **接口**: `/api/prompts/<analysis_type>/create-version`
- **方法**: POST
- **说明**: 基于当前版本创建新版本提示词
- **参数**:
```json
{
  "description": "版本描述",
  "prompt_content": "新的提示词内容",
  "auto_activate": true
}
```
- **返回**:
```json
{
  "success": true,
  "message": "新版本已创建",
  "data": {
    "version": "v2.2",
    "analysis_type": "future",
    "created_at": "2024-12-18 10:30:00"
  }
}
```

### 4.4 管理员复核功能接口

#### 4.4.1 获取待复核文件列表
- **接口**: `/api/admin/review/pending`
- **方法**: GET
- **说明**: 获取需要管理员复核的文件列表
- **参数**: `type`（可选，分析类型），`limit`（可选，限制数量）
- **返回**:
```json
{
  "success": true,
  "data": {
    "total_pending": 25,
    "files": [
      {
        "record_id": 123,
        "filename": "example.pdf",
        "analysis_type": "future",
        "created_at": "2024-12-18 09:00:00",
        "accuracy_score": 0.85,
        "priority": "high"
      }
    ]
  }
}
```

#### 4.4.2 管理员复核操作
- **接口**: `/api/admin/review/<record_id>`
- **方法**: POST
- **说明**: 管理员对分析结果进行复核
- **参数**:
```json
{
  "status": "approved|rejected|needs_revision",
  "comment": "复核意见",
  "corrections": {
    "field_name": "corrected_value"
  },
  "auto_next": true
}
```
- **返回**:
```json
{
  "success": true,
  "message": "复核完成",
  "data": {
    "record_id": 123,
    "review_status": "approved",
    "next_record": {
      "record_id": 124,
      "filename": "next_file.pdf"
    }
  }
}
```

#### 4.4.3 一键全部自动复核
- **接口**: `/api/admin/review/auto-approve-all`
- **方法**: POST
- **说明**: 对所有待复核文件进行自动复核
- **参数**:
```json
{
  "criteria": {
    "min_accuracy": 0.9,
    "analysis_types": ["future", "broker_interest"],
    "exclude_flagged": true
  },
  "comment": "批量自动复核"
}
```
- **返回**:
```json
{
  "success": true,
  "message": "自动复核已完成",
  "data": {
    "total_processed": 18,
    "approved": 15,
    "skipped": 3,
    "failed": 0,
    "processing_time": "2.5s"
  }
}
```

### 4.5 挡板数据编辑接口

#### 4.5.1 编辑AI识别结果
- **接口**: `/api/mock/edit-ai-result`
- **方法**: POST
- **说明**: 编辑首次AI识别结果作为标准答案
- **参数**:
```json
{
  "record_id": 123,
  "analysis_type": "broker_interest",
  "edited_result": {
    "field1": "edited_value1",
    "field2": "edited_value2"
  },
  "save_as_standard": true
}
```
- **返回**:
```json
{
  "success": true,
  "message": "AI结果已编辑并保存为标准答案",
  "data": {
    "record_id": 123,
    "standard_answer_id": 456,
    "updated_at": "2024-12-18 10:30:00"
  }
}
```

### 4.6 PDF原件展示接口

#### 4.6.1 获取PDF文件信息
- **接口**: `/api/files/<file_id>/pdf-info`
- **方法**: GET
- **说明**: 获取PDF文件的基本信息和预览数据
- **返回**:
```json
{
  "success": true,
  "data": {
    "file_id": 123,
    "filename": "example.pdf",
    "file_path": "/uploads/future/example.pdf",
    "file_size": 1024000,
    "page_count": 5,
    "created_at": "2024-12-18 09:00:00",
    "preview_available": true
  }
}
```

#### 4.6.2 PDF文件预览
- **接口**: `/api/files/<file_id>/preview`
- **方法**: GET
- **说明**: 获取PDF文件预览（返回文件流或base64）
- **参数**: `page`（可选，页码），`format`（可选，返回格式：stream/base64）
- **返回**: PDF文件流或base64编码的图片数据

### 4.7 大模型配置管理接口

#### 4.7.1 获取模型配置列表
- **接口**: `/api/models/configs`
- **方法**: GET
- **说明**: 获取所有大模型配置信息
- **返回**:
```json
{
  "success": true,
  "data": {
    "active_model": "qwen3-32b",
    "models": [
      {
        "model_id": "qwen3-32b",
        "model_name": "Qwen3-32B",
        "api_url": "http://192.168.10.236:23000/v1",
        "api_key": "sk-***",
        "status": "active",
        "last_test": "2024-12-18 10:00:00",
        "response_time": "1.2s"
      }
    ]
  }
}
```

#### 4.7.2 添加/更新模型配置
- **接口**: `/api/models/configs`
- **方法**: POST/PUT
- **说明**: 添加或更新大模型配置
- **参数**:
```json
{
  "model_id": "custom-model",
  "model_name": "自定义模型",
  "api_url": "https://api.example.com/v1",
  "api_key": "sk-custom-key",
  "vision_model": "custom-vision",
  "timeout": 30,
  "max_tokens": 4096
}
```
- **返回**:
```json
{
  "success": true,
  "message": "模型配置已保存",
  "data": {
    "model_id": "custom-model",
    "created_at": "2024-12-18 10:30:00"
  }
}
```

#### 4.7.3 测试模型连接
- **接口**: `/api/models/<model_id>/test`
- **方法**: POST
- **说明**: 测试指定模型的连接状态
- **返回**:
```json
{
  "success": true,
  "data": {
    "model_id": "qwen3-32b",
    "connection_status": "success",
    "response_time": "1.2s",
    "test_message": "模型连接正常",
    "tested_at": "2024-12-18 10:30:00"
  }
}
```

### 4.8 导出功能增强接口

#### 4.8.1 多Sheet Excel导出
- **接口**: `/api/export/excel-multi-sheet`
- **方法**: POST
- **说明**: 导出Excel文件，一个文件对应一个Sheet
- **参数**:
```json
{
  "record_ids": [123, 124, 125],
  "include_original": true,
  "sheet_naming": "filename|record_id|custom"
}
```
- **返回**: Excel文件流，包含多个Sheet

---

## 5. 接口使用注意事项

### 5.1 新增功能特性
1. **自动分析**: 单文件上传时自动触发分析，多文件上传时可选择是否自动分析
2. **文件状态管理**: 支持废弃/恢复操作，废弃文件不参与统计和分析
3. **版本管理**: 提示词支持多版本管理，可随时切换和回滚
4. **智能复核**: 管理员复核支持自动跳转下一个待复核文件
5. **挡板编辑**: 支持编辑AI识别结果作为标准答案，提高后续识别准确率

### 5.2 权限控制
- **普通用户**: 文件上传、分析、查看结果
- **分析师**: 额外支持文件打标、重新分析
- **管理员**: 全部功能，包括复核、配置管理、系统设置

### 5.3 性能优化
- **批量操作**: 支持批量复核、批量导出等操作
- **缓存机制**: 仪表盘数据、统计信息支持缓存
- **异步处理**: 重新分析、批量操作采用异步处理

---

> 以上为2024年12月最新功能增量补充，涵盖了仪表盘、文件管理、版本控制、复核功能等核心新特性。

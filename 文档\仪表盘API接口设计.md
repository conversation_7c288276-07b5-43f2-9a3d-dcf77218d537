# 仪表盘API接口设计文档

## 1. 概述

本文档详细描述了多场景智能化文档分析系统仪表盘所需的API接口设计。这些接口将为首页仪表盘提供全面的数据支持，包括关键指标、趋势分析、分布统计、实时监控等多个维度的数据。

## 2. 基础接口规范

### 2.1 请求格式

所有API请求应遵循以下格式：

```
GET/POST /api/dashboard/{endpoint}
```

### 2.2 响应格式

所有API响应应遵循以下JSON格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 2.3 错误处理

错误响应格式：

```json
{
  "success": false,
  "message": "错误信息",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 2.4 通用参数

所有仪表盘接口支持以下通用参数：

| 参数名 | 类型 | 说明 | 默认值 |
|-------|-----|------|-------|
| period | string | 时间周期(day/week/month/year) | day |
| start_date | string | 开始日期(YYYY-MM-DD) | 30天前 |
| end_date | string | 结束日期(YYYY-MM-DD) | 今天 |
| analysis_type | string | 分析类型(可选) | 全部 |
| refresh | boolean | 是否强制刷新缓存 | false |

## 3. 核心接口设计

### 3.1 关键指标接口

#### 3.1.1 获取关键指标卡片数据

**接口**: `/api/dashboard/kpi-cards`  
**方法**: GET  
**说明**: 获取首页顶部关键指标卡片数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "total_files": {
      "title": "总处理量",
      "value": 12580,
      "formatted_value": "12,580",
      "change": 8.5,
      "change_type": "increase",
      "period": "month",
      "icon": "📄",
      "color": "#2563eb"
    },
    "accuracy_rate": {
      "title": "识别准确率",
      "value": 94.5,
      "formatted_value": "94.5%",
      "change": 2.1,
      "change_type": "increase",
      "period": "month",
      "icon": "🎯",
      "color": "#10b981"
    },
    "today_processed": {
      "title": "今日处理",
      "value": 156,
      "formatted_value": "156",
      "change": 12,
      "change_type": "increase",
      "period": "day",
      "icon": "📊",
      "color": "#f59e0b"
    },
    "pending_review": {
      "title": "待复核",
      "value": 23,
      "formatted_value": "23",
      "change": -5,
      "change_type": "decrease",
      "period": "day",
      "icon": "⏳",
      "color": "#ef4444"
    },
    "system_status": {
      "title": "系统状态",
      "value": "正常",
      "uptime": 99.8,
      "formatted_uptime": "99.8%",
      "period": "month",
      "icon": "✅",
      "color": "#06b6d4"
    }
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 3.2 趋势分析接口

#### 3.2.1 获取处理量趋势数据

**接口**: `/api/dashboard/trends/processing-volume`  
**方法**: GET  
**说明**: 获取文件处理量趋势数据  
**参数**: 通用参数 + `granularity`(hour/day/week/month)  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "文件处理量趋势",
    "xAxis": {
      "type": "time",
      "data": ["2024-12-12", "2024-12-13", "2024-12-14", "2024-12-15", "2024-12-16", "2024-12-17", "2024-12-18"]
    },
    "yAxis": {
      "type": "value",
      "name": "处理数量"
    },
    "series": [
      {
        "name": "总量",
        "type": "line",
        "data": [120, 132, 101, 134, 90, 230, 210],
        "color": "#2563eb"
      },
      {
        "name": "期货开户",
        "type": "line",
        "data": [45, 52, 38, 67, 73, 89, 94],
        "color": "#10b981"
      },
      {
        "name": "券商计息",
        "type": "line",
        "data": [23, 28, 31, 42, 38, 45, 52],
        "color": "#f59e0b"
      },
      {
        "name": "非标交易",
        "type": "line",
        "data": [12, 15, 18, 22, 25, 28, 31],
        "color": "#ef4444"
      }
    ],
    "legend": ["总量", "期货开户", "券商计息", "非标交易"]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

#### 3.2.2 获取准确率趋势数据

**接口**: `/api/dashboard/trends/accuracy-rate`  
**方法**: GET  
**说明**: 获取识别准确率趋势数据  
**参数**: 通用参数 + `granularity`(day/week/month)  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "识别准确率趋势",
    "xAxis": {
      "type": "time",
      "data": ["2024-12-12", "2024-12-13", "2024-12-14", "2024-12-15", "2024-12-16", "2024-12-17", "2024-12-18"]
    },
    "yAxis": {
      "type": "value",
      "name": "准确率(%)",
      "min": 80,
      "max": 100
    },
    "series": [
      {
        "name": "整体准确率",
        "type": "line",
        "data": [92.5, 93.2, 94.1, 93.8, 94.5, 95.2, 94.8],
        "color": "#2563eb"
      },
      {
        "name": "期货开户",
        "type": "line",
        "data": [94.2, 94.8, 95.1, 94.9, 95.5, 96.1, 95.8],
        "color": "#10b981"
      },
      {
        "name": "券商计息",
        "type": "line",
        "data": [91.8, 92.5, 93.2, 92.9, 93.6, 94.3, 93.9],
        "color": "#f59e0b"
      }
    ],
    "markLine": {
      "data": [
        {
          "name": "目标准确率",
          "yAxis": 95,
          "lineStyle": {
            "type": "dashed",
            "color": "#ef4444"
          }
        }
      ]
    },
    "legend": ["整体准确率", "期货开户", "券商计息"]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 3.3 分布统计接口

#### 3.3.1 获取业务类型分布数据

**接口**: `/api/dashboard/distribution/business-types`  
**方法**: GET  
**说明**: 获取业务类型分布数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "业务类型分布",
    "series": [
      {
        "type": "pie",
        "data": [
          {"name": "期货开户", "value": 4580, "percentage": 45.8, "color": "#2563eb"},
          {"name": "券商计息", "value": 2890, "percentage": 28.9, "color": "#10b981"},
          {"name": "非标交易", "value": 1650, "percentage": 16.5, "color": "#f59e0b"},
          {"name": "理财产品", "value": 890, "percentage": 8.9, "color": "#ef4444"}
        ]
      }
    ],
    "total": 10010,
    "legend": ["期货开户", "券商计息", "非标交易", "理财产品"]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

#### 3.3.2 获取文件状态分布数据

**接口**: `/api/dashboard/distribution/file-status`  
**方法**: GET  
**说明**: 获取文件状态分布数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "文件状态分布",
    "series": [
      {
        "type": "pie",
        "radius": ["50%", "70%"],
        "data": [
          {"name": "已完成", "value": 8950, "percentage": 71.2, "color": "#10b981"},
          {"name": "处理中", "value": 1580, "percentage": 12.6, "color": "#f59e0b"},
          {"name": "待复核", "value": 1230, "percentage": 9.8, "color": "#ef4444"},
          {"name": "已废弃", "value": 820, "percentage": 6.5, "color": "#6b7280"}
        ]
      }
    ],
    "total": 12580,
    "legend": ["已完成", "处理中", "待复核", "已废弃"]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

#### 3.3.3 获取处理时长分布数据

**接口**: `/api/dashboard/distribution/processing-time`  
**方法**: GET  
**说明**: 获取处理时长分布数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "处理时长分布",
    "xAxis": {
      "type": "category",
      "data": ["0-30s", "30s-1min", "1-2min", "2-5min", ">5min"]
    },
    "yAxis": {
      "type": "value",
      "name": "文件数量"
    },
    "series": [
      {
        "type": "bar",
        "data": [3250, 980, 520, 180, 70],
        "itemStyle": {
          "color": {
            "type": "linear",
            "x": 0, "y": 0, "x2": 0, "y2": 1,
            "colorStops": [
              {"offset": 0, "color": "#2563eb"},
              {"offset": 1, "color": "#93c5fd"}
            ]
          }
        }
      }
    ],
    "total": 5000,
    "percentages": [65.0, 19.6, 10.4, 3.6, 1.4]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

#### 3.3.4 获取错误类型统计数据

**接口**: `/api/dashboard/distribution/error-types`  
**方法**: GET  
**说明**: 获取错误类型统计数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "常见错误类型统计",
    "yAxis": {
      "type": "category",
      "data": ["图片质量问题", "字段识别错误", "格式不匹配", "系统查询失败", "网络超时"]
    },
    "xAxis": {
      "type": "value",
      "name": "错误次数"
    },
    "series": [
      {
        "type": "bar",
        "data": [145, 98, 76, 52, 41],
        "itemStyle": {
          "color": function(params) {
            const colors = ["#ef4444", "#f59e0b", "#10b981", "#2563eb", "#6b7280"];
            return colors[params.dataIndex];
          }
        }
      }
    ],
    "total": 412,
    "percentages": [35.2, 23.8, 18.4, 12.6, 10.0]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 3.4 实时监控接口

#### 3.4.1 获取实时处理队列数据

**接口**: `/api/dashboard/realtime/processing-queue`  
**方法**: GET  
**说明**: 获取实时处理队列数据  
**参数**: `limit`(默认10)  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "实时处理队列",
    "columns": [
      {"key": "filename", "title": "文件名", "width": "30%"},
      {"key": "type", "title": "业务类型", "width": "15%"},
      {"key": "status", "title": "状态", "width": "15%"},
      {"key": "progress", "title": "进度", "width": "20%"},
      {"key": "startTime", "title": "开始时间", "width": "20%"}
    ],
    "data": [
      {
        "id": 12345,
        "filename": "期货开户_20241218_001.pdf",
        "type": "期货开户",
        "status": "处理中",
        "progress": 75,
        "startTime": "10:25:30"
      },
      {
        "id": 12346,
        "filename": "券商计息_20241218_002.pdf",
        "type": "券商计息",
        "status": "等待中",
        "progress": 0,
        "startTime": "10:28:45"
      }
    ],
    "total": 5,
    "refresh_interval": 5
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

#### 3.4.2 获取系统性能监控数据

**接口**: `/api/dashboard/realtime/system-performance`  
**方法**: GET  
**说明**: 获取系统性能监控数据  
**参数**: 无  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "系统性能监控",
    "metrics": [
      {
        "name": "CPU使用率",
        "value": 45,
        "max": 100,
        "unit": "%",
        "color": "#10b981",
        "threshold": [70, 90]
      },
      {
        "name": "内存使用率",
        "value": 62,
        "max": 100,
        "unit": "%",
        "color": "#f59e0b",
        "threshold": [80, 95]
      },
      {
        "name": "磁盘使用率",
        "value": 38,
        "max": 100,
        "unit": "%",
        "color": "#2563eb",
        "threshold": [85, 95]
      },
      {
        "name": "API响应时间",
        "value": 320,
        "max": 2000,
        "unit": "ms",
        "color": "#ef4444",
        "threshold": [500, 1000]
      }
    ],
    "refresh_interval": 30
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

## 4. 扩展接口设计

### 4.1 用户活跃度热力图接口

**接口**: `/api/dashboard/heatmap/user-activity`  
**方法**: GET  
**说明**: 获取用户活跃度热力图数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "用户活跃度热力图",
    "xAxis": {
      "type": "category",
      "data": ["00:00", "02:00", "04:00", "06:00", "08:00", "10:00", "12:00", "14:00", "16:00", "18:00", "20:00", "22:00"]
    },
    "yAxis": {
      "type": "category",
      "data": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
    },
    "series": [
      {
        "type": "heatmap",
        "data": [
          [0, 0, 5], [0, 1, 1], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 2], [0, 6, 4],
          [1, 0, 7], [1, 1, 0], [1, 2, 0], [1, 3, 0], [1, 4, 0], [1, 5, 1], [1, 6, 3]
          // 更多数据...
        ],
        "emphasis": {
          "itemStyle": {
            "shadowBlur": 10,
            "shadowColor": "rgba(0, 0, 0, 0.5)"
          }
        }
      }
    ],
    "visualMap": {
      "min": 0,
      "max": 10,
      "calculable": true,
      "orient": "horizontal",
      "left": "center",
      "bottom": "5%"
    }
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 4.2 模型性能对比雷达图接口

**接口**: `/api/dashboard/radar/model-performance`  
**方法**: GET  
**说明**: 获取AI模型性能对比雷达图数据  
**参数**: `models`(模型ID列表)  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "AI模型性能对比",
    "radar": {
      "indicator": [
        {"name": "准确率", "max": 100},
        {"name": "处理速度", "max": 100},
        {"name": "稳定性", "max": 100},
        {"name": "成本效益", "max": 100},
        {"name": "易用性", "max": 100}
      ]
    },
    "series": [
      {
        "type": "radar",
        "data": [
          {
            "name": "Qwen3-32B",
            "value": [95, 85, 92, 78, 88],
            "areaStyle": {
              "color": "rgba(37, 99, 235, 0.2)"
            },
            "lineStyle": {
              "color": "#2563eb"
            }
          },
          {
            "name": "GPT-4",
            "value": [98, 75, 95, 65, 92],
            "areaStyle": {
              "color": "rgba(16, 185, 129, 0.2)"
            },
            "lineStyle": {
              "color": "#10b981"
            }
          }
        ]
      }
    ]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

### 4.3 准确率分级分布图接口

**接口**: `/api/dashboard/stacked/accuracy-levels`  
**方法**: GET  
**说明**: 获取准确率分级分布图数据  
**参数**: 通用参数  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "title": "准确率分级分布",
    "xAxis": {
      "type": "category",
      "data": ["期货开户", "券商计息", "非标交易", "理财产品"]
    },
    "yAxis": {
      "type": "value",
      "name": "文件数量"
    },
    "series": [
      {
        "name": "优秀(95%+)",
        "type": "bar",
        "stack": "total",
        "data": [320, 180, 150, 80],
        "itemStyle": {"color": "#10b981"}
      },
      {
        "name": "良好(90-95%)",
        "type": "bar",
        "stack": "total",
        "data": [120, 150, 80, 70],
        "itemStyle": {"color": "#f59e0b"}
      },
      {
        "name": "一般(80-90%)",
        "type": "bar",
        "stack": "total",
        "data": [50, 80, 60, 40],
        "itemStyle": {"color": "#ef4444"}
      },
      {
        "name": "较差(<80%)",
        "type": "bar",
        "stack": "total",
        "data": [10, 20, 30, 20],
        "itemStyle": {"color": "#6b7280"}
      }
    ],
    "legend": ["优秀(95%+)", "良好(90-95%)", "一般(80-90%)", "较差(<80%)"]
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

## 5. 数据聚合接口

### 5.1 获取完整仪表盘数据

**接口**: `/api/dashboard/all`  
**方法**: GET  
**说明**: 一次性获取所有仪表盘数据（适用于首次加载）  
**参数**: 通用参数  
**返回**: 包含所有基础图表数据的聚合响应

### 5.2 获取仪表盘配置

**接口**: `/api/dashboard/config`  
**方法**: GET  
**说明**: 获取仪表盘布局和配置信息  
**参数**: 无  
**返回示例**:

```json
{
  "success": true,
  "data": {
    "layout": [
      {"id": "kpi-cards", "type": "kpi-cards", "title": "关键指标", "width": 12, "height": 1, "refresh_interval": 300},
      {"id": "processing-volume", "type": "line-chart", "title": "处理量趋势", "width": 6, "height": 2, "refresh_interval": 600},
      {"id": "accuracy-rate", "type": "line-chart", "title": "准确率趋势", "width": 6, "height": 2, "refresh_interval": 600},
      // 更多布局配置...
    ],
    "theme": "light",
    "auto_refresh": true,
    "default_period": "day",
    "user_preferences": {
      "favorite_charts": ["processing-volume", "accuracy-rate"],
      "hidden_charts": []
    }
  },
  "timestamp": "2024-12-18T10:30:00Z"
}
```

## 6. 接口实现建议

### 6.1 数据缓存策略

- 使用Redis缓存仪表盘数据，减轻数据库压力
- 为不同类型的图表设置不同的缓存过期时间
- 支持强制刷新缓存的参数

### 6.2 数据聚合查询

```sql
-- 示例：获取业务类型分布数据的SQL
SELECT 
    analysis_type,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM analysis_records), 1) as percentage
FROM 
    analysis_records
WHERE 
    created_at BETWEEN :start_date AND :end_date
    AND file_status = 'active'
GROUP BY 
    analysis_type
ORDER BY 
    count DESC;
```

### 6.3 性能优化建议

- 使用数据库索引优化查询性能
- 实现数据预计算和定时统计任务
- 对大量数据进行分页或分批处理
- 使用WebSocket实现实时数据推送

---

> 本文档详细描述了多场景智能化文档分析系统仪表盘所需的API接口设计，包括15个不同类型的图表数据接口。这些接口将为首页仪表盘提供全面的数据支持，确保用户能够获得系统的完整运行状况视图。

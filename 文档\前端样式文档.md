# 多场景智能化文档分析系统前端样式设计规范

## 一、系统整体设计分析

### 1.1 设计理念
- **商务专业风格**：面向银行、券商等金融机构的企业级应用
- **现代化界面**：采用卡片式布局、渐变背景、圆角设计
- **用户体验优先**：响应式设计、平滑动画、直观交互

### 1.2 色彩体系
| 用途分类 | 主要色值 | 应用场景 | 设计说明 |
|---------|---------|---------|---------|
| **主色调** | `#2563eb` | 导航、按钮、链接 | 商务蓝，体现专业可信 |
| **深色调** | `#1e3a8a` | 表头、强调元素 | 深蓝色，增强层次感 |
| **辅助色** | `#1d4ed8` | 悬停状态 | 蓝色变体，交互反馈 |
| **背景色** | `#f8f9fa` | 页面背景 | 浅灰色，护眼舒适 |
| **卡片背景** | `#ffffff` | 内容区域 | 纯白色，内容突出 |
| **边框色** | `#e5e7eb` | 分割线、边框 | 浅灰色，层次分明 |
| **文字色** | `#1f2937` | 主要文本 | 深灰色，阅读清晰 |
| **次要文字** | `#4b5563` | 辅助信息 | 中灰色，信息层级 |

### 1.3 布局架构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏 (64px)                      │
├─────────────────────────────────────────────────────────┤
│  侧边栏    │                主内容区                    │
│  (250px)   │         ┌─────────────────────┐           │
│           │         │      卡片容器        │           │
│  ┌─────┐  │         │  ┌───────────────┐   │           │
│  │菜单1│  │         │  │   功能模块    │   │           │
│  │菜单2│  │         │  └───────────────┘   │           │
│  │菜单3│  │         └─────────────────────┘           │
│  └─────┘  │                                           │
└─────────────────────────────────────────────────────────┘
```

## 二、核心组件样式规范

### 2.1 顶部导航栏设计

```css
.top-navbar {
  width: 100%;
  height: 64px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 48px;
  position: sticky;
  top: 0;
  z-index: 10;
  color: #2563eb;
}

.navbar-logo {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  background: linear-gradient(135deg, #2563eb 60%, #FFD700 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.7em;
  font-weight: bold;
  color: #fff;
  margin-right: 16px;
  box-shadow: 0 2px 8px rgba(37,99,235,0.10);
}

.navbar-title {
  font-size: 1.6em;
  font-weight: 700;
  letter-spacing: 2px;
  color: #2563eb;
}
```

### 2.2 侧边栏导航设计
```css
.sidebar {
  width: 250px;
  background: linear-gradient(180deg, #f8f9fa 0%, #e5e7eb 100%);
  color: #2563eb;
  flex-shrink: 0;
  border-right: 1px solid #e5e7eb;
  border-radius: 20px 0 0 20px;
  position: sticky;
  top: 64px;
  height: calc(100vh - 64px);
  overflow-y: auto;
  z-index: 5;
  box-shadow: inset -1px 0 3px rgba(0,0,0,0.05);
}

.menu-link {
  display: block;
  padding: 15px 25px;
  color: #2563eb;
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  cursor: pointer;
  background: none;
}

.menu-link:hover {
  background: #e5e7eb;
  border-left-color: #2563eb;
  color: #1e3a8a;
}

.menu-link.active {
  background: #e0e7ef;
  border-left-color: #2563eb;
  color: #1e3a8a;
}
```

### 2.3 按钮组件系统

```css
/* 主要按钮 - 商务蓝 */
.btn {
  background: #2563eb;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 22px;
  font-size: 1em;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(10,31,68,0.04);
  cursor: pointer;
  transition: background 0.25s, box-shadow 0.18s, transform 0.12s;
  outline: none;
  margin-right: 8px;
}

.btn:hover, .btn:focus {
  background: #1d4ed8;
  color: #fff;
  box-shadow: 0 4px 16px rgba(24,144,255,0.10);
  transform: scale(1.06);
}

/* 次要按钮 - 浅色 */
.btn-secondary {
  background: #f3f4f6;
  color: #2563eb;
  border: 1px solid #e5e7eb;
}

.btn-secondary:hover {
  background: #e0e7ef;
  color: #1e3a8a;
}

/* 成功按钮 - 绿色 */
.btn-success {
  background: #52c41a;
  color: #fff;
}

.btn-success:hover {
  background: #1890ff;
  color: #fff;
}

/* 信息按钮 - 青色 */
.btn-info {
  background: #17a2b8;
  color: #fff;
}

.btn-info:hover {
  background: #138496;
  color: #fff;
}

/* 禁用状态 */
.btn:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}
```

### 2.4 卡片容器设计
```css
.card {
  background: #fff;
  border-radius: 18px;
  box-shadow: 0 4px 32px 0 rgba(10,31,68,0.06);
  border: 1.5px solid #e5e7eb;
  padding: 28px 32px;
  margin-bottom: 0;
  position: relative;
  transition: box-shadow 0.2s;
  color: #1f2937;
}

.card:hover {
  box-shadow: 0 8px 40px 0 rgba(24,144,255,0.10);
}
```

### 2.5 文件上传区域
```css
.upload-area {
  border: 3px dashed #2563eb;
  border-radius: 15px;
  padding: 60px 20px;
  background: #fff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover, .upload-area.dragover {
  border-color: #1d4ed8;
  background: #e0e7ef;
  transform: scale(1.02);
}

.upload-icon {
  font-size: 4em;
  color: #2563eb;
  margin-bottom: 20px;
}
```

## 三、数据展示组件

### 3.1 表格样式系统

```css
/* 用户管理表格 */
table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

thead tr {
  background: #1e3a8a;
  color: #fff;
}

th, td {
  padding: 12px;
  text-align: left;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

tbody tr:nth-child(even) {
  background: #f9fafb;
}

tbody tr:hover {
  background: #eff6ff;
}
```

### 3.2 分析记录表格
```css
.record-data-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
}

.record-data-row {
  display: flex;
  padding: 14px 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.2s ease;
}

.record-data-row:hover {
  background: #f8f9fa;
}

.record-data-field {
  flex: 0 0 120px;
  font-weight: 600;
  color: #4b5563;
  padding-right: 16px;
  min-width: 120px;
}

.record-data-value {
  flex: 1;
  color: #1f2937;
  word-break: break-word;
}
```

### 3.3 对比结果展示
```css
.comparison-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e5e7eb;
}

.comparison-stats {
  display: flex;
  gap: 20px;
  justify-content: space-around;
  align-items: center;
}

.comparison-stat {
  text-align: center;
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2563eb;
}

.stat-label {
  font-size: 14px;
  color: #4b5563;
  margin-top: 4px;
}
```

### 3.4 对比表格样式
```css
.compare-table {
  margin: 0 32px 0 32px;
  border-radius: 8px;
  background: #fafbff;
  max-height: 340px;
  overflow-y: auto;
  margin-bottom: 18px;
}

.compare-row {
  display: flex;
  padding: 14px 16px;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.2s ease;
}

.compare-row:hover {
  background: #f8f9fa;
}

.compare-field {
  flex: 0 0 120px;
  font-weight: 600;
  color: #4b5563;
  padding-right: 16px;
  min-width: 120px;
}

.comparison-ai-value {
  flex: 1;
  padding: 8px 12px;
  margin-right: 8px;
  background: #dbeafe;
  border-radius: 6px;
  color: #1e40af;
  font-weight: 500;
  word-break: break-word;
}

.comparison-system-value {
  flex: 1;
  padding: 8px 12px;
  background: #e0f2fe;
  border-radius: 6px;
  color: #0369a1;
  font-weight: 500;
  word-break: break-word;
}
```

## 四、状态与反馈组件

### 4.1 状态标签设计

```css
/* 状态标签基础样式 */
.status-tag {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

/* 待处理状态 - 橙色 */
.status-pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #f59e0b;
}

/* 成功状态 - 绿色 */
.status-approved, .status-success {
  background: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

/* 失败状态 - 红色 */
.status-rejected, .status-error {
  background: #fee2e2;
  color: #991b1b;
  border: 1px solid #ef4444;
}

/* 处理中状态 - 蓝色 */
.status-processing {
  background: #dbeafe;
  color: #1e40af;
  border: 1px solid #3b82f6;
}
```

### 4.2 加载与进度指示
```css
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #4b5563;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #2563eb;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
  transition: width 0.3s ease;
}
```

## 五、弹窗与模态框

### 5.1 模态框基础样式
```css
.modal {
  position: fixed;
  z-index: 1001;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.modal-content {
  background: #fff;
  color: #1f2937;
  border-radius: 16px;
  padding: 0;
  min-width: 320px;
  max-width: 600px;
  width: 90vw;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  position: relative;
  margin: 0 auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e8e8e8;
}

.close {
  position: absolute;
  right: 18px;
  top: 12px;
  font-size: 1.5em;
  color: #2563eb;
  cursor: pointer;
}

.close:hover {
  transform: scale(1.1);
  color: #1d4ed8;
}
```

### 5.2 特殊弹窗样式
```css
/* 挡板数据管理弹窗 */
#mockDataModal .modal-content {
  width: 650px;
  max-width: 90vw;
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  color: #1f2937;
  border: 1.5px solid #e5e7eb;
  border-radius: 18px;
  box-shadow: 0 12px 48px rgba(37,99,235,0.13);
  padding: 24px 20px 18px 20px;
}

/* 登录注册模态框 */
.auth-modal .modal-content {
  max-width: 400px;
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
}
```

## 六、响应式设计规范

### 6.1 断点设置
```css
/* 移动设备 */
@media (max-width: 768px) {
  .container {
    max-width: 100vw;
    margin: 16px 0 0 0;
    border-radius: 0;
  }

  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    border-radius: 0;
  }

  .content-area {
    padding: 20px 16px;
  }

  .comparison-row {
    flex-direction: column;
    gap: 8px;
  }

  .modal-content {
    width: 95vw;
    max-width: 95vw;
    margin: 0 auto;
  }
}

/* 平板设备 */
@media (max-width: 1200px) {
  .container {
    max-width: 100vw;
    margin: 16px 0 0 0;
    border-radius: 12px;
  }

  .navbar-title {
    font-size: 1.4em;
  }

  .sidebar {
    width: 220px;
  }
}
```

### 6.2 字体响应式
```css
/* 基础字体设置 */
body {
  font-family: 'Inter', 'Roboto', 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
}

/* 标题层级 */
h1 { font-size: 2.5em; font-weight: 700; color: #2563eb; }
h2 { font-size: 2em; font-weight: 600; color: #1f2937; }
h3 { font-size: 1.5em; font-weight: 600; color: #1f2937; }
h4 { font-size: 1.25em; font-weight: 500; color: #374151; }

/* 移动端字体调整 */
@media (max-width: 768px) {
  body { font-size: 14px; }
  h1 { font-size: 2em; }
  h2 { font-size: 1.75em; }
  h3 { font-size: 1.25em; }
}
```

## 七、动画与交互效果

### 7.1 过渡动画
```css
/* 全局过渡效果 */
* {
  transition: all 0.2s ease;
}

/* 按钮悬停效果 */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.15);
}

/* 卡片悬停效果 */
.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 48px rgba(37,99,235,0.15);
}

/* 菜单项动画 */
.menu-link {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-link:hover {
  transform: translateX(4px);
}
```

### 7.2 滚动条美化
```css
/* 主滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 对比表格滚动条 */
.compare-table::-webkit-scrollbar {
  width: 8px;
  background: #f3eaff;
  border-radius: 8px;
}

.compare-table::-webkit-scrollbar-thumb {
  background: #a084ee55;
  border-radius: 8px;
}
```

## 八、系统风格统一性约束规范

### 8.1 强制性设计约束 ⚠️

#### 8.1.1 色彩使用限制
```css
/* 🚫 禁止使用的颜色 - 违反商务风格 */
/* 禁止：鲜艳色彩 */
.forbidden-colors {
  /* color: #ff0000; */  /* 禁止纯红色 */
  /* color: #00ff00; */  /* 禁止纯绿色 */
  /* color: #ff00ff; */  /* 禁止紫红色 */
  /* color: #ffff00; */  /* 禁止纯黄色 */
  /* background: linear-gradient(rainbow); */ /* 禁止彩虹渐变 */
}

/* ✅ 强制使用的标准色彩 */
:root {
  /* 主色调 - 必须使用 */
  --primary-blue: #2563eb;
  --primary-dark: #1e3a8a;
  --primary-light: #dbeafe;

  /* 辅助色 - 限定使用 */
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;
  --info-cyan: #06b6d4;

  /* 中性色 - 标准灰度 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* 背景色 - 固定使用 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
}

/* 🔒 强制约束：所有颜色必须使用CSS变量 */
* {
  /* 禁止直接使用十六进制颜色值 */
  /* color: #123456; ❌ 错误 */
  /* color: var(--gray-800); ✅ 正确 */
}
```

#### 8.1.2 字体使用限制
```css
/* 🚫 禁止的字体 */
.forbidden-fonts {
  /* font-family: "Comic Sans MS"; */     /* 禁止卡通字体 */
  /* font-family: "Papyrus"; */           /* 禁止装饰字体 */
  /* font-family: "Impact"; */            /* 禁止冲击字体 */
  /* font-family: "Brush Script MT"; */   /* 禁止手写字体 */
}

/* ✅ 强制标准字体栈 */
:root {
  --font-primary: 'Inter', 'Roboto', 'Segoe UI', 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'Fira Code', 'Monaco', 'Consolas', 'Courier New', monospace;
}

/* 🔒 字体大小限制 */
:root {
  --text-xs: 0.75rem;    /* 12px - 最小字体 */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px - 基础字体 */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px - 最大字体 */
}

/* 禁止使用超出范围的字体大小 */
.size-constraints {
  /* font-size: 8px; ❌ 太小，影响可读性 */
  /* font-size: 48px; ❌ 太大，破坏布局 */
}
```

#### 8.1.3 间距使用限制
```css
/* 🔒 标准间距系统 - 8px基准 */
:root {
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px - 基础单位 */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
}

/* 🚫 禁止使用非标准间距 */
.spacing-violations {
  /* margin: 13px; ❌ 不符合8px网格 */
  /* padding: 27px; ❌ 不符合8px网格 */
  /* gap: 15px; ❌ 不符合8px网格 */
}

/* ✅ 正确的间距使用 */
.correct-spacing {
  margin: var(--space-4);     /* ✅ 16px */
  padding: var(--space-6);    /* ✅ 24px */
  gap: var(--space-8);        /* ✅ 32px */
}
```

### 8.2 组件设计约束

#### 8.2.1 按钮设计限制
```css
/* 🔒 按钮尺寸标准 */
:root {
  --btn-height-sm: 32px;    /* 小按钮 */
  --btn-height-md: 40px;    /* 标准按钮 */
  --btn-height-lg: 48px;    /* 大按钮 */

  --btn-radius: 8px;        /* 统一圆角 */
  --btn-padding-x: 16px;    /* 水平内边距 */
}

/* 🚫 禁止的按钮样式 */
.forbidden-button-styles {
  /* border-radius: 50%; ❌ 禁止圆形按钮 */
  /* border-radius: 0; ❌ 禁止直角按钮 */
  /* height: 60px; ❌ 超出尺寸限制 */
  /* background: linear-gradient(45deg, red, blue); ❌ 禁止彩色渐变 */
}

/* ✅ 强制按钮规范 */
.btn-base {
  height: var(--btn-height-md);
  border-radius: var(--btn-radius);
  padding: 0 var(--btn-padding-x);
  font-weight: 500;
  font-size: var(--text-base);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}
```

#### 8.2.2 卡片设计限制
```css
/* 🔒 卡片规范 */
:root {
  --card-radius: 16px;      /* 统一圆角 */
  --card-padding: 24px;     /* 统一内边距 */
  --card-shadow: 0 4px 32px rgba(10,31,68,0.06); /* 统一阴影 */
  --card-border: 1px solid var(--gray-200); /* 统一边框 */
}

/* 🚫 禁止的卡片样式 */
.forbidden-card-styles {
  /* border-radius: 0; ❌ 禁止直角卡片 */
  /* border-radius: 50px; ❌ 禁止过度圆角 */
  /* box-shadow: 0 0 50px red; ❌ 禁止彩色阴影 */
  /* padding: 5px; ❌ 内边距过小 */
}

/* ✅ 强制卡片规范 */
.card-base {
  background: var(--bg-primary);
  border-radius: var(--card-radius);
  padding: var(--card-padding);
  box-shadow: var(--card-shadow);
  border: var(--card-border);
}
```

### 8.3 布局约束规范

#### 8.3.1 网格系统限制
```css
/* 🔒 标准网格系统 */
:root {
  --container-max-width: 1400px;
  --sidebar-width: 250px;
  --navbar-height: 64px;
  --content-padding: 32px;
}

/* 🚫 禁止的布局方式 */
.forbidden-layouts {
  /* position: absolute; ❌ 避免绝对定位 */
  /* float: left; ❌ 禁止浮动布局 */
  /* display: table; ❌ 禁止表格布局 */
}

/* ✅ 推荐的布局方式 */
.recommended-layouts {
  display: flex;           /* ✅ 弹性布局 */
  display: grid;           /* ✅ 网格布局 */
  display: block;          /* ✅ 块级布局 */
}
```

#### 8.3.2 响应式断点限制
```css
/* 🔒 标准断点系统 */
:root {
  --breakpoint-sm: 640px;   /* 小屏设备 */
  --breakpoint-md: 768px;   /* 平板设备 */
  --breakpoint-lg: 1024px;  /* 笔记本 */
  --breakpoint-xl: 1280px;  /* 桌面 */
  --breakpoint-2xl: 1536px; /* 大屏 */
}

/* 🚫 禁止使用非标准断点 */
@media (max-width: 500px) { /* ❌ 非标准断点 */ }
@media (max-width: 900px) { /* ❌ 非标准断点 */ }

/* ✅ 使用标准断点 */
@media (max-width: 768px) { /* ✅ 标准断点 */ }
@media (max-width: 1024px) { /* ✅ 标准断点 */ }
```

### 8.4 代码质量约束

#### 8.4.1 CSS 编写规范
```css
/* 🔒 强制性编写规范 */

/* ✅ 正确的选择器命名 */
.navbar-container { }           /* kebab-case */
.btn-primary { }               /* 语义化命名 */
.modal-content { }             /* 组件-元素 */

/* 🚫 禁止的选择器命名 */
.redButton { }                 /* ❌ 驼峰命名 */
.button1 { }                   /* ❌ 数字命名 */
.my_button { }                 /* ❌ 下划线命名 */
.BUTTON { }                    /* ❌ 全大写命名 */

/* ✅ 正确的属性顺序 */
.component {
  /* 1. 定位 */
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;

  /* 2. 盒模型 */
  display: flex;
  width: 100%;
  height: auto;
  margin: 0;
  padding: var(--space-4);
  border: var(--card-border);

  /* 3. 视觉 */
  background: var(--bg-primary);
  color: var(--gray-800);
  font-size: var(--text-base);

  /* 4. 其他 */
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 🚫 禁止的CSS写法 */
.bad-example {
  /* color: red !important; ❌ 禁止!important */
  /* margin: 10px 5px 15px 20px; ❌ 不规范间距 */
  /* font-size: 13.5px; ❌ 非标准字体大小 */
}
```

#### 8.4.2 HTML 结构约束
```html
<!-- ✅ 正确的HTML结构 -->
<div class="card">
  <div class="card-header">
    <h3 class="card-title">标题</h3>
  </div>
  <div class="card-body">
    <p class="card-text">内容</p>
  </div>
  <div class="card-footer">
    <button class="btn btn-primary">操作</button>
  </div>
</div>

<!-- 🚫 禁止的HTML结构 -->
<div class="redBox">           <!-- ❌ 非语义化命名 -->
  <span class="title">         <!-- ❌ 错误的标签使用 -->
    <div>标题</div>            <!-- ❌ 嵌套错误 -->
  </span>
</div>
```

### 8.5 开发流程约束

#### 8.5.1 代码审查检查清单
```markdown
## 🔍 强制性检查项目

### 色彩使用检查
- [ ] 所有颜色都使用CSS变量
- [ ] 没有使用禁止的鲜艳色彩
- [ ] 色彩对比度符合WCAG 2.1 AA标准（4.5:1）

### 字体使用检查
- [ ] 使用标准字体栈
- [ ] 字体大小在允许范围内（12px-36px）
- [ ] 行高设置合理（1.4-1.8）

### 间距使用检查
- [ ] 所有间距符合8px网格系统
- [ ] 使用标准间距变量
- [ ] 没有使用奇数像素值

### 组件规范检查
- [ ] 按钮高度符合标准（32px/40px/48px）
- [ ] 卡片圆角统一（16px）
- [ ] 阴影效果统一

### 响应式检查
- [ ] 使用标准断点
- [ ] 移动端适配完整
- [ ] 触摸目标大小≥44px

### 代码质量检查
- [ ] CSS属性顺序正确
- [ ] 选择器命名规范
- [ ] 没有使用!important
- [ ] HTML结构语义化
```

#### 8.5.2 自动化检查工具配置
```json
// .stylelintrc.json - CSS代码检查配置
{
  "extends": ["stylelint-config-standard"],
  "rules": {
    "color-hex-length": "long",
    "color-no-invalid-hex": true,
    "font-family-name-quotes": "always-where-recommended",
    "font-size-no-duplicate-values": true,
    "length-zero-no-unit": true,
    "max-nesting-depth": 3,
    "no-duplicate-selectors": true,
    "no-empty-source": true,
    "property-no-unknown": true,
    "selector-class-pattern": "^[a-z][a-z0-9]*(-[a-z0-9]+)*$",
    "unit-whitelist": ["px", "rem", "em", "%", "vh", "vw", "s", "ms"]
  }
}
```

### 8.6 违规处理机制

#### 8.6.1 违规等级定义
```css
/* 🚨 严重违规 - 必须立即修复 */
.critical-violations {
  /* 使用禁止颜色 */
  /* 破坏响应式布局 */
  /* 影响可访问性 */
}

/* ⚠️ 一般违规 - 下次迭代修复 */
.minor-violations {
  /* 间距不符合网格 */
  /* 命名不规范 */
  /* 代码重复 */
}

/* 💡 建议优化 - 有时间时优化 */
.optimization-suggestions {
  /* 性能优化 */
  /* 代码简化 */
  /* 可维护性提升 */
}
```

#### 8.6.2 修复指南
```markdown
## 🛠️ 常见违规修复指南

### 颜色违规修复
❌ 错误：`color: #ff0000;`
✅ 修复：`color: var(--error-red);`

### 间距违规修复
❌ 错误：`margin: 13px;`
✅ 修复：`margin: var(--space-3); /* 12px */`

### 字体违规修复
❌ 错误：`font-size: 13.5px;`
✅ 修复：`font-size: var(--text-sm); /* 14px */`

### 命名违规修复
❌ 错误：`.redButton`
✅ 修复：`.btn-danger`

### 布局违规修复
❌ 错误：`position: absolute; top: 50px;`
✅ 修复：`display: flex; align-items: center;`
```

---

## 九、系统风格统一性保障

### 9.1 设计令牌系统
```css
/* 🎨 设计令牌 - 系统级变量定义 */
:root {
  /* 品牌色彩 */
  --brand-primary: #2563eb;
  --brand-secondary: #1e3a8a;
  --brand-accent: #dbeafe;

  /* 功能色彩 */
  --semantic-success: #10b981;
  --semantic-warning: #f59e0b;
  --semantic-error: #ef4444;
  --semantic-info: #06b6d4;

  /* 中性色彩 */
  --neutral-white: #ffffff;
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;

  /* 字体系统 */
  --font-family-primary: 'Inter', 'Roboto', 'Segoe UI', 'Microsoft YaHei', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;

  /* 字体大小 */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  /* 字体粗细 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距系统 */
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-base: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-full: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.25);

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}
```

### 9.2 强制性使用规范
```css
/* 🔒 所有组件必须继承基础样式 */
.component-base {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--neutral-800);
  transition: var(--transition-base);
}

/* 🔒 所有交互元素必须符合可访问性 */
.interactive-base {
  min-height: 44px;  /* 最小触摸目标 */
  min-width: 44px;
  cursor: pointer;
  outline: none;
}

.interactive-base:focus-visible {
  outline: 2px solid var(--brand-primary);
  outline-offset: 2px;
}

/* 🔒 所有文本必须符合对比度要求 */
.text-contrast-check {
  /* 正常文本：4.5:1 对比度 */
  color: var(--neutral-800);  /* ✅ 通过 */
  /* color: var(--neutral-400); ❌ 不通过 */

  /* 大文本（18px+）：3:1 对比度 */
  font-size: var(--font-size-lg);
  color: var(--neutral-600);  /* ✅ 通过 */
}
```

---

## 总结

本文档基于实际代码分析，提供了完整的前端样式设计规范和**严格的统一性约束**。涵盖了：

### 📋 核心内容
1. **设计理念**：商务专业、现代化、用户体验优先
2. **色彩体系**：以商务蓝为主色调的完整配色方案
3. **组件规范**：按钮、表格、卡片、弹窗等核心组件
4. **响应式设计**：适配多种设备的布局方案
5. **交互效果**：平滑动画和用户反馈

### 🔒 新增约束机制
6. **强制性设计约束**：色彩、字体、间距使用限制
7. **组件设计约束**：按钮、卡片、布局标准化
8. **代码质量约束**：CSS编写规范、HTML结构要求
9. **开发流程约束**：代码审查清单、自动化检查
10. **违规处理机制**：违规等级定义、修复指南
11. **设计令牌系统**：系统级变量定义、强制使用规范

### 🎯 统一性保障
- **CSS变量强制使用**：所有样式值必须使用预定义变量
- **设计令牌系统**：确保整个系统视觉一致性
- **自动化检查**：通过工具确保代码规范
- **分级违规处理**：明确修复优先级和方法

> 本规范体现了金融行业所需的稳重、专业、可信赖的商务风格，通过严格的约束机制确保整个系统风格的高度统一性。
# 多场景智能化文档分析系统功能实现计划

## 📋 功能实现优先级分级

### 🔴 P0 - 核心功能（立即实现）
1. **首页仪表盘** - 提升用户体验的关键功能
2. **文件状态管理** - 废弃/恢复按钮替换删除功能
3. **管理员复核功能** - 从人工审核升级为管理员复核
4. **系统风格统一** - 确保整体视觉一致性

### 🟡 P1 - 重要功能（近期实现）
5. **提示词版本管理** - 支持版本控制和切换
6. **文件打标系统** - 便于文件分类和管理
7. **PDF原件展示** - 提升分析结果查看体验
8. **挡板数据编辑** - 特定场景的标准答案管理

### 🟢 P2 - 优化功能（后期实现）
9. **文件去重检查** - 避免重复处理
10. **重新分析功能** - 可选的重新处理能力
11. **多Sheet Excel导出** - 导出功能增强
12. **大模型配置管理** - 预留配置界面

---

## 🚀 详细实现计划

### 1. 首页仪表盘实现 (P0)

#### 1.1 后端实现
```python
# 新增仪表盘统计服务
class DashboardService:
    def get_dashboard_stats(self):
        """获取仪表盘统计数据"""
        return {
            'total_files': self.get_total_files(),
            'accuracy_rate': self.calculate_accuracy_rate(),
            'today_processed': self.get_today_processed(),
            'type_stats': self.get_type_statistics(),
            'recent_trend': self.get_recent_trend()
        }
    
    def calculate_accuracy_rate(self):
        """计算识别准确率"""
        # 基于对比结果计算准确率
        pass
```

#### 1.2 数据库变更
```sql
-- 创建仪表盘统计表
CREATE TABLE dashboard_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE NOT NULL,
    analysis_type VARCHAR(50),
    total_files INT DEFAULT 0,
    processed_files INT DEFAULT 0,
    accuracy_rate DECIMAL(5,4) DEFAULT 0,
    avg_processing_time DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 1.3 前端实现
- 创建仪表盘组件
- 集成图表库（Chart.js或ECharts）
- 实现实时数据刷新
- 响应式布局适配

#### 1.4 API接口
- `GET /api/dashboard/stats` - 获取统计数据
- `GET /api/dashboard/accuracy` - 获取准确率详情

### 2. 文件状态管理实现 (P0)

#### 2.1 数据库变更
```sql
-- 扩展analysis_records表
ALTER TABLE analysis_records 
ADD COLUMN file_status ENUM('active', 'deprecated', 'archived') DEFAULT 'active',
ADD COLUMN status_changed_by INT,
ADD COLUMN status_changed_at TIMESTAMP NULL,
ADD COLUMN status_reason TEXT;
```

#### 2.2 后端实现
```python
@app.route('/api/files/<int:file_id>/status', methods=['PUT'])
@login_required
def update_file_status(file_id):
    """更新文件状态"""
    data = request.get_json()
    status = data.get('status')  # 'deprecated' or 'active'
    reason = data.get('reason', '')
    
    # 更新数据库记录
    # 记录操作日志
    # 返回更新结果
```

#### 2.3 前端实现
- 替换删除按钮为废弃按钮
- 废弃状态下显示恢复按钮
- 醒目的按钮样式设计
- 状态变更确认对话框

### 3. 管理员复核功能实现 (P0)

#### 3.1 数据库变更
```sql
-- 创建复核记录表
CREATE TABLE review_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    review_status ENUM('approved', 'rejected', 'needs_revision') NOT NULL,
    review_comment TEXT,
    corrections JSON,
    review_time DECIMAL(8,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 更新analysis_records表
ALTER TABLE analysis_records 
ADD COLUMN review_status ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending',
ADD COLUMN review_priority ENUM('low', 'normal', 'high') DEFAULT 'normal';
```

#### 3.2 后端实现
```python
class ReviewService:
    def get_pending_reviews(self, limit=10):
        """获取待复核文件列表"""
        pass
    
    def submit_review(self, record_id, status, comment, corrections=None):
        """提交复核结果"""
        pass
    
    def auto_approve_all(self, criteria):
        """一键全部自动复核"""
        pass
    
    def get_next_review(self, current_record_id):
        """获取下一个待复核文件"""
        pass
```

#### 3.3 前端实现
- 复核按钮与废弃按钮并列显示
- 复核完成后自动跳转下一个
- 一键全部复核功能
- 复核进度显示

### 4. 系统风格统一实现 (P0)

#### 4.1 CSS变量系统
```css
:root {
  /* 品牌色彩 */
  --brand-primary: #2563eb;
  --brand-secondary: #1e3a8a;
  
  /* 功能色彩 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}
```

#### 4.2 组件标准化
- 统一按钮样式和尺寸
- 标准化表格、卡片、弹窗组件
- 统一的颜色和字体规范
- 响应式布局标准

#### 4.3 Logo替换功能
```python
@app.route('/api/system/logo', methods=['POST'])
@admin_required
def upload_logo():
    """上传公司Logo"""
    # 处理文件上传
    # 更新系统配置
    # 返回上传结果
```

### 5. 提示词版本管理实现 (P1)

#### 5.1 数据库设计
```sql
CREATE TABLE prompt_versions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    prompt_content TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INT DEFAULT 0
);
```

#### 5.2 版本管理服务
```python
class PromptVersionService:
    def create_version(self, analysis_type, content, description):
        """创建新版本"""
        pass
    
    def switch_version(self, analysis_type, version):
        """切换版本"""
        pass
    
    def get_version_history(self, analysis_type):
        """获取版本历史"""
        pass
    
    def compare_versions(self, version1, version2):
        """版本对比"""
        pass
```

### 6. 文件打标系统实现 (P1)

#### 6.1 数据库设计
```sql
CREATE TABLE file_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_id INT NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    tag_color VARCHAR(7) DEFAULT '#2563eb',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 6.2 打标功能实现
- 标签创建和管理
- 颜色选择器
- 批量打标功能
- 标签筛选和搜索

### 7. PDF原件展示实现 (P1)

#### 7.1 PDF处理服务
```python
class PDFService:
    def generate_preview(self, file_path):
        """生成PDF预览"""
        # 使用pdf2image生成缩略图
        pass
    
    def get_pdf_info(self, file_path):
        """获取PDF信息"""
        # 页数、大小等信息
        pass
```

#### 7.2 前端PDF查看器
- 集成PDF.js或其他PDF查看器
- 页面导航和缩放功能
- 与分析结果并排显示

### 8. 挡板数据编辑实现 (P1)

#### 8.1 编辑界面设计
- JSON编辑器集成
- 语法高亮和格式化
- 实时预览功能
- 保存为标准答案

#### 8.2 标准答案管理
```sql
CREATE TABLE standard_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50) NOT NULL,
    file_pattern VARCHAR(255),
    standard_result JSON NOT NULL,
    confidence_score DECIMAL(5,4) DEFAULT 1.0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 📅 实施时间表

### 第一阶段（1-2周）- P0功能
- [ ] 首页仪表盘基础版本
- [ ] 文件状态管理（废弃/恢复）
- [ ] 管理员复核功能核心逻辑
- [ ] 系统风格统一（CSS变量系统）

### 第二阶段（2-3周）- P0功能完善
- [ ] 仪表盘图表和实时数据
- [ ] 复核功能完整流程
- [ ] Logo替换功能
- [ ] 样式统一性检查和修复

### 第三阶段（3-4周）- P1功能
- [ ] 提示词版本管理
- [ ] 文件打标系统
- [ ] PDF原件展示
- [ ] 挡板数据编辑（限定场景）

### 第四阶段（4-5周）- P2功能
- [ ] 文件去重检查
- [ ] 重新分析功能
- [ ] 多Sheet Excel导出
- [ ] 大模型配置管理界面

---

## 🧪 测试计划

### 单元测试
- 各服务类的核心方法测试
- 数据库操作测试
- API接口测试

### 集成测试
- 完整业务流程测试
- 用户权限测试
- 文件上传和分析流程测试

### 用户验收测试
- 界面易用性测试
- 功能完整性验证
- 性能和稳定性测试

---

## 📊 成功指标

### 功能指标
- [ ] 所有P0功能100%实现
- [ ] 系统响应时间<2秒
- [ ] 识别准确率统计准确性>99%
- [ ] 零数据丢失

### 用户体验指标
- [ ] 界面风格100%统一
- [ ] 移动端适配完成
- [ ] 用户操作流程优化
- [ ] 错误提示友好化

### 技术指标
- [ ] 代码覆盖率>80%
- [ ] 数据库查询优化
- [ ] 前端资源压缩
- [ ] 安全漏洞修复

---

> 本计划将根据实际开发进度和用户反馈进行动态调整，确保功能实现质量和用户体验。

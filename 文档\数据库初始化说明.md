# 数据库初始化脚本说明

## 概述
`init_db.sql` 是多场景智能化文档分析系统的完整数据库初始化脚本，包含所有必要的表结构、索引、约束、视图和初始数据。

## 表结构说明

### 1. 用户表 (users)
**功能**: 存储系统用户信息和权限控制
```sql
- id: 主键
- username: 用户名（唯一）
- password_hash: 密码哈希
- role: 角色 (user, analyst, admin)
- status: 状态 (active, inactive, suspended)
- last_login: 最后登录时间
- created_at: 创建时间
- created_by: 创建者ID（自引用外键）
```

### 2. 分析记录表 (analysis_records)
**功能**: 存储文件分析结果和审核状态
```sql
- id: 主键
- filename: 文件名
- analysis_type: 分析类型
- ai_result: AI识别结果(JSON)
- system_result: 系统查询结果(JSON)
- comparison_result: 对比结果(JSON)
- status: 处理状态 (pending, processing, completed, failed)
- audit_status: 审核状态 (pass, fail, pending) - 新增
- audit_comment: 审核备注 - 新增
- audited_by: 审核人ID - 新增
- audited_at: 审核时间 - 新增
- created_at: 创建时间
- updated_at: 更新时间
```

### 3. 挡板数据表 (mock_data)
**功能**: 存储模拟数据用于测试
```sql
- id: 主键
- query_key: 查询关键字
- query_type: 查询类型
- mock_result: 挡板返回结果(JSON)
- created_at: 创建时间
```

### 4. 客户系统模拟数据表 (customer_system_data)
**功能**: 存储客户系统模拟数据
```sql
- id: 主键
- query_key: 查询关键字
- query_type: 查询类型
- data_json: 客户系统返回内容(JSON)
- created_at: 创建时间
```

### 5. 用户活动日志表 (user_activities)
**功能**: 记录用户操作日志
```sql
- id: 主键
- user_id: 用户ID（外键）
- action: 操作类型
- resource: 操作资源
- details: 操作详情(JSON)
- ip_address: IP地址
- user_agent: 用户代理
- success: 是否成功
- error_message: 错误信息
- created_at: 创建时间
```

### 6. 提示词配置表 (prompt_config)
**功能**: 存储AI提示词配置
```sql
- id: 主键
- name: 提示词名称
- type: 细分类型
- main_type: 主要功能分类
- prompt: 提示词内容
- created_at: 创建时间
- updated_at: 更新时间
```

### 7. 系统配置表 (system_config) - 新增
**功能**: 存储系统配置参数
```sql
- id: 主键
- config_key: 配置键（唯一）
- config_value: 配置值
- config_type: 配置类型 (string, json, number, boolean)
- description: 配置描述
- created_at: 创建时间
- updated_at: 更新时间
```

### 8. 审核规则配置表 (audit_rules) - 新增
**功能**: 存储审核规则配置
```sql
- id: 主键
- rule_name: 规则名称
- analysis_type: 适用的分析类型
- similarity_threshold: 相似度阈值
- auto_approve: 是否自动通过
- rule_description: 规则描述
- is_active: 是否启用
- created_at: 创建时间
- updated_at: 更新时间
```

## 视图说明

### 1. 用户权限视图 (user_permissions_view)
**功能**: 提供用户权限信息
```sql
SELECT id, username, role, status, permissions
FROM user_permissions_view
WHERE status = 'active'
```

### 2. 审核统计视图 (audit_statistics_view) - 新增
**功能**: 提供审核统计信息
```sql
SELECT analysis_type, total_records, approved_records, 
       rejected_records, pending_records, approval_rate
FROM audit_statistics_view
```

## 索引优化

### 单列索引
- 用户表: username, role, status, created_at
- 分析记录表: status, audit_status, analysis_type, created_at, audited_at, filename
- 挡板数据表: query_key, query_type, created_at
- 客户系统数据表: query_key, query_type, created_at
- 用户活动表: user_id, action, created_at, success
- 提示词配置表: type, main_type, created_at
- 系统配置表: config_key, config_type
- 审核规则表: analysis_type, is_active

### 复合索引
- analysis_records(analysis_type, status)
- analysis_records(analysis_type, audit_status)
- analysis_records(created_at, audit_status)

## 触发器

### 自动更新时间触发器
- `update_analysis_records_updated_at`: 自动更新分析记录表的updated_at字段
- `update_prompt_config_updated_at`: 自动更新提示词配置表的updated_at字段
- `update_audit_rules_updated_at`: 自动更新审核规则表的updated_at字段

## 初始数据

### 默认管理员账户
```sql
用户名: admin
密码: admin123
角色: admin
状态: active
```

### 默认审核规则
- 期货账户分析: 相似度100%自动通过
- 理财产品说明书: 相似度100%自动通过
- 券商账户计息变更: 相似度100%自动通过
- 非标交易确认单: 相似度100%自动通过
- 宁银理财费用变更: 相似度100%自动通过
- 账户开户场景: 相似度100%自动通过

### 默认系统配置
- `auto_audit_enabled`: true (启用自动审核)
- `default_similarity_threshold`: 1.00 (默认相似度阈值)
- `audit_retention_days`: 365 (审核记录保留天数)
- `max_upload_file_size`: 10485760 (最大上传文件大小10MB)
- `allowed_file_types`: ["pdf", "jpg", "jpeg", "png"] (允许的文件类型)
- `system_name`: 多场景智能化文档分析系统
- `system_version`: 1.0.0

## 使用说明

### 1. 执行初始化脚本
```bash
mysql -u username -p < init_db.sql
```

### 2. 验证初始化结果
```sql
-- 检查表是否创建成功
SHOW TABLES;

-- 检查默认管理员账户
SELECT username, role, status FROM users WHERE username = 'admin';

-- 检查审核规则
SELECT rule_name, analysis_type, similarity_threshold FROM audit_rules;

-- 检查系统配置
SELECT config_key, config_value FROM system_config;
```

### 3. 权限配置
初始化后，系统包含以下角色权限：

**管理员 (admin)**
- 用户管理
- 系统设置
- 提示词管理
- 挡板数据管理
- 查看所有记录
- 人工审核
- 文件上传
- 文件分析
- 查看自己的记录
- 下载报告
- 审核管理

**分析师 (analyst)**
- 查看所有记录
- 人工审核
- 文件上传
- 文件分析
- 查看自己的记录
- 下载报告
- 审核管理

**普通用户 (user)**
- 文件上传
- 文件分析
- 查看自己的记录
- 下载报告

## 注意事项

1. **外键约束**: 确保在创建表时遵循正确的顺序，避免外键约束错误
2. **字符集**: 使用utf8mb4字符集，支持完整的Unicode字符
3. **索引优化**: 根据查询模式创建合适的索引，提高查询性能
4. **数据备份**: 在生产环境执行前，请备份现有数据
5. **权限设置**: 确保数据库用户具有创建表、视图、触发器的权限

## 扩展功能

### 1. 审核规则配置
可以通过修改 `audit_rules` 表来调整不同分析类型的审核规则：
```sql
-- 修改相似度阈值
UPDATE audit_rules 
SET similarity_threshold = 0.95 
WHERE analysis_type = 'future';

-- 禁用自动审核
UPDATE audit_rules 
SET auto_approve = FALSE 
WHERE analysis_type = 'financial';
```

### 2. 系统配置管理
可以通过修改 `system_config` 表来调整系统参数：
```sql
-- 修改最大文件大小
UPDATE system_config 
SET config_value = '20971520' 
WHERE config_key = 'max_upload_file_size';

-- 添加新的文件类型
UPDATE system_config 
SET config_value = '["pdf", "jpg", "jpeg", "png", "tiff"]' 
WHERE config_key = 'allowed_file_types';
```

### 3. 审核统计查询
可以使用审核统计视图来查看审核情况：
```sql
-- 查看各分析类型的审核统计
SELECT * FROM audit_statistics_view;

-- 查看特定类型的审核情况
SELECT * FROM audit_statistics_view WHERE analysis_type = 'future';
``` 
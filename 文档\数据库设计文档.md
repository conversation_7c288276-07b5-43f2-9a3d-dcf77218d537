# 多场景智能化文档分析系统 - 数据库设计文档

## 📋 概述

本文档详细描述了多场景智能化文档分析系统的完整数据库设计，包括所有数据表结构、字段定义、索引设计、关系约束等。

## 🗄️ 数据库基本信息

- **数据库类型**: MySQL 8.0+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **时区**: Asia/Shanghai

## 📊 数据表设计

### 1. 分析记录表 (analysis_records)

**用途**: 存储所有文档分析记录的核心信息

```sql
CREATE TABLE `analysis_records` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `filename` VARCHAR(255) NOT NULL COMMENT '文件名',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `ai_result` TEXT COMMENT 'AI识别结果(JSON)',
    `system_result` TEXT COMMENT '系统查询结果(JSON)',
    `comparison_result` TEXT COMMENT '对比结果(JSON)',
    `status` VARCHAR(20) DEFAULT 'pending' COMMENT '状态: pending, processing, completed, failed',
    `file_status` ENUM('active', 'deprecated', 'archived') DEFAULT 'active' COMMENT '文件状态',
    `status_changed_by` INT DEFAULT NULL COMMENT '状态修改人ID',
    `status_changed_at` TIMESTAMP NULL DEFAULT NULL COMMENT '状态修改时间',
    `status_reason` TEXT DEFAULT NULL COMMENT '状态修改原因',
    `file_hash` VARCHAR(64) DEFAULT NULL COMMENT '文件哈希值(用于去重)',
    `file_info` JSON DEFAULT NULL COMMENT '文件详细信息',
    `review_status` ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending' COMMENT '复核状态',
    `review_priority` ENUM('low', 'normal', 'high') DEFAULT 'normal' COMMENT '复核优先级',
    `accuracy_score` DECIMAL(5,4) DEFAULT NULL COMMENT '准确率评分',
    `audit_status` VARCHAR(20) COMMENT '审核状态: pass, fail, pending',
    `audit_comment` TEXT COMMENT '审核备注',
    `audited_by` INT COMMENT '审核人ID',
    `audited_at` DATETIME COMMENT '审核时间',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_analysis_type` (`analysis_type`),
    INDEX `idx_status` (`status`),
    INDEX `idx_file_status` (`file_status`),
    INDEX `idx_review_status` (`review_status`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_file_hash` (`file_hash`),
    INDEX `idx_accuracy_score` (`accuracy_score`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`audited_by`) REFERENCES `users`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`status_changed_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析记录表';
```

### 2. 用户表 (users)

**用途**: 存储系统用户信息和权限

```sql
CREATE TABLE `users` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `username` VARCHAR(64) UNIQUE NOT NULL COMMENT '用户名',
    `password_hash` VARCHAR(128) NOT NULL COMMENT '密码哈希',
    `role` VARCHAR(20) DEFAULT 'user' COMMENT '角色: user, analyst, admin',
    `status` VARCHAR(20) DEFAULT 'active' COMMENT '状态: active, inactive, suspended',
    `email` VARCHAR(255) DEFAULT NULL COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `last_login` DATETIME COMMENT '最后登录时间',
    `login_count` INT DEFAULT 0 COMMENT '登录次数',
    `created_by` INT COMMENT '创建者ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_username` (`username`),
    INDEX `idx_role` (`role`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
```

### 3. 提示词配置表 (prompt_config)

**用途**: 存储AI分析的提示词配置

```sql
CREATE TABLE `prompt_config` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `prompt_key` VARCHAR(100) NOT NULL COMMENT '提示词键名',
    `prompt_content` TEXT NOT NULL COMMENT '提示词内容',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `version` VARCHAR(20) DEFAULT 'v1.0' COMMENT '版本号',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY `unique_type_key` (`analysis_type`, `prompt_key`),
    INDEX `idx_analysis_type` (`analysis_type`),
    INDEX `idx_is_active` (`is_active`),
    INDEX `idx_version` (`version`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词配置表';
```

### 4. 提示词版本表 (prompt_versions)

**用途**: 存储提示词的版本历史

```sql
CREATE TABLE `prompt_versions` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `version` VARCHAR(20) NOT NULL COMMENT '版本号',
    `prompt_content` TEXT NOT NULL COMMENT '提示词内容',
    `description` TEXT DEFAULT NULL COMMENT '版本描述',
    `is_active` BOOLEAN DEFAULT FALSE COMMENT '是否激活',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `performance_score` DECIMAL(5,4) DEFAULT NULL COMMENT '性能评分',
    
    -- 索引
    UNIQUE KEY `unique_type_version` (`analysis_type`, `version`),
    INDEX `idx_type_active` (`analysis_type`, `is_active`),
    INDEX `idx_created_at` (`created_at`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词版本表';
```

### 5. 挡板数据表 (mock_data)

**用途**: 存储系统测试用的挡板数据

```sql
CREATE TABLE `mock_data` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `query_key` VARCHAR(255) NOT NULL COMMENT '查询关键字',
    `query_type` VARCHAR(50) NOT NULL COMMENT '查询类型',
    `mock_result` TEXT COMMENT '挡板返回结果(JSON)',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_query_key` (`query_key`),
    INDEX `idx_query_type` (`query_type`),
    INDEX `idx_is_active` (`is_active`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='挡板数据表';
```

### 6. 客户系统数据表 (customer_system_data)

**用途**: 存储客户系统的模拟数据

```sql
CREATE TABLE `customer_system_data` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `query_key` VARCHAR(255) NOT NULL COMMENT '查询关键字',
    `query_type` VARCHAR(50) NOT NULL COMMENT '查询类型',
    `data_json` TEXT COMMENT '客户系统返回内容(JSON)',
    `description` TEXT COMMENT '描述',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    `created_by` INT COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_query_key` (`query_key`),
    INDEX `idx_query_type` (`query_type`),
    INDEX `idx_is_active` (`is_active`),
    
    -- 外键约束
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户系统数据表';
```

### 7. 文件标签表 (file_tags)

**用途**: 存储文件的标签信息

```sql
CREATE TABLE `file_tags` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `file_id` INT NOT NULL COMMENT '文件ID',
    `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `tag_color` VARCHAR(7) DEFAULT '#2563eb' COMMENT '标签颜色',
    `tag_description` TEXT DEFAULT NULL COMMENT '标签描述',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    UNIQUE KEY `unique_file_tag` (`file_id`, `tag_name`),
    INDEX `idx_tag_name` (`tag_name`),
    INDEX `idx_created_by` (`created_by`),
    
    -- 外键约束
    FOREIGN KEY (`file_id`) REFERENCES `analysis_records`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件标签表';
```

### 8. 复核记录表 (review_records)

**用途**: 存储管理员复核记录

```sql
CREATE TABLE `review_records` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `record_id` INT NOT NULL COMMENT '分析记录ID',
    `reviewer_id` INT NOT NULL COMMENT '复核人ID',
    `review_status` ENUM('approved', 'rejected', 'needs_revision') NOT NULL COMMENT '复核状态',
    `review_comment` TEXT DEFAULT NULL COMMENT '复核意见',
    `corrections` JSON DEFAULT NULL COMMENT '修正内容',
    `review_time` DECIMAL(8,2) DEFAULT NULL COMMENT '复核耗时(秒)',
    `auto_reviewed` BOOLEAN DEFAULT FALSE COMMENT '是否自动复核',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX `idx_record_status` (`record_id`, `review_status`),
    INDEX `idx_reviewer` (`reviewer_id`),
    INDEX `idx_created_at` (`created_at`),
    
    -- 外键约束
    FOREIGN KEY (`record_id`) REFERENCES `analysis_records`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`reviewer_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='复核记录表';
```

### 9. 标准答案表 (standard_answers)

**用途**: 存储标准答案数据

```sql
CREATE TABLE `standard_answers` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `analysis_type` VARCHAR(50) NOT NULL COMMENT '分析类型',
    `file_pattern` VARCHAR(255) DEFAULT NULL COMMENT '文件名模式',
    `standard_result` JSON NOT NULL COMMENT '标准答案结果',
    `confidence_score` DECIMAL(5,4) DEFAULT 1.0000 COMMENT '置信度评分',
    `source_record_id` INT DEFAULT NULL COMMENT '来源记录ID',
    `created_by` INT DEFAULT NULL COMMENT '创建人ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `is_active` BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    
    -- 索引
    INDEX `idx_type_pattern` (`analysis_type`, `file_pattern`),
    INDEX `idx_confidence` (`confidence_score`),
    INDEX `idx_active` (`is_active`),
    
    -- 外键约束
    FOREIGN KEY (`source_record_id`) REFERENCES `analysis_records`(`id`) ON DELETE SET NULL,
    FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标准答案表';
```

### 10. 模型配置表 (model_configs)

**用途**: 存储大模型配置信息

```sql
CREATE TABLE `model_configs` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `model_id` VARCHAR(50) UNIQUE NOT NULL COMMENT '模型标识',
    `model_name` VARCHAR(100) NOT NULL COMMENT '模型名称',
    `api_url` VARCHAR(255) NOT NULL COMMENT 'API地址',
    `api_key` VARCHAR(255) NOT NULL COMMENT 'API密钥',
    `vision_model` VARCHAR(100) DEFAULT NULL COMMENT '视觉模型名称',
    `timeout` INT DEFAULT 30 COMMENT '超时时间(秒)',
    `max_tokens` INT DEFAULT 4096 COMMENT '最大令牌数',
    `temperature` DECIMAL(3,2) DEFAULT 0.70 COMMENT '温度参数',
    `is_active` BOOLEAN DEFAULT FALSE COMMENT '是否激活',
    `last_test_at` TIMESTAMP NULL DEFAULT NULL COMMENT '最后测试时间',
    `test_status` ENUM('success', 'failed', 'pending') DEFAULT 'pending' COMMENT '测试状态',
    `response_time` DECIMAL(8,2) DEFAULT 0.00 COMMENT '响应时间(秒)',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_active` (`is_active`),
    INDEX `idx_test_status` (`test_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模型配置表';
```

## 📈 统计和监控表

### 11. 仪表盘统计表 (dashboard_stats)

**用途**: 存储仪表盘统计数据

```sql
CREATE TABLE `dashboard_stats` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `stat_date` DATE NOT NULL COMMENT '统计日期',
    `analysis_type` VARCHAR(50) DEFAULT NULL COMMENT '分析类型',
    `total_files` INT DEFAULT 0 COMMENT '总文件数',
    `processed_files` INT DEFAULT 0 COMMENT '已处理文件数',
    `accuracy_rate` DECIMAL(5,4) DEFAULT 0.0000 COMMENT '识别准确率',
    `avg_processing_time` DECIMAL(8,2) DEFAULT 0.00 COMMENT '平均处理时间(秒)',
    `success_count` INT DEFAULT 0 COMMENT '成功处理数',
    `failed_count` INT DEFAULT 0 COMMENT '失败处理数',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_date_type` (`stat_date`, `analysis_type`),
    INDEX `idx_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仪表盘统计数据表';
```

### 12. 用户活动日志表 (user_activities)

**用途**: 记录用户操作日志

```sql
CREATE TABLE `user_activities` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `user_id` INT NOT NULL COMMENT '用户ID',
    `session_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID',
    `request_id` VARCHAR(64) DEFAULT NULL COMMENT '请求ID',
    `action` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `resource` VARCHAR(255) DEFAULT NULL COMMENT '操作资源',
    `details` JSON DEFAULT NULL COMMENT '操作详情',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_session` (`session_id`),
    INDEX `idx_request` (`request_id`),
    
    -- 外键约束
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';
```

### 13. 全局设置表 (global_settings)

**用途**: 存储系统全局配置

```sql
CREATE TABLE `global_settings` (
    `id` INT PRIMARY KEY AUTO_INCREMENT COMMENT '主键',
    `key` VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    `value` TEXT COMMENT '配置值',
    `description` TEXT COMMENT '配置描述',
    `data_type` VARCHAR(20) DEFAULT 'string' COMMENT '数据类型',
    `is_public` BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_key` (`key`),
    INDEX `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='全局设置表';
```

## 🔗 表关系图

```
users (用户表)
├── analysis_records (分析记录表) - created_by, audited_by, status_changed_by
├── prompt_config (提示词配置表) - created_by
├── prompt_versions (提示词版本表) - created_by
├── mock_data (挡板数据表) - created_by
├── customer_system_data (客户系统数据表) - created_by
├── file_tags (文件标签表) - created_by
├── review_records (复核记录表) - reviewer_id
├── standard_answers (标准答案表) - created_by
└── user_activities (用户活动日志表) - user_id

analysis_records (分析记录表)
├── file_tags (文件标签表) - file_id
├── review_records (复核记录表) - record_id
└── standard_answers (标准答案表) - source_record_id
```

## 📝 初始化数据

### 默认用户
```sql
INSERT INTO users (username, password_hash, role, status) VALUES
('admin', 'hashed_password', 'admin', 'active'),
('analyst', 'hashed_password', 'analyst', 'active'),
('user', 'hashed_password', 'user', 'active');
```

### 默认配置
```sql
INSERT INTO global_settings (`key`, `value`, `description`) VALUES
('system_name', '多场景智能化文档分析系统', '系统名称'),
('max_file_size', '52428800', '最大文件大小(50MB)'),
('allowed_extensions', 'pdf,png,jpg,jpeg,gif,bmp,tiff', '允许的文件扩展名'),
('default_analysis_type', 'future', '默认分析类型'),
('mock_mode_enabled', 'true', '是否启用挡板模式');
```

### 默认模型配置
```sql
INSERT INTO model_configs (model_id, model_name, api_url, api_key, is_active) VALUES
('qwen3-32b', 'Qwen3-32B', 'http://**************:23000/v1', 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553', TRUE);
```

---

> 本文档提供了多场景智能化文档分析系统的完整数据库设计，包括13个核心数据表的详细结构、索引设计、关系约束和初始化数据。这个设计支持系统的所有功能需求，并考虑了性能优化和数据完整性。

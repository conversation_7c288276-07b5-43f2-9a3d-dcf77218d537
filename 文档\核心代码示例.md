# 多场景智能化文档分析系统 - 核心代码示例

## 📋 概述

本文档提供了系统核心功能的代码实现示例，包括主要的类、方法和业务逻辑，为重新开发提供参考。

## 🏗️ 项目结构

```
nb_bank_ai_poc/
├── app.py                  # Flask主应用
├── models.py               # 数据模型
├── config.py               # 配置文件
├── database_service.py     # 数据库服务
├── bowang_service.py       # 系统查询服务
├── comparison_service.py   # 结果对比服务
├── util_ai.py             # AI工具类
├── util_img_process.py    # 图像处理工具
├── templates/             # 前端模板
├── static/                # 静态资源
├── uploads/               # 上传文件
└── requirements.txt       # 依赖包
```

## 🔧 核心代码实现

### 1. Flask主应用 (app.py)

```python
from flask import Flask, request, jsonify, render_template
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
from flask_cors import CORS
import os
import json
from datetime import datetime

# 应用初始化
app = Flask(__name__)
app.secret_key = 'your_secret_key_here'
CORS(app)

# 数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://user:password@localhost/dbname'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)

# 登录管理
login_manager = LoginManager()
login_manager.init_app(app)

# 导入服务
from database_service import DatabaseService
from bowang_service import BowangService
from comparison_service import ComparisonService
from util_ai import AIService
from util_img_process import ImageProcessor

# 初始化服务
db_service = DatabaseService(app)
bowang_service = BowangService(use_mock=True)
comparison_service = ComparisonService()
ai_service = AIService()
image_processor = ImageProcessor()

# 主页路由
@app.route('/')
def index():
    return render_template('index.html')

# 文件上传接口
@app.route('/api/upload', methods=['POST'])
@login_required
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有文件'}), 400
        
        file = request.files['file']
        analysis_type = request.form.get('analysis_type', 'futures_account')
        
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        # 保存文件
        filename = secure_filename(file.filename)
        upload_dir = os.path.join('uploads', analysis_type)
        os.makedirs(upload_dir, exist_ok=True)
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'filename': filename,
            'file_path': file_path
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 文件分析接口
@app.route('/api/analyze', methods=['POST'])
@login_required
def analyze_file():
    try:
        data = request.get_json()
        filename = data.get('filename')
        analysis_type = data.get('analysis_type', 'futures_account')
        use_mock = data.get('use_mock', True)
        
        # 1. 文件预处理
        file_path = os.path.join('uploads', analysis_type, filename)
        if filename.lower().endswith('.pdf'):
            image_paths = image_processor.convert_pdf_to_images(file_path)
        else:
            image_paths = [file_path]
        
        # 2. AI识别
        ai_result = ai_service.analyze_images(image_paths, analysis_type)
        
        # 3. 系统查询
        bowang_service.set_mock_mode(use_mock)
        system_result = bowang_service.query_hosting_system(
            query_params=ai_result,
            query_type=analysis_type
        )
        
        # 4. 结果对比
        comparison_result = comparison_service.compare_results(
            ai_result, system_result
        )
        
        # 5. 保存记录
        record_id = db_service.save_analysis_record(
            filename=filename,
            analysis_type=analysis_type,
            ai_result=ai_result,
            system_result=system_result,
            comparison_result=comparison_result.get('comparison'),
            created_by=current_user.id
        )
        
        return jsonify({
            'success': True,
            'message': '分析完成',
            'data': {
                'record_id': record_id,
                'ai_result': ai_result,
                'system_result': system_result,
                'comparison_result': comparison_result
            }
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# 获取分析记录
@app.route('/api/records', methods=['GET'])
@login_required
def get_records():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        analysis_type = request.args.get('analysis_type')
        
        records = db_service.get_analysis_records(
            page=page,
            per_page=per_page,
            analysis_type=analysis_type,
            user_id=current_user.id if current_user.role == 'user' else None
        )
        
        return jsonify({
            'success': True,
            'data': records
        })
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5105)
```

### 2. 数据模型 (models.py)

```python
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
import json

db = SQLAlchemy()

class AnalysisRecord(db.Model):
    """分析记录表"""
    __tablename__ = 'analysis_records'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    filename = db.Column(db.String(255), nullable=False, comment='文件名')
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    ai_result = db.Column(db.Text, comment='AI识别结果(JSON)')
    system_result = db.Column(db.Text, comment='系统查询结果(JSON)')
    comparison_result = db.Column(db.Text, comment='对比结果(JSON)')
    status = db.Column(db.String(20), default='pending', comment='状态')
    file_status = db.Column(db.Enum('active', 'deprecated', 'archived'), 
                           default='active', comment='文件状态')
    review_status = db.Column(db.Enum('pending', 'approved', 'rejected', 'needs_revision'), 
                             default='pending', comment='复核状态')
    accuracy_score = db.Column(db.Numeric(5,4), comment='准确率评分')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), comment='创建人ID')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, 
                          onupdate=datetime.now, comment='更新时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'filename': self.filename,
            'analysis_type': self.analysis_type,
            'ai_result': json.loads(self.ai_result) if self.ai_result else None,
            'system_result': json.loads(self.system_result) if self.system_result else None,
            'comparison_result': json.loads(self.comparison_result) if self.comparison_result else None,
            'status': self.status,
            'file_status': self.file_status,
            'review_status': self.review_status,
            'accuracy_score': float(self.accuracy_score) if self.accuracy_score else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': self.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

class User(UserMixin, db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(64), unique=True, nullable=False, comment='用户名')
    password_hash = db.Column(db.String(128), nullable=False, comment='密码哈希')
    role = db.Column(db.String(20), default='user', comment='角色')
    status = db.Column(db.String(20), default='active', comment='状态')
    last_login = db.Column(db.DateTime, comment='最后登录时间')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'role': self.role,
            'status': self.status,
            'last_login': self.last_login.strftime('%Y-%m-%d %H:%M:%S') if self.last_login else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

class PromptConfig(db.Model):
    """提示词配置表"""
    __tablename__ = 'prompt_config'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    analysis_type = db.Column(db.String(50), nullable=False, comment='分析类型')
    prompt_key = db.Column(db.String(100), nullable=False, comment='提示词键名')
    prompt_content = db.Column(db.Text, nullable=False, comment='提示词内容')
    description = db.Column(db.Text, comment='描述')
    is_active = db.Column(db.Boolean, default=True, comment='是否激活')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, 
                          onupdate=datetime.now, comment='更新时间')

class MockData(db.Model):
    """挡板数据表"""
    __tablename__ = 'mock_data'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    query_key = db.Column(db.String(255), nullable=False, comment='查询关键字')
    query_type = db.Column(db.String(50), nullable=False, comment='查询类型')
    mock_result = db.Column(db.Text, comment='挡板返回结果(JSON)')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    
    def to_dict(self):
        return {
            'id': self.id,
            'query_key': self.query_key,
            'query_type': self.query_type,
            'mock_result': json.loads(self.mock_result) if self.mock_result else None,
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
```

### 3. 数据库服务 (database_service.py)

```python
from models import db, AnalysisRecord, User, PromptConfig, MockData
from flask import current_app
import json
from datetime import datetime

class DatabaseService:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        with app.app_context():
            db.create_all()
    
    def save_analysis_record(self, filename, analysis_type, ai_result, 
                           system_result, comparison_result, created_by=None):
        """保存分析记录"""
        try:
            record = AnalysisRecord(
                filename=filename,
                analysis_type=analysis_type,
                ai_result=json.dumps(ai_result, ensure_ascii=False) if ai_result else None,
                system_result=json.dumps(system_result, ensure_ascii=False) if system_result else None,
                comparison_result=json.dumps(comparison_result, ensure_ascii=False) if comparison_result else None,
                status='completed',
                created_by=created_by
            )
            db.session.add(record)
            db.session.commit()
            return record.id
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_analysis_records(self, page=1, per_page=20, analysis_type=None, user_id=None):
        """获取分析记录列表"""
        query = AnalysisRecord.query
        
        if analysis_type:
            query = query.filter(AnalysisRecord.analysis_type == analysis_type)
        
        if user_id:
            query = query.filter(AnalysisRecord.created_by == user_id)
        
        query = query.order_by(AnalysisRecord.created_at.desc())
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        
        return {
            'records': [record.to_dict() for record in pagination.items],
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': page,
            'per_page': per_page
        }
    
    def get_analysis_record_by_id(self, record_id):
        """根据ID获取分析记录"""
        record = AnalysisRecord.query.get(record_id)
        return record.to_dict() if record else None
    
    def update_record_status(self, record_id, status, review_status=None):
        """更新记录状态"""
        try:
            record = AnalysisRecord.query.get(record_id)
            if record:
                record.status = status
                if review_status:
                    record.review_status = review_status
                record.updated_at = datetime.now()
                db.session.commit()
                return True
            return False
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_prompt_config(self, analysis_type, prompt_key):
        """获取提示词配置"""
        config = PromptConfig.query.filter_by(
            analysis_type=analysis_type,
            prompt_key=prompt_key,
            is_active=True
        ).first()
        return config.prompt_content if config else None
    
    def save_mock_data(self, query_key, query_type, mock_result):
        """保存挡板数据"""
        try:
            # 先查找是否存在
            existing = MockData.query.filter_by(
                query_key=query_key,
                query_type=query_type
            ).first()
            
            if existing:
                existing.mock_result = json.dumps(mock_result, ensure_ascii=False)
                existing.created_at = datetime.now()
            else:
                mock_data = MockData(
                    query_key=query_key,
                    query_type=query_type,
                    mock_result=json.dumps(mock_result, ensure_ascii=False)
                )
                db.session.add(mock_data)
            
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            raise e
    
    def get_mock_data(self, query_key, query_type):
        """获取挡板数据"""
        mock_data = MockData.query.filter_by(
            query_key=query_key,
            query_type=query_type
        ).first()
        
        if mock_data:
            return json.loads(mock_data.mock_result)
        return None
```

### 4. AI服务 (util_ai.py)

```python
import openai
import base64
import json
from config import OPENAI_CONFIG
from database_service import DatabaseService

class AIService:
    def __init__(self):
        self.client = openai.OpenAI(
            api_key=OPENAI_CONFIG['api_key'],
            base_url=OPENAI_CONFIG['base_url']
        )
        self.model_name = OPENAI_CONFIG['model_name']
        self.vision_model = OPENAI_CONFIG['vision_model']
        self.db_service = DatabaseService()
    
    def analyze_images(self, image_paths, analysis_type):
        """分析图片内容"""
        try:
            # 获取提示词
            system_prompt = self.db_service.get_prompt_config(analysis_type, 'system_prompt')
            img_prompt = self.db_service.get_prompt_config(analysis_type, 'img_prompt_1')
            
            if not system_prompt or not img_prompt:
                raise ValueError(f"未找到分析类型 {analysis_type} 的提示词配置")
            
            # 编码图片
            encoded_images = []
            for image_path in image_paths:
                with open(image_path, 'rb') as image_file:
                    encoded_image = base64.b64encode(image_file.read()).decode('utf-8')
                    encoded_images.append(encoded_image)
            
            # 构建消息
            messages = [
                {"role": "system", "content": system_prompt}
            ]
            
            # 添加图片内容
            user_content = [{"type": "text", "text": img_prompt}]
            for encoded_image in encoded_images:
                user_content.append({
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{encoded_image}"}
                })
            
            messages.append({"role": "user", "content": user_content})
            
            # 调用AI
            response = self.client.chat.completions.create(
                model=self.vision_model,
                messages=messages,
                max_tokens=4096,
                temperature=0.1
            )
            
            # 解析结果
            result_text = response.choices[0].message.content
            try:
                result = json.loads(result_text)
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试提取JSON部分
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    result = json.loads(json_match.group())
                else:
                    result = {"raw_response": result_text}
            
            return result
            
        except Exception as e:
            raise Exception(f"AI分析失败: {str(e)}")
    
    def encode_image(self, image_path):
        """编码图片为base64"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
```

### 5. 系统查询服务 (bowang_service.py)

```python
import requests
import json
from database_service import DatabaseService

class BowangService:
    def __init__(self, base_url="http://localhost:8080", use_mock=False):
        self.base_url = base_url
        self.use_mock = use_mock
        self.db_service = DatabaseService()
    
    def query_hosting_system(self, query_params, query_type="default"):
        """查询托管系统内容"""
        if self.use_mock:
            return self._get_mock_data(query_params, query_type)
        else:
            return self._query_real_system(query_params, query_type)
    
    def _get_mock_data(self, query_params, query_type):
        """获取挡板数据"""
        # 生成参数化key
        query_key = f"{query_type}_{json.dumps(query_params, sort_keys=True)}"
        
        # 先查参数化key
        mock_data = self.db_service.get_mock_data(query_key, query_type)
        if mock_data:
            return {"success": True, "data": mock_data, "source": "mock_parameterized"}
        
        # 再查default key
        default_data = self.db_service.get_mock_data("default", query_type)
        if default_data:
            return {"success": True, "data": default_data, "source": "mock_default"}
        
        # 最后返回内置默认数据
        return self._get_default_mock_data(query_type, query_params)
    
    def _get_default_mock_data(self, query_type, query_params):
        """获取内置默认挡板数据"""
        default_data = {
            "future": {
                "account_info": "模拟期货账户信息",
                "trading_codes": ["********", "********"],
                "member_id": "001",
                "status": "active"
            },
            "broker_interest": {
                "customer_id": "C001",
                "interest_rate": "3.5%",
                "effective_date": "2024-01-01",
                "status": "approved"
            }
        }
        
        return {
            "success": True,
            "data": default_data.get(query_type, {}),
            "source": "mock_builtin"
        }
    
    def _query_real_system(self, query_params, query_type):
        """查询真实系统"""
        try:
            url = f"{self.base_url}/api/query/{query_type}"
            response = requests.post(url, json=query_params, timeout=30)
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "success": False,
                    "error": f"请求失败: {response.status_code}",
                    "data": None
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"请求异常: {str(e)}",
                "data": None
            }
    
    def set_mock_mode(self, use_mock):
        """设置挡板模式"""
        self.use_mock = use_mock
```

### 6. 结果对比服务 (comparison_service.py)

```python
import json
from difflib import SequenceMatcher

class ComparisonService:
    def compare_results(self, ai_result, system_result):
        """对比AI结果和系统结果"""
        try:
            if not ai_result or not system_result:
                return {
                    "success": False,
                    "message": "缺少对比数据",
                    "comparison": None
                }
            
            # 提取系统数据
            system_data = system_result.get('data', {}) if isinstance(system_result, dict) else {}
            
            # 执行字段对比
            comparison_details = []
            similarity_scores = []
            
            for key, ai_value in ai_result.items():
                system_value = system_data.get(key)
                
                # 计算相似度
                similarity = self._calculate_similarity(str(ai_value), str(system_value))
                similarity_scores.append(similarity)
                
                comparison_details.append({
                    "field": key,
                    "ai_value": ai_value,
                    "system_value": system_value,
                    "similarity": similarity,
                    "match": similarity > 0.8
                })
            
            # 计算总体相似度
            overall_similarity = sum(similarity_scores) / len(similarity_scores) if similarity_scores else 0
            
            return {
                "success": True,
                "comparison": {
                    "overall_similarity": overall_similarity,
                    "match_count": sum(1 for detail in comparison_details if detail["match"]),
                    "total_fields": len(comparison_details),
                    "details": comparison_details
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"对比失败: {str(e)}",
                "comparison": None
            }
    
    def _calculate_similarity(self, text1, text2):
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0.0
        
        # 使用SequenceMatcher计算相似度
        matcher = SequenceMatcher(None, str(text1).lower(), str(text2).lower())
        return matcher.ratio()
```

### 7. 配置文件 (config.py)

```python
import os
from datetime import timedelta

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://username:password@localhost/nb_bank_ai_poc'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'pool_pre_ping': True
    }

    # 文件上传配置
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    UPLOAD_FOLDER = 'uploads'
    ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff'}

    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)

    # CORS配置
    CORS_ORIGINS = ["http://localhost:3000", "http://127.0.0.1:3000"]

# OpenAI配置
OPENAI_CONFIG = {
    'api_key': 'sk-LLFZSeNxq59tCMCoA2Fc909a5a5d4720B0188556F67a7553',
    'base_url': 'http://**************:23000/v1',
    'model_name': 'qwen3-32b',
    'vision_model': 'qwen-vl-max',
    'timeout': 60,
    'max_tokens': 4096,
    'temperature': 0.1
}

# 业务配置
ANALYSIS_TYPES = {
    'futures_account': '期货账户/开户文件解析',
    'wealth_management': '理财产品说明书',
    'broker_interest': '券商账户计息变更',
    'account_opening': '账户开户场景',
    'ningyin_fee': '宁银理财费用变更',
    'non_standard_trade': '非标交易确认单解析'
}

# 系统配置
SYSTEM_CONFIG = {
    'system_name': '多场景智能化文档分析系统',
    'version': '2.0.0',
    'debug': True,
    'host': '0.0.0.0',
    'port': 5105,
    'mock_mode_default': True,
    'auto_analysis_single_file': True,
    'auto_analysis_multiple_files': False
}

# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        }
    },
    'handlers': {
        'file': {
            'class': 'logging.FileHandler',
            'filename': 'logs/app.log',
            'formatter': 'default'
        },
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'default'
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file', 'console']
    }
}
```

### 8. 依赖包文件 (requirements.txt)

```txt
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Login==0.6.3
Flask-CORS==4.0.0
PyMySQL==1.1.0
cryptography==41.0.4
openai==1.3.5
PyMuPDF==1.23.5
Pillow==10.0.1
python-dotenv==1.0.0
requests==2.31.0
pandas==2.1.1
openpyxl==3.1.2
reportlab==4.0.4
Werkzeug==2.3.7
```

### 9. 图像处理工具 (util_img_process.py)

```python
import fitz  # PyMuPDF
from PIL import Image
import os
import base64
from io import BytesIO

class ImageProcessor:
    def __init__(self):
        self.supported_formats = ['pdf', 'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff']

    def convert_pdf_to_images(self, pdf_path, output_dir=None):
        """将PDF转换为图片"""
        try:
            if not output_dir:
                output_dir = os.path.join('processed', 'pdf_images')
            os.makedirs(output_dir, exist_ok=True)

            doc = fitz.open(pdf_path)
            image_paths = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)

                # 设置缩放比例，提高图片质量
                mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
                pix = page.get_pixmap(matrix=mat)

                # 保存图片
                filename = f"{os.path.splitext(os.path.basename(pdf_path))[0]}_page_{page_num + 1}.png"
                image_path = os.path.join(output_dir, filename)
                pix.save(image_path)
                image_paths.append(image_path)

            doc.close()
            return image_paths

        except Exception as e:
            raise Exception(f"PDF转换失败: {str(e)}")

    def resize_image(self, image_path, max_size=(1920, 1080)):
        """调整图片大小"""
        try:
            with Image.open(image_path) as img:
                # 计算新尺寸
                img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # 保存调整后的图片
                output_path = image_path.replace('.', '_resized.')
                img.save(output_path, optimize=True, quality=85)
                return output_path

        except Exception as e:
            raise Exception(f"图片调整失败: {str(e)}")

    def encode_image_to_base64(self, image_path):
        """将图片编码为base64"""
        try:
            with open(image_path, "rb") as image_file:
                encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
                return encoded_string
        except Exception as e:
            raise Exception(f"图片编码失败: {str(e)}")

    def validate_image(self, image_path):
        """验证图片文件"""
        try:
            with Image.open(image_path) as img:
                img.verify()
                return True
        except Exception:
            return False

    def get_image_info(self, image_path):
        """获取图片信息"""
        try:
            with Image.open(image_path) as img:
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height
                }
        except Exception as e:
            raise Exception(f"获取图片信息失败: {str(e)}")
```

### 10. 启动脚本 (run.py)

```python
#!/usr/bin/env python3
"""
多场景智能化文档分析系统启动脚本
"""

import os
import sys
import logging
from logging.config import dictConfig

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from config import LOGGING_CONFIG, SYSTEM_CONFIG

def setup_logging():
    """设置日志"""
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)

    # 配置日志
    dictConfig(LOGGING_CONFIG)

def create_directories():
    """创建必要的目录"""
    directories = [
        'uploads',
        'processed',
        'logs',
        'static/uploads',
        'templates'
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)

def main():
    """主函数"""
    print("=" * 60)
    print(f"🚀 启动 {SYSTEM_CONFIG['system_name']}")
    print(f"📦 版本: {SYSTEM_CONFIG['version']}")
    print("=" * 60)

    # 设置日志
    setup_logging()

    # 创建目录
    create_directories()

    # 启动应用
    app.run(
        debug=SYSTEM_CONFIG['debug'],
        host=SYSTEM_CONFIG['host'],
        port=SYSTEM_CONFIG['port'],
        threaded=True
    )

if __name__ == '__main__':
    main()
```

---

> 以上代码示例展示了多场景智能化文档分析系统的完整实现，包括Flask应用结构、数据模型定义、服务类设计、配置文件、工具类等所有关键部分。这些代码为重新开发提供了完整的参考框架和实现细节。

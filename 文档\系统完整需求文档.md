# 多场景智能化文档分析系统 - 完整需求文档

## 📋 项目概述

### 系统名称
多场景智能化文档分析系统

### 系统定位
基于Flask的Web应用程序，专门用于处理各类金融文档的智能分析，通过AI识别、系统查询和结果对比，为用户提供准确的文档分析服务。

### 核心价值
- **多场景支持**: 支持期货开户、理财产品、券商计息、非标交易等6大业务场景
- **AI智能识别**: AI自动识别文档内容，提供初始分析结果
- **人工校正机制**: 每个文件的识别结果都可人工编辑校正，确保准确性
- **完整的管理体系**: 用户权限、审核流程、数据导出、结果管理

## 🎯 业务场景

### 支持的文档类型
1. **期货开户文件解析** (`future`)
   - 产品名称、资金账号、交易编码识别
   - 各大交易所会员号提取
   - 开始/结束时间识别

2. **理财产品说明书** (`financial`)
   - 销售机构信息提取
   - 产品基本信息识别
   - 风险等级和收益信息

3. **券商账户计息变更** (`broker_interest`)
   - 客户号、资产名称识别
   - 计息利率变更信息
   - 变更生效时间

4. **期货交易会员** (`future_member`)
   - 会员信息提取
   - 资金账户、保证金账户
   - 银期转账信息

5. **宁银费用变更** (`ningyin_fee`)
   - 产品信息识别
   - 费用变更详情
   - 变更原因和时间

6. **非标交易确认单解析** (`non_standard_trade`)
   - 交易确认信息
   - 交易对手方信息
   - 交易金额和条件

## 🏗️ 系统架构

### 技术栈
- **后端框架**: Flask 2.3.3
- **数据库**: MySQL 8.0+
- **AI服务**: OpenAI API (Qwen3-32B)
- **文件处理**: PyMuPDF, PIL
- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **其他**: Flask-Login, Flask-CORS, Flask-SQLAlchemy

### 核心组件
1. **DatabaseService**: 数据库服务层
2. **ResultService**: 结果管理服务
3. **AI工具类**: 图像处理和AI调用
4. **用户认证**: Flask-Login集成

### 目录结构
```
nb_bank_ai_poc/
├── app.py                  # 主应用程序
├── models.py               # 数据模型定义
├── config.py               # 系统配置
├── database_service.py     # 数据库服务
├── result_service.py       # 结果管理服务
├── util_ai.py             # AI工具类
├── util_img_process.py    # 图像处理工具
├── templates/             # 前端模板
│   ├── index.html         # 主页面
│   ├── records.html       # 记录管理
│   ├── review.html        # 审核页面
│   └── prompt_config.html # 提示词配置
├── static/                # 静态资源
├── uploads/               # 上传文件存储
├── processed/             # 处理后文件
└── requirements.txt       # 依赖包列表
```

## 💾 数据库设计

### 核心数据表

#### 1. 分析记录表 (analysis_records)
```sql
CREATE TABLE analysis_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    analysis_type VARCHAR(50) NOT NULL COMMENT '分析类型',
    ai_result TEXT COMMENT 'AI识别结果(JSON)',
    correct_result TEXT COMMENT '正确结果(JSON)',
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态',
    file_status ENUM('active', 'deprecated', 'archived') DEFAULT 'active',
    review_status ENUM('pending', 'approved', 'rejected', 'needs_revision') DEFAULT 'pending',
    accuracy_score DECIMAL(5,4) DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 用户表 (users)
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(64) UNIQUE NOT NULL,
    password_hash VARCHAR(128) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' COMMENT '角色: user, analyst, admin',
    status VARCHAR(20) DEFAULT 'active',
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 提示词配置表 (prompt_config)
```sql
CREATE TABLE prompt_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50) NOT NULL,
    prompt_key VARCHAR(100) NOT NULL,
    prompt_content TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 4. 结果编辑历史表 (result_edit_history)
```sql
CREATE TABLE result_edit_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL COMMENT '分析记录ID',
    previous_result TEXT COMMENT '修改前结果(JSON)',
    new_result TEXT COMMENT '修改后结果(JSON)',
    edited_by INT COMMENT '编辑人ID',
    edit_reason TEXT COMMENT '编辑原因',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES analysis_records(id),
    FOREIGN KEY (edited_by) REFERENCES users(id)
);
```

#### 5. 文件标签表 (file_tags)
```sql
CREATE TABLE file_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_id INT NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    tag_color VARCHAR(7) DEFAULT '#2563eb',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES analysis_records(id)
);
```

#### 6. 复核记录表 (review_records)
```sql
CREATE TABLE review_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    review_status ENUM('approved', 'rejected', 'needs_revision') NOT NULL,
    review_comment TEXT,
    corrections JSON,
    review_time DECIMAL(8,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES analysis_records(id)
);
```

## 🔧 核心功能模块

### 1. 文件处理流程
```python
# 完整的文件处理流程
def process_file(file, analysis_type):
    # 1. 文件上传和验证
    filename = secure_filename(file.filename)
    file_path = save_uploaded_file(file, analysis_type)

    # 2. 文件预处理
    if filename.endswith('.pdf'):
        image_paths = convert_pdf_to_images(file_path)
    else:
        image_paths = [file_path]

    # 3. AI识别
    ai_result = ai_analyze_images(image_paths, analysis_type)

    # 4. 初始化正确结果（默认与AI结果相同）
    correct_result = ai_result.copy()

    # 5. 保存记录
    record_id = db_service.save_analysis_record(
        filename=filename,
        analysis_type=analysis_type,
        ai_result=ai_result,
        correct_result=correct_result
    )

    return record_id, ai_result, correct_result
```

### 2. 用户认证与权限
```python
# 用户角色权限定义
ROLE_PERMISSIONS = {
    'user': ['upload', 'view_own_records'],
    'analyst': ['upload', 'view_all_records', 'tag_files', 'reanalyze'],
    'admin': ['all_permissions', 'user_management', 'system_config', 'review']
}

# 权限装饰器
def require_permission(permission):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return jsonify({'success': False, 'message': '需要登录'}), 401
            if not has_permission(current_user, permission):
                return jsonify({'success': False, 'message': '权限不足'}), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator
```

### 3. 挡板模式系统
```python
class BowangService:
    def __init__(self, use_mock=False):
        self.use_mock = use_mock
    
    def query_hosting_system(self, query_params, query_type):
        # 1. 优先查询本地客户系统数据
        local_data = self.get_customer_system_data(query_params, query_type)
        if local_data:
            return {"success": True, "data": local_data, "source": "local"}
        
        # 2. 挡板模式
        if self.use_mock:
            return self.get_mock_data(query_params, query_type)
        
        # 3. 真实系统查询
        return self.query_real_system(query_params, query_type)
    
    def get_mock_data(self, query_params, query_type):
        # 先查参数化key，再查default key，最后用内置默认
        query_key = f"{query_type}_{json.dumps(query_params, sort_keys=True)}"
        mock_data = MockData.query.filter_by(
            query_key=query_key, query_type=query_type
        ).first()
        if mock_data:
            return json.loads(mock_data.mock_result)
        
        # 查询default key
        default_mock = MockData.query.filter_by(
            query_key="default", query_type=query_type
        ).first()
        if default_mock:
            return json.loads(default_mock.mock_result)
        
        # 返回内置默认数据
        return self.get_default_mock_data(query_type)
```

## 🎨 前端界面设计

### 页面结构
1. **主页面** (`/`) - 文件上传和分析
2. **记录管理** (`/records`) - 分析记录查看和管理
3. **审核页面** (`/review`) - 管理员复核功能
4. **提示词配置** (`/prompt_config`) - 提示词管理

### UI组件规范
- **色彩方案**: 商务蓝 (#2563eb) 为主色调
- **布局方式**: 卡片式布局，响应式设计
- **交互效果**: 平滑动画，悬停反馈
- **表格样式**: 斑马纹，悬停高亮
- **按钮设计**: 圆角，渐变背景，状态反馈

### 关键交互功能
- 拖拽上传文件
- 批量分析进度提示
- 实时结果对比展示
- 挡板数据可视化编辑
- 文件标签管理
- PDF在线预览

## 📊 仪表盘设计

### 关键指标卡片
- 总处理量、识别准确率、今日处理、待复核数、系统状态

### 图表组件
1. **处理量趋势图** - 折线图显示各业务类型处理量变化
2. **准确率趋势图** - 折线图显示识别准确率时间变化
3. **业务类型分布** - 饼图显示各业务场景占比
4. **文件状态分布** - 环形图显示文件状态分布
5. **处理时长分布** - 柱状图显示不同时长区间文件数量
6. **错误类型统计** - 水平柱状图显示常见错误分布
7. **实时处理队列** - 表格显示当前处理中的文件
8. **系统性能监控** - 仪表盘显示CPU、内存、磁盘使用率

## 🔌 API接口设计

### 核心接口列表

#### 文件处理接口
- `POST /api/upload` - 文件上传
- `POST /api/analyze` - 单文件分析
- `POST /api/batch-analyze` - 批量分析
- `POST /api/files/<id>/reanalyze` - 重新分析

#### 记录管理接口
- `GET /api/records` - 获取分析记录列表
- `GET /api/records/<id>` - 获取单个记录详情
- `PUT /api/files/<id>/status` - 更新文件状态
- `POST /api/files/<id>/tags` - 文件打标

#### 用户认证接口
- `POST /api/register` - 用户注册
- `POST /api/login` - 用户登录
- `POST /api/logout` - 用户登出
- `GET /api/userinfo` - 获取用户信息

#### 审核管理接口
- `GET /api/admin/review/pending` - 获取待复核列表
- `POST /api/admin/review/<id>` - 提交复核结果
- `POST /api/admin/review/auto-approve-all` - 一键自动复核

#### 系统配置接口
- `GET /api/dashboard/stats` - 仪表盘统计数据
- `GET /api/models/configs` - 模型配置管理
- `GET /api/prompts/<type>/versions` - 提示词版本管理
- `POST /api/mock/data/save` - 挡板数据保存

## 🚀 部署要求

### 系统环境
- **操作系统**: Linux/Windows
- **Python版本**: 3.8+
- **数据库**: MySQL 8.0+
- **内存**: 8GB+ (推荐16GB)
- **存储**: 100GB+ (根据文件量调整)

### 依赖包
```bash
pip install -r requirements.txt
```

### 启动命令
```bash
python app.py
```

### Docker部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5105
CMD ["python", "app.py"]
```

## 📈 功能特色

### 1. 智能化程度高
- AI自动识别文档内容
- 智能对比验证结果
- 自动计算准确率评分

### 2. 业务场景全面
- 支持6大金融业务场景
- 可扩展的业务类型配置
- 灵活的提示词管理

### 3. 用户体验优秀
- 直观的拖拽上传
- 实时的处理进度
- 美观的结果展示

### 4. 管理功能完善
- 完整的用户权限体系
- 灵活的审核流程
- 丰富的数据导出

### 5. 开发测试友好
- 挡板模式支持
- 可视化数据编辑
- 完整的日志记录

## 🎯 开发优先级

### P0 - 核心功能 (第1-2周)
1. **基础框架搭建**
   - Flask应用初始化
   - 数据库连接和模型定义
   - 基础路由和模板

2. **文件处理核心**
   - 文件上传功能
   - PDF转图片处理
   - AI识别调用

3. **用户认证系统**
   - 用户注册/登录
   - 权限控制
   - 会话管理

### P1 - 重要功能 (第3-4周)
1. **业务分析流程**
   - 6大业务场景支持
   - 系统查询服务
   - 结果对比分析

2. **记录管理**
   - 分析记录存储
   - 记录查询和展示
   - 批量分析功能

3. **挡板模式**
   - 挡板数据管理
   - 可视化编辑界面
   - 模式切换功能

### P2 - 增强功能 (第5-6周)
1. **仪表盘系统**
   - 关键指标展示
   - 图表组件集成
   - 实时数据刷新

2. **审核管理**
   - 复核流程
   - 状态管理
   - 批量操作

3. **系统配置**
   - 提示词管理
   - 模型配置
   - 系统设置

## 🔍 关键技术点

### 1. AI集成
```python
# AI调用示例
def call_ai_service(image_paths, analysis_type):
    prompt = get_prompt_by_type(analysis_type)
    response = openai.ChatCompletion.create(
        model="qwen3-32b",
        messages=[
            {"role": "system", "content": prompt},
            {"role": "user", "content": [
                {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                for base64_image in encode_images(image_paths)
            ]}
        ]
    )
    return parse_ai_response(response)
```

### 2. 文件处理
```python
# PDF转图片处理
def convert_pdf_to_images(pdf_path):
    doc = fitz.open(pdf_path)
    images = []
    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        pix = page.get_pixmap()
        img_data = pix.tobytes("png")
        images.append(img_data)
    return images
```

### 3. 数据库操作
```python
# 使用SQLAlchemy ORM
class DatabaseService:
    def save_analysis_record(self, **kwargs):
        record = AnalysisRecord(**kwargs)
        db.session.add(record)
        db.session.commit()
        return record.id

    def get_records_by_user(self, user_id, page=1, per_page=20):
        return AnalysisRecord.query.filter_by(
            created_by=user_id
        ).paginate(page=page, per_page=per_page)
```

## 📚 开发文档清单

为了让AI完全理解您的需求，建议提供以下文档：

### 必需文档 (⭐⭐⭐)
1. **系统完整需求文档.md** - 本文档，包含完整的功能需求
2. **API接口文档.md** - 详细的API接口规范
3. **数据库设计文档** - 完整的数据表结构和关系
4. **前端样式文档.md** - UI/UX设计规范

### 重要文档 (⭐⭐)
5. **功能实现计划.md** - 开发计划和优先级
6. **系统设计文档.md** - 技术架构和设计思路
7. **仪表盘设计方案.md** - 仪表盘详细设计

### 参考文档 (⭐)
8. **现有代码示例** - 关键功能的代码实现
9. **配置文件示例** - config.py, requirements.txt
10. **部署文档** - Docker配置和部署说明

---

> 本文档详细描述了多场景智能化文档分析系统的完整需求，包括业务场景、技术架构、数据库设计、功能模块、API接口、开发优先级等各个方面，为系统重新开发提供全面的参考依据。配合其他技术文档，足以让AI完全理解并重新实现整个系统。

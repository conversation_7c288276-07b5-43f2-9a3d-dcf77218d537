# 多场景智能化文档分析系统设计文档

## 1. 系统概述

### 1.1 系统简介

多场景智能化文档分析系统是一个基于 Flask 的 Web 应用程序，主要用于处理各类金融文档的智能分析。系统支持期货账户、理财产品、券商计息变更、期货交易会员、宁银费用变更、产品说明书等多种业务场景，通过 AI 识别、系统查询和结果对比，为用户提供准确的文档分析服务。

### 1.2 系统目标

- 提供文档智能分析服务
- 支持多种业务场景的文档处理
- 实现 AI 识别与系统数据的对比验证
- 提供完整的分析记录管理
- 支持挡板模式便于开发测试和数据模拟
- 支持挡板数据自定义，前端可视化编辑
- 支持批量分析与进度提示

## 2. 系统架构

### 2.1 技术栈

- 后端框架: Flask
- 数据库: MySQL（已全面替换，原有SQLite兼容保留说明）
- 文件处理: PDF 转图片、图片处理
- AI服务: OpenAI/本地模型
- 前端: 原生HTML+CSS+JS
- 跨域支持: Flask-CORS

### 2.2 系统组件

1. 核心服务组件
   - DatabaseService: 数据库服务
   - BowangService: 系统查询服务
   - ComparisonService: 结果对比服务
2. 工具组件
   - util_img_process: 图片处理工具
   - util_ai: AI 工具类

### 2.3 目录结构

```
nb_bank_ai_poc/
├── uploads/                # 上传文件存储
├── processed/              # 处理后文件
├── static/                 # 静态资源
├── templates/              # 前端模板
├── app.py                  # 主应用程序
├── models.py               # 数据模型
├── bowang_service.py       # 系统查询服务
├── comparison_service.py   # 结果对比服务
├── ...                     # 其他工具/脚本
```

## 3. 核心功能设计

### 3.1 文件处理流程

1. 文件上传
   - 文件类型/大小校验，自动命名，按类型存储
2. 文件分析
   - PDF 转图片(如需)
   - AI 识别处理
   - 系统数据查询
   - 结果对比分析
   - 记录保存
   - 支持批量分析，分析进度提示常亮，分析结束后自动消失
3. 结果管理
   - 分析记录存储与查询
   - 历史记录管理
   - 文件清理
4. 批量分析
   - 支持多文件批量分析，进度提示常亮，结果可逐个查看

### 3.2 挡板模式设计

1. 配置管理
   - 全局开关控制
   - 业务类型配置
   - 模拟数据管理
   - 挡板数据自定义：前端页面支持弹窗编辑、格式化、重置、保存当前挡板数据，数据持久化到数据库，分析时优先使用自定义数据
2. 数据模型设计

#### 3.3.1 分析记录(analysis_records)
| 字段名           | 类型         | 说明           |
|------------------|--------------|----------------|
| id               | INT          | 主键，自增     |
| filename         | VARCHAR      | 文件名         |
| analysis_type    | VARCHAR      | 分析类型       |
| ai_result        | TEXT         | AI识别结果（JSON）|
| system_result    | TEXT         | 系统数据（JSON）|
| comparison_result| TEXT         | 对比结果（JSON）|
| use_mock         | BOOLEAN      | 是否用挡板     |
| created_at       | TIMESTAMP    | 创建时间       |

#### 3.3.2 挡板配置(mock_configs)
| 字段名      | 类型      | 说明           |
|-------------|-----------|----------------|
| id          | INT       | 主键，自增     |
| query_type  | VARCHAR   | 分析类型       |
| enabled     | BOOLEAN   | 是否启用挡板   |
| mock_data   | TEXT      | 挡板数据（JSON）|
| created_at  | TIMESTAMP | 创建时间       |
| updated_at  | TIMESTAMP | 更新时间       |

#### 3.3.3 全局设置(global_settings)
| 字段名      | 类型      | 说明           |
|-------------|-----------|----------------|
| id          | INT       | 主键，自增     |
| key         | VARCHAR   | 配置项         |
| value       | TEXT      | 配置值         |
| created_at  | TIMESTAMP | 创建时间       |
| updated_at  | TIMESTAMP | 更新时间       |

## 4. 前端设计要点

- 采用卡片式、对比式、表格式等多种展示方式
- AI识别内容与系统内容对比窗口美观，风格与对比窗口一致
- 批量分析结果可逐个查看
- 挡板数据自定义弹窗支持JSON编辑、格式化、重置、保存
- 分析进度提示常亮，分析结束后自动消失

## 5. 安全性设计

- 文件类型/大小限制，路径隔离
- CORS保护，参数校验，错误处理，日志记录
- 数据备份，历史保护，敏感信息脱敏

## 6. 部署说明

- Python 3.x，依赖见 requirements.txt（如需MySQL请确保已安装mysqlclient等相关依赖）
- MySQL 数据库
- 启动：python app.py
- Docker 支持：docker build --platform linux/amd64 -t {镜像名称:版本号} .

## 7. 开发与运维规范

- 代码风格统一，注释清晰，PEP8
- 每次AI执行前后需git add/commit，注释用中文
- 文档及时更新，结构清晰，Markdown格式
- 日志监控、性能监控、错误告警、容量规划

## 8. 后续优化建议
- 增加更多业务类型支持
- 分析结果可视化增强
- 支持多模型切换与配置
- 前端交互体验持续优化

---
补充说明：
1. 挡板数据自定义功能已上线，详见“挡板模式设计”章节。
2. 批量分析、进度提示、AI/系统内容窗口美化等功能已集成，详见相关章节。
3. 数据库表结构如需MySQL建表SQL，可参考init_bowang_mysql.sql。

---

## 9. 增量补充说明（2024年6月）

### 9.1 用户认证与权限管理
- 系统集成 Flask-Login，实现用户注册、登录、登出、权限校验。
- 用户表（users）包含用户名、密码哈希、角色、创建时间。
- 重要操作（如分析、导出、审核、挡板管理）需登录后方可访问。

### 9.2 多模型配置与切换
- 支持多种大模型（如qwen3-32b、openai-gpt4等），配置项包括key、url、model_name。
- 可通过API动态切换当前激活模型，支持模拟调用效果。
- 配置文件详见config.py。

### 9.3 审核与导出功能
- 分析记录支持人工审核，审核状态与备注可通过API提交。
- 支持分析结果导出为Excel、PDF，导出接口需登录。

### 9.4 提示词配置
- 所有AI分析提示词（system_prompt、img_prompt等）均支持数据库配置化，支持前端可视化编辑、版本管理。
- 提示词配置表（prompt_config）结构详见README_新功能说明.md。

### 9.5 客户系统模拟数据
- 支持本地客户系统模拟数据表（customer_system_data），分析时优先查本地数据。
- 挡板数据表（mock_data）支持参数化key和默认key，便于灵活模拟。

### 9.6 主要API接口一览
- 用户注册/登录/登出/信息：/api/register, /api/login, /api/logout, /api/userinfo
- 文件上传/分析/批量分析：/api/upload, /api/analyze, /api/batch-analyze
- 记录查询/审核/导出：/api/records, /api/records/<id>, /api/records/<id>/audit, /api/export/excel, /api/export/pdf
- 挡板与Mock管理：/api/mock-mode, /api/mock-data, /api/mock/data/<type>, /api/mock/data/save
- 模型管理：/api/model/active

### 9.7 其他
- 所有数据库变更均遵循历史数据保护原则，新增字段需默认值并兼容旧数据。
- 详细API参数、返回值、示例见《API接口文档.md》。

---

## 10. 系统重大功能升级（2024年12月）

### 10.1 首页仪表盘设计

#### 10.1.1 核心指标展示
- **识别准确率**: 实时计算各业务场景的AI识别准确率
- **处理统计**: 今日/本周/本月处理文件数量统计
- **业务分布**: 各业务类型文件分布饼图
- **趋势分析**: 近30天处理量和准确率趋势图

#### 10.1.2 数据模型扩展
```sql
-- 仪表盘统计表
CREATE TABLE dashboard_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    stat_date DATE NOT NULL,
    analysis_type VARCHAR(50),
    total_files INT DEFAULT 0,
    processed_files INT DEFAULT 0,
    accuracy_rate DECIMAL(5,4) DEFAULT 0,
    avg_processing_time DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_date_type (stat_date, analysis_type)
);
```

### 10.2 文件管理增强

#### 10.2.1 文件状态管理
- **状态类型**: active（活跃）、deprecated（已废弃）、archived（已归档）
- **状态变更**: 废弃按钮 ↔ 恢复按钮，醒目的UI设计
- **权限控制**: 只有管理员和分析师可以废弃文件

#### 10.2.2 文件打标系统
```sql
-- 文件标签表
CREATE TABLE file_tags (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_id INT NOT NULL,
    tag_name VARCHAR(50) NOT NULL,
    tag_color VARCHAR(7) DEFAULT '#2563eb',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES analysis_records(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE KEY unique_file_tag (file_id, tag_name)
);
```

#### 10.2.3 文件去重机制
- **哈希计算**: 基于文件内容计算MD5/SHA256哈希值
- **相似度检测**: 使用文件名、大小、创建时间等特征
- **重复处理**: 提示用户重复文件，可选择跳过或强制上传

### 10.3 提示词版本管理

#### 10.3.1 版本控制设计
```sql
-- 提示词版本表
CREATE TABLE prompt_versions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,
    prompt_content TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INT DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES users(id),
    UNIQUE KEY unique_type_version (analysis_type, version),
    INDEX idx_type_active (analysis_type, is_active)
);
```

#### 10.3.2 版本管理功能
- **版本创建**: 基于当前版本创建新版本，支持描述和变更说明
- **版本切换**: 可随时切换到历史版本，支持A/B测试
- **版本对比**: 显示不同版本间的差异和性能对比
- **自动备份**: 每次修改自动创建版本备份

### 10.4 管理员复核系统

#### 10.4.1 复核流程设计
1. **待复核队列**: 按优先级、时间排序的待复核文件列表
2. **复核界面**: 并列显示废弃按钮和复核按钮
3. **自动跳转**: 复核完成后自动展示下一个待复核文件
4. **批量复核**: 支持一键全部自动复核功能

#### 10.4.2 复核数据模型
```sql
-- 复核记录表
CREATE TABLE review_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    record_id INT NOT NULL,
    reviewer_id INT NOT NULL,
    review_status ENUM('approved', 'rejected', 'needs_revision') NOT NULL,
    review_comment TEXT,
    corrections JSON,
    review_time DECIMAL(8,2) COMMENT '复核耗时(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (record_id) REFERENCES analysis_records(id),
    FOREIGN KEY (reviewer_id) REFERENCES users(id),
    INDEX idx_record_status (record_id, review_status)
);
```

### 10.5 挡板数据编辑系统

#### 10.5.1 编辑功能设计
- **场景限制**: 仅非标交易确认单解析、券商账户计息变更支持挡板编辑
- **编辑界面**: 可视化编辑器，支持JSON格式化和语法高亮
- **标准答案**: 编辑后的结果可保存为标准答案，用于后续对比
- **版本管理**: 挡板数据支持版本控制和历史回溯

#### 10.5.2 标准答案数据模型
```sql
-- 标准答案表
CREATE TABLE standard_answers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    analysis_type VARCHAR(50) NOT NULL,
    file_pattern VARCHAR(255) COMMENT '文件名模式',
    standard_result JSON NOT NULL,
    confidence_score DECIMAL(5,4) DEFAULT 1.0,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INT DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_type_pattern (analysis_type, file_pattern)
);
```

### 10.6 PDF原件展示功能

#### 10.6.1 PDF处理能力
- **在线预览**: 支持PDF文件在线预览，无需下载
- **页面导航**: 支持多页PDF的页面切换和缩放
- **标注功能**: 支持在PDF上标注关键信息区域
- **对比展示**: PDF原件与识别结果并排显示

#### 10.6.2 文件存储优化
```sql
-- 文件信息扩展
ALTER TABLE analysis_records ADD COLUMN file_info JSON COMMENT '文件详细信息';
-- 示例JSON结构:
-- {
--   "file_size": 1024000,
--   "page_count": 5,
--   "file_type": "pdf",
--   "preview_generated": true,
--   "thumbnail_path": "/thumbnails/xxx.jpg"
-- }
```

### 10.7 大模型配置管理

#### 10.7.1 配置管理界面
- **模型列表**: 显示所有配置的大模型及其状态
- **连接测试**: 实时测试模型连接状态和响应时间
- **配置编辑**: 支持添加、编辑、删除模型配置
- **预留字段**: 模型地址、模型名称、API Key等配置项

#### 10.7.2 模型配置数据模型
```sql
-- 模型配置表
CREATE TABLE model_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    model_id VARCHAR(50) UNIQUE NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    api_url VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) NOT NULL,
    vision_model VARCHAR(100),
    timeout INT DEFAULT 30,
    max_tokens INT DEFAULT 4096,
    temperature DECIMAL(3,2) DEFAULT 0.7,
    is_active BOOLEAN DEFAULT FALSE,
    last_test_at TIMESTAMP NULL,
    test_status ENUM('success', 'failed', 'pending') DEFAULT 'pending',
    response_time DECIMAL(8,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 10.8 导出功能增强

#### 10.8.1 多Sheet Excel导出
- **文件结构**: 一个分析记录对应一个Sheet页
- **Sheet命名**: 支持按文件名、记录ID、自定义规则命名
- **内容组织**: 每个Sheet包含AI结果、系统结果、对比结果
- **格式优化**: 统一的表格样式和颜色标识

### 10.9 系统风格统一

#### 10.9.1 UI/UX标准化
- **色彩规范**: 统一的商务蓝色调（#2563eb）
- **组件库**: 标准化的按钮、表格、卡片、弹窗组件
- **交互规范**: 统一的悬停效果、动画过渡、反馈机制
- **响应式设计**: 适配桌面、平板、移动端的布局

#### 10.9.2 品牌标识
- **Logo替换**: 支持上传公司专属Logo
- **品牌色彩**: 可配置的主题色彩方案
- **字体规范**: 统一的字体族和字号体系

### 10.10 性能与安全优化

#### 10.10.1 性能优化
- **缓存机制**: 仪表盘数据、统计信息缓存
- **异步处理**: 文件分析、批量操作异步化
- **数据库优化**: 索引优化、查询优化
- **前端优化**: 组件懒加载、图片压缩

#### 10.10.2 安全增强
- **权限细化**: 基于角色的细粒度权限控制
- **操作审计**: 关键操作的完整审计日志
- **数据加密**: 敏感数据加密存储
- **访问控制**: IP白名单、访问频率限制

---

## 11. 部署与运维

### 11.1 系统要求
- **Python**: 3.8+
- **数据库**: MySQL 8.0+
- **内存**: 8GB+（推荐16GB）
- **存储**: 100GB+（根据文件量调整）
- **网络**: 稳定的互联网连接（访问AI模型）

### 11.2 部署方式
- **Docker部署**: 推荐使用Docker容器化部署
- **传统部署**: 支持直接在服务器上部署
- **云部署**: 支持AWS、阿里云等云平台部署

### 11.3 监控与维护
- **健康检查**: 系统组件状态监控
- **性能监控**: 响应时间、资源使用率监控
- **日志管理**: 结构化日志记录和分析
- **备份策略**: 数据库定期备份和恢复测试

---

> 以上为2024年12月系统重大功能升级说明，涵盖了仪表盘、文件管理、版本控制、复核系统等核心新特性的详细设计。

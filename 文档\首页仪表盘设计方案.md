# 🏠 首页仪表盘设计方案

## 📊 整体布局设计

### 布局结构
```
┌─────────────────────────────────────────────────────────────────┐
│                        顶部关键指标卡片区                          │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐    │
│  │总处理量 │ │识别准确率│ │今日处理 │ │待复核数 │ │系统状态 │    │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘ └─────────┘    │
├─────────────────────────────────────────────────────────────────┤
│                        主要图表区域                              │
│  ┌─────────────────────┐ ┌─────────────────────┐                │
│  │   处理量趋势图       │ │   准确率趋势图       │                │
│  │   (折线图)          │ │   (折线图)          │                │
│  └─────────────────────┘ └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│                        业务分析区域                              │
│  ┌─────────────────────┐ ┌─────────────────────┐                │
│  │   业务类型分布       │ │   文件状态分布       │                │
│  │   (饼图)            │ │   (环形图)          │                │
│  └─────────────────────┘ └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│                        详细统计区域                              │
│  ┌─────────────────────┐ ┌─────────────────────┐                │
│  │   处理时长分布       │ │   错误类型统计       │                │
│  │   (柱状图)          │ │   (水平柱状图)       │                │
│  └─────────────────────┘ └─────────────────────┘                │
├─────────────────────────────────────────────────────────────────┤
│                        实时监控区域                              │
│  ┌─────────────────────┐ ┌─────────────────────┐                │
│  │   实时处理队列       │ │   系统性能监控       │                │
│  │   (表格)            │ │   (仪表盘)          │                │
│  └─────────────────────┘ └─────────────────────┘                │
└─────────────────────────────────────────────────────────────────┘
```

## 📈 详细图表设计

### 1. 顶部关键指标卡片 (KPI Cards)

#### 1.1 总处理量卡片
```json
{
  "title": "总处理量",
  "value": "12,580",
  "change": "+8.5%",
  "period": "较上月",
  "icon": "📄",
  "color": "#2563eb"
}
```

#### 1.2 识别准确率卡片
```json
{
  "title": "识别准确率",
  "value": "94.5%",
  "change": "+2.1%",
  "period": "较上月",
  "icon": "🎯",
  "color": "#10b981"
}
```

#### 1.3 今日处理卡片
```json
{
  "title": "今日处理",
  "value": "156",
  "change": "+12",
  "period": "较昨日",
  "icon": "📊",
  "color": "#f59e0b"
}
```

#### 1.4 待复核数量卡片
```json
{
  "title": "待复核",
  "value": "23",
  "change": "-5",
  "period": "较昨日",
  "icon": "⏳",
  "color": "#ef4444"
}
```

#### 1.5 系统状态卡片
```json
{
  "title": "系统状态",
  "value": "正常",
  "uptime": "99.8%",
  "period": "本月可用率",
  "icon": "✅",
  "color": "#06b6d4"
}
```

### 2. 处理量趋势图 (折线图)

#### 2.1 数据维度
- **时间维度**: 近30天/近7天/近24小时
- **业务维度**: 各业务类型的处理量趋势
- **对比维度**: 同比/环比数据对比

#### 2.2 图表配置
```javascript
{
  "type": "line",
  "title": "文件处理量趋势",
  "xAxis": "日期",
  "yAxis": "处理数量",
  "series": [
    {
      "name": "期货开户",
      "data": [45, 52, 38, 67, 73, 89, 94],
      "color": "#2563eb"
    },
    {
      "name": "券商计息",
      "data": [23, 28, 31, 42, 38, 45, 52],
      "color": "#10b981"
    },
    {
      "name": "非标交易",
      "data": [12, 15, 18, 22, 25, 28, 31],
      "color": "#f59e0b"
    }
  ]
}
```

### 3. 识别准确率趋势图 (折线图)

#### 3.1 数据维度
- **准确率变化**: 按日期显示准确率变化
- **业务对比**: 不同业务类型的准确率对比
- **目标线**: 显示准确率目标线(如95%)

#### 3.2 图表配置
```javascript
{
  "type": "line",
  "title": "识别准确率趋势",
  "xAxis": "日期",
  "yAxis": "准确率(%)",
  "targetLine": 95,
  "series": [
    {
      "name": "整体准确率",
      "data": [92.5, 93.2, 94.1, 93.8, 94.5, 95.2, 94.8],
      "color": "#2563eb"
    },
    {
      "name": "期货开户",
      "data": [94.2, 94.8, 95.1, 94.9, 95.5, 96.1, 95.8],
      "color": "#10b981"
    },
    {
      "name": "券商计息",
      "data": [91.8, 92.5, 93.2, 92.9, 93.6, 94.3, 93.9],
      "color": "#f59e0b"
    }
  ]
}
```

### 4. 业务类型分布图 (饼图)

#### 4.1 数据维度
```javascript
{
  "type": "pie",
  "title": "业务类型分布",
  "data": [
    {"name": "期货开户", "value": 4580, "percentage": 45.8, "color": "#2563eb"},
    {"name": "券商计息", "value": 2890, "percentage": 28.9, "color": "#10b981"},
    {"name": "非标交易", "value": 1650, "percentage": 16.5, "color": "#f59e0b"},
    {"name": "理财产品", "value": 890, "percentage": 8.9, "color": "#ef4444"}
  ]
}
```

### 5. 文件状态分布图 (环形图)

#### 5.1 数据维度
```javascript
{
  "type": "doughnut",
  "title": "文件状态分布",
  "data": [
    {"name": "已完成", "value": 8950, "percentage": 71.2, "color": "#10b981"},
    {"name": "处理中", "value": 1580, "percentage": 12.6, "color": "#f59e0b"},
    {"name": "待复核", "value": 1230, "percentage": 9.8, "color": "#ef4444"},
    {"name": "已废弃", "value": 820, "percentage": 6.5, "color": "#6b7280"}
  ]
}
```

### 6. 处理时长分布图 (柱状图)

#### 6.1 数据维度
```javascript
{
  "type": "bar",
  "title": "处理时长分布",
  "xAxis": "时长区间",
  "yAxis": "文件数量",
  "data": [
    {"range": "0-30s", "count": 3250, "percentage": 65.0},
    {"range": "30s-1min", "count": 980, "percentage": 19.6},
    {"range": "1-2min", "count": 520, "percentage": 10.4},
    {"range": "2-5min", "count": 180, "percentage": 3.6},
    {"range": ">5min", "count": 70, "percentage": 1.4}
  ]
}
```

### 7. 错误类型统计图 (水平柱状图)

#### 7.1 数据维度
```javascript
{
  "type": "horizontalBar",
  "title": "常见错误类型统计",
  "xAxis": "错误次数",
  "yAxis": "错误类型",
  "data": [
    {"type": "图片质量问题", "count": 145, "percentage": 35.2},
    {"type": "字段识别错误", "count": 98, "percentage": 23.8},
    {"type": "格式不匹配", "count": 76, "percentage": 18.4},
    {"type": "系统查询失败", "count": 52, "percentage": 12.6},
    {"type": "网络超时", "count": 41, "percentage": 10.0}
  ]
}
```

### 8. 实时处理队列表格

#### 8.1 表格结构
```javascript
{
  "type": "table",
  "title": "实时处理队列",
  "columns": [
    {"key": "filename", "title": "文件名", "width": "30%"},
    {"key": "type", "title": "业务类型", "width": "15%"},
    {"key": "status", "title": "状态", "width": "15%"},
    {"key": "progress", "title": "进度", "width": "20%"},
    {"key": "startTime", "title": "开始时间", "width": "20%"}
  ],
  "data": [
    {
      "filename": "期货开户_20241218_001.pdf",
      "type": "期货开户",
      "status": "处理中",
      "progress": "75%",
      "startTime": "10:25:30"
    }
  ]
}
```

### 9. 系统性能监控仪表盘

#### 9.1 性能指标
```javascript
{
  "type": "gauge",
  "title": "系统性能监控",
  "metrics": [
    {
      "name": "CPU使用率",
      "value": 45,
      "max": 100,
      "unit": "%",
      "color": "#10b981",
      "threshold": [70, 90]
    },
    {
      "name": "内存使用率",
      "value": 62,
      "max": 100,
      "unit": "%",
      "color": "#f59e0b",
      "threshold": [80, 95]
    },
    {
      "name": "磁盘使用率",
      "value": 38,
      "max": 100,
      "unit": "%",
      "color": "#2563eb",
      "threshold": [85, 95]
    }
  ]
}
```

## 📊 新增图表建议

### 10. 用户活跃度热力图
```javascript
{
  "type": "heatmap",
  "title": "用户活跃度热力图",
  "description": "显示一周内各时段的用户活跃情况",
  "xAxis": ["00:00", "02:00", "04:00", "06:00", "08:00", "10:00", "12:00", "14:00", "16:00", "18:00", "20:00", "22:00"],
  "yAxis": ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
  "data": "二维数组，表示各时段的活跃度"
}
```

### 11. 模型性能对比雷达图
```javascript
{
  "type": "radar",
  "title": "AI模型性能对比",
  "description": "对比不同模型在各维度的表现",
  "dimensions": ["准确率", "处理速度", "稳定性", "成本效益", "易用性"],
  "series": [
    {
      "name": "Qwen3-32B",
      "data": [95, 85, 92, 78, 88]
    },
    {
      "name": "GPT-4",
      "data": [98, 75, 95, 65, 92]
    }
  ]
}
```

### 12. 文件大小分布直方图
```javascript
{
  "type": "histogram",
  "title": "文件大小分布",
  "description": "显示上传文件的大小分布情况",
  "xAxis": "文件大小(MB)",
  "yAxis": "文件数量",
  "bins": ["0-1", "1-5", "5-10", "10-20", "20-50", ">50"]
}
```

### 13. 复核效率统计图
```javascript
{
  "type": "combination",
  "title": "复核效率统计",
  "description": "复核数量与平均复核时间的组合图",
  "leftAxis": "复核数量",
  "rightAxis": "平均时间(分钟)",
  "series": [
    {
      "type": "bar",
      "name": "复核数量",
      "yAxisIndex": 0
    },
    {
      "type": "line",
      "name": "平均时间",
      "yAxisIndex": 1
    }
  ]
}
```

### 14. 准确率分级分布图
```javascript
{
  "type": "stackedBar",
  "title": "准确率分级分布",
  "description": "按准确率等级显示文件分布",
  "categories": ["期货开户", "券商计息", "非标交易", "理财产品"],
  "series": [
    {"name": "优秀(95%+)", "color": "#10b981"},
    {"name": "良好(90-95%)", "color": "#f59e0b"},
    {"name": "一般(80-90%)", "color": "#ef4444"},
    {"name": "较差(<80%)", "color": "#6b7280"}
  ]
}
```

### 15. 月度对比瀑布图
```javascript
{
  "type": "waterfall",
  "title": "月度处理量变化",
  "description": "显示各业务类型对总处理量变化的贡献",
  "data": [
    {"name": "上月总量", "value": 8500, "type": "start"},
    {"name": "期货开户", "value": 450, "type": "positive"},
    {"name": "券商计息", "value": -120, "type": "negative"},
    {"name": "非标交易", "value": 280, "type": "positive"},
    {"name": "理财产品", "value": 90, "type": "positive"},
    {"name": "本月总量", "value": 9200, "type": "end"}
  ]
}
```

## 🎨 视觉设计规范

### 颜色方案
```css
:root {
  --primary-blue: #2563eb;
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --danger-red: #ef4444;
  --info-cyan: #06b6d4;
  --neutral-gray: #6b7280;
  
  /* 渐变色 */
  --gradient-blue: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-green: linear-gradient(135deg, #10b981 0%, #059669 100%);
}
```

### 卡片样式
```css
.dashboard-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 32px rgba(10,31,68,0.06);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 40px rgba(24,144,255,0.10);
}
```

## 📱 响应式适配

### 移动端布局
- 卡片改为单列布局
- 图表高度自适应
- 表格支持横向滚动
- 简化复杂图表显示

### 平板端布局
- 2列卡片布局
- 图表保持原有比例
- 侧边栏可收缩

## 🔄 数据刷新策略

### 实时数据
- 处理队列: 5秒刷新
- 系统状态: 10秒刷新
- 性能监控: 30秒刷新

### 统计数据
- KPI卡片: 5分钟刷新
- 趋势图表: 10分钟刷新
- 分布图表: 30分钟刷新

---

> 这个仪表盘设计方案提供了15个不同类型的图表和统计展示，全面覆盖了系统的各个维度，既有宏观的趋势分析，也有微观的实时监控，能够为用户提供完整的系统运行状况视图。

{"cells": [{"cell_type": "code", "execution_count": 1, "id": "57fe6775", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:45:51.022603Z", "start_time": "2025-06-03T08:45:50.981234Z"}, "code_folding": [19, 30, 54, 74, 88, 104, 150]}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\nb_remoteMineruAPI_v1.7\\nb_bank_ai_poc\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import *\n", "from mineru_pdf import *\n", "import json\n", "from util_img_process import process_pdf\n", "import hashlib\n", "from func import convert_images_to_pdf"]}, {"cell_type": "code", "execution_count": null, "id": "ffac0a1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[DEBUG] 正在调用远程服务: http://**************:30000/file_parse\n", "[DEBUG] 文件路径: app/券商计息变更脱敏/中泰期货利率批量返息函********.pdf_1745473851164.pdf\n", "[DEBUG] 请求参数: {'return_md': 'true', 'return_images': 'true', 'return_middle_json': 'true', 'return_model_output': 'false', 'return_content_list': 'false', 'start_page_id': '0', 'end_page_id': '99999', 'parse_method': 'ocr', 'lang_list': 'ch', 'output_dir': './output', 'backend': 'vlm-sglang-engine', 'table_enable': 'true', 'formula_enable': 'true'}\n", "[DEBUG] 响应状态码: 200\n", "[DEBUG] 远程服务返回结果类型: <class 'dict'>\n"]}], "source": ["\n", "fn = \"app/券商计息变更脱敏/中泰期货利率批量返息函********.pdf_1745473851164.pdf\"\n", "# 将pdf转为图片形式的pdf,以启用ocr\n", "\n", "# 强制OCR\n", "markdown_data = PDFMarkdownConverter.trans_pdf_to_markdown(fn, parse_method='ocr', backend='vlm-sglang-engine')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 31, "id": "c9e14cf7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 中泰期货股份有限公司\n", "\n", "# 关于 期货保证金账户可用资金年化收益率的说明函\n", "\n", "汇添富基金管理股份有限公司：\n", "\n", "贵司在我司开立期货账户,账号为22900711、22920228、22920109、22900667、22920229、22900721、22900720、22900719、22900716、22900715和22900707。按商议收费标准,期货保证金账户内的可用资金自2025年1月2日按年利率  $1.4\\%$  计息(该利率会随市场利率的调整而调整)并按月支付,于每月20号之前支付上月利息到相应期货账户中,对其他权益不支付利息。\n", "\n", "利息要素如下:\n", "\n", "<table><tr><td>客户号</td><td>资产名称</td><td>每日账户可用资金(非质押资金)计息利率</td><td>计息起始日</td></tr><tr><td>22900711</td><td>汇添富远景成长一年持有期混合型证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22920228</td><td>汇添富稳利60天滚动持有短债债券型证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22920109</td><td>汇添富成长领先混合型证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22900667</td><td>汇添富北证50成份指数型证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22900707</td><td>汇添富中证800指数增强型证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22900715</td><td>汇添富中证2000交易型开放式指数证券投资基金</td><td>年化1.4%</td><td>2025年1月2日</td></tr><tr><td>22900716</td><td>汇添富稳益60天持有期债券</td><td>年化1.4%</td><td>2025年1月2日</td></tr></table>\n", "\n", "<table><tr><td></td><td>型证券投资基金</td><td></td><td></td></tr><tr><td>22900719</td><td>汇添富中证信息技术应用创新产业交易型开放式指数证券投资基金</td><td>年化 1.4%</td><td>2025 年 1 月 2 日</td></tr><tr><td>22900720</td><td>汇添富添添乐双鑫债券型证券投资基金</td><td>年化 1.4%</td><td>2025 年 1 月 2 日</td></tr><tr><td>22900721</td><td>汇添富中短债债券型证券投资基金</td><td>年化 1.4%</td><td>2025 年 1 月 2 日</td></tr><tr><td>22920229</td><td>汇添富稳福 60 天滚动持有中短债债券型证券投资基金</td><td>年化 1.4%</td><td>2025 年 1 月 2 日</td></tr></table>\n", "\n", "计息方式:每日利息  $=$  计息金额  $*$  申请年化利率/360。\n", "\n", "![](images/97522326c1b8823d5f923f838c1036189bab48002069dfa1d8c726de8b5bc71f.jpg)\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 32, "id": "926eaac7", "metadata": {}, "outputs": [], "source": ["sys_prompt = \"\"\"你是一位资深的银行托管部经理，负责从客户提供的文件（邮件、公告、合同、通知等）中**抽取基金计息变更要素**，并按以下要求结构化输出 JSON 数组。\n", "\n", "=====================【字段定义】=====================\n", "1. 【产品名称】\n", "   • 如果正文**出现了具体的基金、系列基金**名称，就逐一提取这些名称；  \n", "   • 只有在正文出现 “本公司全部产品 / 全公司所有基金 / X X 公司所有产品”等措辞，**且未列举任何具体产品**时，才将“X X 公司”作为【产品名称】，并认定为“全公司产品”。\n", "\n", "2. 【产品类别】枚举值：单产品 / 系列产品 / 全公司产品  \n", "   • 判断依据同上：列举多只不同基金 ⇒ “单产品”（多行输出）；  \n", "     明确写“系列基金 / 系列产品” ⇒ “系列产品”；  \n", "     出现“全公司全部产品”且未列举 ⇒ “全公司产品”。\n", "\n", "3. 【利率(年化)】——*嵌套 JSON*  \n", "   用 **键值对** 表示分段利率，遵循以下规则：  \n", "   • **单一利率** → `{\"all\": \"X.X%\"}`  \n", "   • **按客户类型分段** → 外层键为 “个人”“非个人”“机构”等；值可以是：  \n", "     - 单一值 → `\"0.10%\"`  \n", "     - **再按日期分段** → 内层对象，键格式：  \n", "       - `START:YYYY-MM-DD` 表示 *起始至该日（含）*  \n", "       - `YYYY-MM-DD:END` 表示 *该日起（含）至结束*  \n", "       - `YYYY-MM-DD至YYYY-MM-DD` 表示闭区间  \n", "     例：`\"非个人\": {\"START:2024-11-10\": \"0.15%\", \"2024-11-11:END\": \"0.10%\"}`  \n", "   • **仅按日期分段**（不区分客户类型） → 直接用内层对象：  \n", "     `{\"START:2025-01-01\": \"1.5%\", \"2025-07-01:END\": \"1.2%\"}`  \n", "   • 多维分段时，优先按 **客户类型** 外层、**日期** 内层。\n", "\n", "4. 【开始时间】【截止时间】  \n", "   • 如文档给出单一“计息起始日”，则写入【开始时间】；【截止时间】留空，除非正文给出明确结束日期。  \n", "   • 如果分段计息，每条分段各自写一行 JSON（推荐）或在【利率(年化)】内注明日期区间并将最早日期写入【开始时间】，最晚日期写入【截止时间】。\n", "\n", "5. 【计息天数】  \n", "   • 常见值 360 / 365 / “实际天数”。若文档只出现“/360”或“/365”，直接填数字；若写“按实际天数”，填“实际天数”；否则留空。\n", "\n", "6. 【备注】  \n", "   • 放无法归入前述字段但对计息有影响的信息，如“利率随市场调整”“按月结息至期货账户”等。  \n", "   • 对于按客户类型分段的说明，也可以简要复述，以便人工复核。\n", "\n", "=====================【格式要求】=====================\n", "• 日期全部转为 `YYYY-MM-DD`。  \n", "• 跨页或 HTML 表格拆分的内容需合并后再识别。  \n", "• 输出 **JSON 数组**，字段顺序固定，示例如下：\n", "\n", "```json\n", "[\n", "  {\n", "    \"产品名称\": \"汇添富远景成长一年持有期混合型基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"国泰君安证券股份有限公司\",\n", "    \"产品类别\": \"全公司产品\",\n", "    \"利率(年化)\": {\"个人\": \"0.10%\", \"非个人\": {\"START:2024-11-10\": \"0.15%\", \"2024-11-11:END\": \"0.10%\"}},\n", "    \"开始时间\": \"2024-11-11\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": \"\",\n", "    \"备注\": \"经纪/两融/期权/贵金属保证金统一执行\"\n", "  }\n", "]\n", "```\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 33, "id": "c1b1fc6d", "metadata": {}, "outputs": [], "source": ["chat_bot = ChatBot(system_prompt=sys_prompt)\n", "\n", "res = chat_bot.chat(messages=[{\"role\": \"user\", \"content\": markdown_content}], top_p=0.5, temperature=0.3)"]}, {"cell_type": "code", "execution_count": 34, "id": "d2fb523c", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:51:03.141606Z", "start_time": "2025-06-03T08:51:03.135551Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "  {\n", "    \"产品名称\": \"汇添富远景成长一年持有期混合型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳利60天滚动持有短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富成长领先混合型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富北证50成份指数型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证800指数增强型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证2000交易型开放式指数证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳益60天持有期债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中证信息技术应用创新产业交易型开放式指数证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富添添乐双鑫债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富中短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  },\n", "  {\n", "    \"产品名称\": \"汇添富稳福60天滚动持有中短债债券型证券投资基金\",\n", "    \"产品类别\": \"单产品\",\n", "    \"利率(年化)\": {\"all\": \"1.4%\"},\n", "    \"开始时间\": \"2025-01-02\",\n", "    \"截止时间\": \"\",\n", "    \"计息天数\": 360,\n", "    \"备注\": \"按月20日前结息至期货账户；利率随市场利率调整\"\n", "  }\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": 4, "id": "0ae4b9f7", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T08:52:58.068316Z", "start_time": "2025-06-03T08:52:58.061326Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "[\n", "\t{\"客户号（账号）\": \"31006666108800297534\", \"资产名称\": \"创金合信泰盈双季红6个月定期开放债券型证券投资基金\", \"计息利率(年化)\": \"0.000%\", \"计息起始日\": \"2019年9月26日\"}\n", "]\n", "```\n"]}], "source": ["print(res)"]}, {"cell_type": "code", "execution_count": null, "id": "d8ea3f76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}
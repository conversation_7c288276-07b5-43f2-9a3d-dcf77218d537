{"cells": [{"cell_type": "code", "execution_count": 1, "id": "dd8d5298", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:57:14.448462Z", "start_time": "2025-06-03T01:57:14.430596Z"}}, "outputs": [], "source": ["import docx\n", "import json\n", "from docx.table import Table, _Cell, _Row\n", "from docx.text.paragraph import Paragraph\n", "import logging\n", "from docx.oxml import parse_xml\n", "from docx.oxml.ns import nsdecls\n", "from copy import deepcopy\n", "import re\n", "from docx.oxml.ns import qn\n", "import pandas as pd\n", "import os\n", "from pdf2docx import Converter\n", "import uuid\n", "import PyPDF2\n", "import tqdm\n"]}, {"cell_type": "code", "execution_count": 3, "id": "b8ccb5ec", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:52:33.808949Z", "start_time": "2025-06-03T01:52:33.801853Z"}, "scrolled": true}, "outputs": [], "source": ["def pdf_to_word_pdf2docx(pdf_path, word_path):\n", "    cv = Converter(pdf_path)\n", "    cv.convert(word_path, start=0, end=None)\n", "    cv.close()"]}, {"cell_type": "code", "execution_count": 6, "id": "0a10300d", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:41:28.587183Z", "start_time": "2025-06-03T01:41:28.565795Z"}, "code_folding": [0, 14, 45]}, "outputs": [], "source": ["def check_vertical_merge(cell):\n", "    tc = cell._tc\n", "    tc_pr = tc.get_or_add_tcPr()\n", "    vmerge = tc_pr.find(qn(\"w:vMerge\"))\n", "    \n", "    if vmerge is not None:\n", "        val = vmerge.get(qn(\"w:val\"))\n", "        # 特殊处理多个restart的情况\n", "        if val == \"restart\":\n", "            return \"restart\"\n", "        elif val == \"continue\":\n", "            return \"continue\"\n", "    return \"None\"\n", "\n", "def fix_merge_table(fix_table):\n", "    new_talbe = pd.DataFrame()\n", "    min_length = {}\n", "    for table_i, table in fix_table.groupby('table_id'):\n", "        if table_i == 0:\n", "            new_talbe = table.copy()\n", "            # 固化字段长度用以校验\n", "            for col in table.columns:\n", "                min_length[col] = table[col].iloc[:-1].astype(str).map(len).mean()\n", "        else:\n", "            first_row = table.iloc[0]\n", "            # 计算字段达成率\n", "            success_num = 0\n", "            for col in table.columns:\n", "                if len(str(first_row[col])) >= min_length[col]:\n", "                    success_num += 1\n", "            success_ratio = success_num / len(min_length)\n", "\n", "            # 如果字段达成率低于70%则合并\n", "            if success_ratio <= 0.7:\n", "                for col in table.columns:\n", "                    if col == 'table_id':\n", "                        continue\n", "                    fix_idx = new_talbe[new_talbe[col]!='!restart!'].iloc[-1].name\n", "                    new_talbe.loc[fix_idx, col] += first_row[col].strip() if isinstance(first_row[col], str) else first_row[col]\n", "                table = table.iloc[1:]\n", "            new_talbe = pd.concat([new_talbe, table], ignore_index=True)\n", "    fin_table = new_talbe.replace('!restart!', pd.NA).fillna(method='pad')\n", "    del fin_table['table_id']\n", "    return fin_table\n", "\n", "def df_to_json_rows(df):\n", "    json_list = df.to_dict(orient='records')\n", "    return json.dumps(json_list, indent=2, ensure_ascii=False)"]}, {"cell_type": "code", "execution_count": 7, "id": "9fab3879", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:58:25.037861Z", "start_time": "2025-06-03T01:58:25.016799Z"}, "code_folding": [13], "scrolled": true}, "outputs": [], "source": ["def pdf_to_markdown(pdf_fn):\n", "    temp_fn = f\"{uuid.uuid1().hex}.docx\"\n", "    # 使用示例\n", "    pdf_to_word_pdf2docx(pdf_fn, temp_fn)\n", "    # 加载Word文档\n", "    doc = docx.Document(temp_fn)\n", "    out_markdown = \"\"\n", "\n", "    table_list = []\n", "    now_table = None\n", "    header = []\n", "    child_i = 0\n", "    table_id = 0\n", "    for child in doc.element.body:\n", "    #     print(child_i, type(child))\n", "        if isinstance(child, docx.oxml.text.paragraph.CT_P):  # 段落\n", "            if child.text.replace('\\n', '').strip() == '':\n", "                continue\n", "            if now_table is not None:\n", "                print(child.text.replace('\\n', '').strip())\n", "                print(\"重置表格\")\n", "                out_markdown += \"\\n```json\\n\" + df_to_json_rows(fix_merge_table(now_table)) + \"\\n```\\n\"\n", "                now_table = None\n", "                header = []\n", "                table_id = 0\n", "            out_markdown += child.text.replace('\\n', '').strip() + '\\n'\n", "        elif isinstance(child, docx.oxml.table.CT_Tbl): # 表格\n", "            data = []\n", "            table = Table(child, doc)\n", "            last_tc = {}\n", "            for row in table.rows:\n", "                row_data = []\n", "                cols_id = 0\n", "                for cell in row.cells:\n", "                    tc = cell._tc  # 获取底层 XML 元素\n", "                    # 检查是否是合并单元格的后续行\n", "                    if check_vertical_merge(cell) == \"restart\":\n", "                        if last_tc.get(cols_id, None) is None:\n", "                            last_tc[cols_id] = tc\n", "                        else:\n", "                            row_data.append('!restart!')  # 非首行合并单元格，输出空\n", "                            continue\n", "\n", "                    # 否则提取文本并清理\n", "                    text = cell.text.strip().replace('\\n', '').replace('\\t', ' ')\n", "                    row_data.append(text)\n", "                    cols_id += 1\n", "                data.append(row_data)\n", "\n", "            if now_table is None:\n", "                header = data[0]\n", "                data = data[1:]\n", "            out_df = pd.DataFrame(data, columns=header)\n", "            out_df['table_id'] = table_id\n", "            if now_table is None:\n", "                now_table = out_df.copy()\n", "            else:\n", "                now_table = pd.concat([now_table, out_df])\n", "            table_id += 1\n", "        child_i += 1\n", "    \n", "    os.remove(temp_fn)\n", "    return out_markdown"]}, {"cell_type": "code", "execution_count": 8, "id": "984967e5", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:42.838347Z", "start_time": "2025-06-03T01:54:42.826771Z"}, "code_folding": [0]}, "outputs": [], "source": ["def is_scanned_pdf(pdf_path, threshold=0.9):\n", "    \"\"\"\n", "    判断PDF是否为扫描件\n", "    :param pdf_path: PDF文件路径\n", "    :param threshold: 判断为扫描件的阈值（页面中可提取文本的比例）\n", "    :return: True（扫描件）或 False（可转换文本）\n", "    \"\"\"\n", "    try:\n", "        # 方法1：检查PDF中是否包含可提取文本\n", "        with open(pdf_path, 'rb') as file:\n", "            reader = PyPDF2.PdfReader(file)\n", "            total_pages = len(reader.pages)\n", "            text_pages = 0\n", "            \n", "            for page in reader.pages:\n", "                text = page.extract_text()\n", "                if text and len(text.strip()) > 10:  # 如果有可读文本\n", "                    text_pages += 1\n", "            \n", "            text_ratio = text_pages / total_pages\n", "            \n", "            # 如果大部分页面都有可提取文本，则认为是可转换PDF\n", "            if text_ratio > threshold:\n", "                return False\n", "    except:\n", "        traceback.print_exc()"]}, {"cell_type": "code", "execution_count": 11, "id": "1f33976f", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:43.163043Z", "start_time": "2025-06-03T01:54:43.156272Z"}}, "outputs": [], "source": ["test_path = \"app/费率优惠公告（公开文件）\"\n", "pdf_fns = [x for x in os.listdir(test_path) if x.lower().endswith('pdf')]"]}, {"cell_type": "code", "execution_count": 12, "id": "392be698", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:54:43.673719Z", "start_time": "2025-06-03T01:54:43.668715Z"}}, "outputs": [], "source": ["pdf_fn = \"关于部分产品固定管理费和销售服务费优惠的公告2025053002.pdf_1750058152134.pdf\""]}, {"cell_type": "code", "execution_count": 13, "id": "c358a9f0", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T01:58:59.525342Z", "start_time": "2025-06-03T01:58:26.532586Z"}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/4 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert app/费率优惠公告（公开文件）/关于宁银理财宁欣日日薪固定收益类日开理财20号（最短持有14天）产品固定管理费优惠的公告.pdf_1750057997942.pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/2) Page 1\n", "[INFO] (2/2) Page 2\n", "[INFO] Terminated in 2.43s.\n", " 25%|██▌       | 1/4 [00:03<00:09,  3.31s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert app/费率优惠公告（公开文件）/关于宁银理财宁欣日日薪固定收益类日开理财50号（最短持有60天）产品部分份额固定管理费及销售服务费优惠的公告.pdf_1750057415010.pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/1) Page 1\n", "[INFO] Terminated in 0.99s.\n", " 50%|█████     | 2/4 [00:04<00:04,  2.07s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert app/费率优惠公告（公开文件）/关于宁银理财部分产品费率优惠的公告-24032902.pdf_1750057875172.pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/3) Page 1\n", "[INFO] (2/3) Page 2\n", "[INFO] (3/3) Page 3\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/3) Page 1\n", "[INFO] (2/3) Page 2\n", "[INFO] (3/3) Page 3\n", "[INFO] Terminated in 4.42s.\n", " 75%|███████▌  | 3/4 [00:09<00:03,  3.53s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n", "宁银理财有限责任公司\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[INFO] Start to convert app/费率优惠公告（公开文件）/关于部分产品固定管理费和销售服务费优惠的公告2025053002.pdf_1750058152134.pdf\n", "[INFO] \u001b[1;36m[1/4] Opening document...\u001b[0m\n", "[INFO] \u001b[1;36m[2/4] Analyzing document...\u001b[0m\n", "[INFO] \u001b[1;36m[3/4] Parsing pages...\u001b[0m\n", "[INFO] (1/5) Page 1\n", "[INFO] (2/5) Page 2\n", "[INFO] (3/5) Page 3\n", "[INFO] (4/5) Page 4\n", "[INFO] (5/5) Page 5\n", "[INFO] \u001b[1;36m[4/4] Creating pages...\u001b[0m\n", "[INFO] (1/5) Page 1\n", "[INFO] (2/5) Page 2\n", "[INFO] (3/5) Page 3\n", "[INFO] (4/5) Page 4\n", "[INFO] (5/5) Page 5\n", "[INFO] Terminated in 8.98s.\n", "100%|██████████| 4/4 [00:19<00:00,  4.88s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["注*：优惠截止日当日仍然享受优惠后的费率。\n", "重置表格\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["for pdf_fn in tqdm.tqdm(pdf_fns):\n", "    full_path = f\"{test_path}/{pdf_fn}\"\n", "    if is_scanned_pdf(full_path):\n", "        raise\n", "    else:\n", "        input_file_path = f\"{test_path}/{pdf_fn}\"\n", "        markdown_data = pdf_to_markdown(input_file_path)\n", "        out_fn = pdf_fn.split('.')[0]\n", "        with open(f'nb_fee/{out_fn}.md', 'w', encoding='utf-8') as f:\n", "            f.write(markdown_data)"]}, {"cell_type": "code", "execution_count": null, "id": "665536f1", "metadata": {"ExecuteTime": {"end_time": "2025-05-28T02:00:15.923968Z", "start_time": "2025-05-28T02:00:15.918408Z"}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7ff278d0", "metadata": {"ExecuteTime": {"end_time": "2025-05-23T08:39:40.958305Z", "start_time": "2025-05-23T08:39:40.907785Z"}, "scrolled": true}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ce4acfeb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}
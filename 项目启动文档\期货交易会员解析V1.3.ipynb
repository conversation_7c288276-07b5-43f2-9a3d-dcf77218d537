{"cells": [{"cell_type": "code", "execution_count": 1, "id": "23c3afbe", "metadata": {"ExecuteTime": {"end_time": "2025-06-03T09:28:34.379529Z", "start_time": "2025-06-03T09:28:33.335395Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\nb_remoteMineruAPI_v1.7\\nb_bank_ai_poc\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from util_ai import ChatBot\n", "from mineru_pdf import *"]}, {"cell_type": "code", "execution_count": 2, "id": "dbddd4b5", "metadata": {}, "outputs": [], "source": ["chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一名期货开户文件解析专家。请严格区分“会员号”（固定 4 位数字）和“交易编码”（固定 8 位数字），按照下列规则从用户提供的期货开户/备案文件中提取信息，并用 JSON 返回。\n", "\n", "=====================\n", "【必须提取的字段】\n", "1. 产品名称：资管计划或产品的正式名称\n", "2. 资金账号：资金账户号码\n", "3. 会员号（每个交易所各 1 个，仅 4 位数字；未出现填“/”）\n", "4. 交易编码（每个交易所按账户类型细分，均 8 位数字；用途缺失时默认为“投机”）\n", "5. 开始时间：写明的开始日期或者函件落款日，有时也被称为开户日期（YYYY-MM-DD；缺失填“/”）\n", "6. 结束时间：文件内表明的截止日期，取不到则为\"/\"（YYYY-MM-DD；缺失填“/”）\n", "\n", "=====================\n", "【交易所名称映射】\n", "- 上期所＝上海期货交易所／上海交易所\n", "- 大商所＝大连商品交易所／大连交易所\n", "- 郑商所＝郑州商品交易所／郑州交易所\n", "- 中金所＝中国金融期货交易所／金融交易所\n", "- 上能所＝上海能源交易所／能源中心\n", "- 广期所＝广州期货交易所／广州交易所\n", "\n", "=====================\n", "【账户用途映射】\n", "- 投机＝投机交易账户\n", "- 套利＝套利交易账户\n", "- 套保＝套期保值交易账户\n", "\n", "若文档未写明用途，默认“投机”。\n", "\n", "=====================\n", "【关键区别提醒】\n", "- 会员号：**始终 4 位数字**，表示交易所会员，仅 1 个，不含用途。\n", "- 交易编码：**始终 8 位数字**，表示具体交易账户，需要区分投机／套利／套保。\n", "- **禁止**将 8 位数字误作会员号，或将 4 位数字误作交易编码。可用数字长度自动判别。\n", "- 发现长度不符（5–7 位或 9 位等）则忽略该数字。\n", "\n", "=====================\n", "【输出 JSON 格式示例】\n", "```json\n", "{\n", "  \"产品名称\": \"金瑞同进尊享1号FOF单一资产管理计划\",\n", "  \"资金账号\": \"2120061\",\n", "  \"会员号\": {\n", "    \"上期所\": \"0121\",\n", "    \"大商所\": \"/\",\n", "    \"郑商所\": \"0059\",\n", "    \"中金所\": \"0170\",\n", "    \"上能所\": \"8059\",\n", "    \"广期所\": \"0021\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"81010373\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"99871700\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"00185013\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"上能所\": {\"投机\": \"81010376\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"04471686\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"2025-01-01\",\n", "  \"结束时间\": \"2025-01-15\"\n", "}\n", "```\n", "    \"\"\"\n", "    )"]}, {"cell_type": "code", "execution_count": 23, "id": "cc532feb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[DEBUG] 正在调用远程服务: http://192.168.10.237:30000/file_parse\n", "[DEBUG] 文件路径: app/开户回执档案（脱敏）/浙商期货-鹏远1号开户回执.jpg_1750042224248.jpg\n", "[DEBUG] 请求参数: {'return_md': 'true', 'return_images': 'true', 'return_middle_json': 'true', 'return_model_output': 'false', 'return_content_list': 'false', 'start_page_id': '0', 'end_page_id': '99999', 'parse_method': 'ocr', 'lang_list': 'ch', 'output_dir': './output', 'backend': 'vlm-sglang-engine', 'table_enable': 'true', 'formula_enable': 'true'}\n", "[DEBUG] 响应状态码: 200\n", "[DEBUG] 远程服务返回结果类型: <class 'dict'>\n"]}], "source": ["# 本代码不采用pdf转扫描式pdf的前置步骤\n", "fn = \"app/开户回执档案（脱敏）/浙商期货-鹏远1号开户回执.jpg_1750042224248.jpg\"\n", "markdown_data = PDFMarkdownConverter.trans_pdf_to_markdown(fn, parse_method='ocr', backend='vlm-sglang-engine')\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    continue"]}, {"cell_type": "code", "execution_count": 24, "id": "0c69cff4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["# 参与期货账户信息函\n", "\n", "合煦智远- 鹏远1号集合资产管理计划已完成期货开户手续\n", "\n", "具体信息如下:\n", "\n", "期货资金账户账号:8028323\n", "\n", "期货公司期货保证金账户:\n", "\n", "户名:浙商期货有限公司\n", "\n", "开户行:交通银行浙江省分行营业部\n", "\n", "账号:331066110010141097319\n", "\n", "<table><tr><td>期货商名称</td><td>会员编码</td><td>客户交易编码</td><td>账户类型（套保/套利/交易）</td></tr><tr><td>浙商期货有限公司</td><td>中金所：【0009】</td><td>00168939</td><td>套保</td></tr></table>\n", "\n", "统一开户编码:05833097\n", "\n", "本业务交易密码:111111\n", "\n", "本业务资金密码:111111\n", "\n", "本业务保证金监控中心查询账号:02108028323\n", "\n", "密码:5534zPTU\n", "\n", "![](images/f742e0170c4f161179cf356cb1050cfb744ef4eecc2b69fb3afa6af37a952033.jpg)\n"]}], "source": ["print(markdown_content)"]}, {"cell_type": "code", "execution_count": 25, "id": "77801cae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"产品名称\": \"合煦智远-鹏远1号集合资产管理计划\",\n", "  \"资金账号\": \"8028323\",\n", "  \"会员号\": {\n", "    \"上期所\": \"/\",\n", "    \"大商所\": \"/\",\n", "    \"郑商所\": \"/\",\n", "    \"中金所\": \"0009\",\n", "    \"上能所\": \"/\",\n", "    \"广期所\": \"/\"\n", "  },\n", "  \"交易编码\": {\n", "    \"上期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"大商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"郑商所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"中金所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"00168939\"},\n", "    \"上能所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"},\n", "    \"广期所\": {\"投机\": \"/\", \"套利\": \"/\", \"套保\": \"/\"}\n", "  },\n", "  \"开始时间\": \"/\",\n", "  \"结束时间\": \"/\"\n", "}\n", "```\n"]}], "source": ["response = chatbot.chat(markdown_content)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "id": "7bf3474d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "c718b718", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "23dcc490", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a4dd30e6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "be3f6955", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}
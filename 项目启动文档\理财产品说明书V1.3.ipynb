{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["d:\\nb_remoteMineruAPI_v1.7\\nb_bank_ai_poc\\.venv\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from mineru_pdf import *\n", "from util_ai import ChatBot"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[DEBUG] 正在调用远程服务: http://**************:30000/file_parse\n", "[DEBUG] 文件路径: app/理财产品说明书（脱敏）/ZK206108_07_宁银理财产品说明书合并版-直销.pdf_1750064823252.pdf\n", "[DEBUG] 请求参数: {'return_md': 'true', 'return_images': 'true', 'return_middle_json': 'true', 'return_model_output': 'false', 'return_content_list': 'false', 'start_page_id': '0', 'end_page_id': '99999', 'parse_method': 'auto', 'lang_list': 'ch', 'output_dir': './output', 'backend': 'pipeline', 'table_enable': 'true', 'formula_enable': 'true'}\n", "[DEBUG] 响应状态码: 200\n", "[DEBUG] 远程服务返回结果类型: <class 'dict'>\n"]}], "source": ["fn = \"app/理财产品说明书（脱敏）/ZK206108_07_宁银理财产品说明书合并版-直销.pdf_1750064823252.pdf\"\n", "\n", "markdown_data = PDFMarkdownConverter.trans_pdf_to_markdown(fn)\n", "markdown_content = \"\"\n", "for fn_name, data in markdown_data['results'].items():\n", "    markdown_content = data['md_content']\n", "    break"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["father_docs = markdown_content.split(\"\\n\\n\")\n", "son_docs = {}\n", "for father_doc in father_docs:\n", "    son_docs[father_doc] = father_doc.split(\"\\n\")"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["# 相关文档检索\n", "refer_docs = []\n", "keywords = [\"销售\", \"代销\", \"代理销售\"]\n", "keywords2 = [\"公司\", \"银行\"]\n", "for father_doc in father_docs:\n", "    is_match = False\n", "    for keyword in keywords:\n", "        if keyword in father_doc:\n", "            for keyword2 in keywords2:\n", "                if keyword2 in father_doc:\n", "                    refer_docs.append(father_doc)\n", "                    is_match = True\n", "                    break\n", "        if is_match:\n", "            break"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["refer_doc_text = \"\\n\".join(refer_docs)"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["['<html><body><table><tr><td>产品代码</td><td>ZK206108</td></tr><tr><td>全国银行业信息 登记系统编码</td><td>Z7002120000114 凭此编码可在“中国理财网（www.chinawealth.com.cn）”上查询</td></tr><tr><td>产品名称</td><td>本产品信息 宁银理财宁欣固定收益类半年定期开放式理财8号（周年庆专属）</td></tr><tr><td>产品类型</td><td>固定收益类、非保本浮动收益型</td></tr><tr><td>募集方式</td><td>公募</td></tr><tr><td>产品评级</td><td>PR2 本评级为产品管理人内部评级，仅供参考。该产品通过代理销售机 构渠道销售的，理财产品评级应当以代理销售机构最终披露的评级 结果为准。</td></tr><tr><td>目标投资者</td><td>风险评级为稳健型，平衡型，成长型，进取型的投资者 其中A份额（销售代码：ZK206108）面向宁波银行及部分其他渠道客 户，C份额（销售代码：ZK206108C）面向宁波银行白金卡（月日均 资产达到30万）个人客户、薪福宝客户、企业财资客户、企业APP渠 道专属客户及部分非宁波银行渠道客户，D份额（销售代码： ZK206108D）面向宁波银行钻石卡（月日均资产达到300万）个人客 户、新客户及部分非宁波银行渠道客户（具体以销售机构的规则为</td></tr><tr><td>本金及收益币种</td><td>准） 人民币 A份额：70%*中债新综合全价（1-3年）指数收益率（CBA00123.CS)</td></tr><tr><td>业绩比较基准</td><td>+30%*个人定期（整存整取）一年 C份额：70%*中债新综合全价（1-3年）指数收益率（CBA00123.CS） +30%*个人定期（整存整取）一年+0.05% D份额：70%*中债新综合全价（1-3年）指数收益率（CBA00123.CS） +30%*个人定期（整存整取）一年+0.1%</td></tr></table></body></html>',\n", " '<html><body><table><tr><td>额</td><td></td></tr><tr><td>追加赎回份额</td><td>0.01份（具体以销售机构的规则为准） 1、单个投资者对本产品单个开放日的申购金额上限为无上限；若单 个投资者在单个开放日对本产品的申购金额达到上限的，产品管理</td></tr><tr><td>单日单户申赎限 额</td><td>人有权不再继续接受其申购； 2、单个投资者对本产品单个开放日的赎回份额上限为无上限；若单 个投资者在单个开放日对本产品的赎回份额达到上限的，产品管理 人有权不再继续接受其赎回。 1、单个投资者对本产品持有金额上限为A、D份额为个人投资者无上</td></tr><tr><td>单户限额</td><td>限、机构投资者2亿元；C份额为个人投资者无上限，机构投资者 8000万元；若单个投资者对本产品持有金额达到上限的，产品管理 人有权不再继续接受其认（申）购；若单个投资者对本产品持有金 额达到上限以后仍有认 (申)购需求的，可咨询本产品销售机构各营 业网点或通过电话方式咨询其客服热线，产品管理人将审慎分析评 估其认 (申)购申请，当继续接受其认 (申)购可能对理财产品存量 投资者利益构成重大不利影响时，或者基于投资运作与风险控制需 要，产品管理人有权不再继续接受其认 (申)购。非因产品管理人主 观因素导致单一投资者持有份额比例超过总份额的50%（含）时，产 品管理人将暂停接受其认（申）购申请。 2、单个投资者对本产品每类份额的最低持有金额为个人投资者1元、</td></tr><tr><td></td><td>机构投资者10元，若投资者持有不足该金额时，产品管理人可对投 资者持有的产品剩余金额进行强制赎回。</td></tr><tr><td>销售区域 管理机构/产品管 理人</td><td>全国 (不含港澳台地区） 宁银理财有限责任公司</td></tr><tr><td>销售机构</td><td>宁波银行股份有限公司、兴业银行股份有限公司、东莞银行股份有 限公司、江苏银行股份有限公司、浙商银行股份有限公司、华侨银 行有限公司、中信百信银行股份有限公司、苏州银行股份有限公司、 九江银行股份有限公司、长沙银行股份有限公司、南洋商业银行 （中国）有限公司、江苏常熟农村商业银行股份有限公司、深圳农 村商业银行股份有限公司、宁银理财有限责任公司</td></tr><tr><td>托管机构 投资合作机构</td><td>宁波银行股份有限公司 投资合作机构包括但不限于理财产品所投资资产管理产品的发行机 构、根据合同约定从事理财产品受托投资的机构以及与理财产品投 资管理相关的投资顾问等。 本理财产品投资合作机构可能包括但不限于太平资产管理有限公司、</td></tr><tr><td></td><td>民生通惠资产管理有限公司、永赢资产管理有限公司、博时资本管 理有限公司、平安证券股份有限公司、中国国际金融股份有限公司、 五矿国际信托有限公司、中国对外经济贸易信托有限公司、创金合 信基金管理有限公司、光大证券资产管理有限公司、光大永明资产 管理股份有限公司、招商财富资产管理有限公司、华泰证券（上海） 资产管理有限公司等。 后续新增投资合作机构时，管理人将对合作机构的资质条件、专业 服务能力和风险管理水平等进行充分评价，切实履行投资管理职责。</td></tr></table></body></html>',\n", " '2、综合考虑本产品的投资组合、同类产品过往业绩和风险水平（包括但不限于拟投资市场和资产的风险评估等）等因素，本产品评级为PR2风险（本评级为公司内部评级，仅供参考，该产品通过代理销售机构渠道销售的，理财产品评级应当以代理销售机构最终披露的评级结果为准）。',\n", " '1、安全托管理财资金及其所投资的资产或保管代表资产的权益凭证或财产清单；2、为本产品开设独立的托管账户，不同托管账户中的资产应当相互独立；3、按照托管协议约定和产品管理人的投资指令，及时办理清算、交割事宜；4、记录理财资金划拨情况，保存产品管理人的资金用途说明；5、负责本产品财产的会计核算；6、建立与产品管理人的对账机制，核对本产品财产交易记录、资金和财产账目、资产  \\n净值等数据7、监督本产品投资运作；8、办理与本产品托管业务活动相关的信息披露事项；9、将本产品托管业务活动的记录、账册、报表和其他相关资料保存15年以上；10、对本产品投资信息和相关资料承担保密责任，除法律、行政法规、规章规定、审  \\n计要求或者合同约定外，不得向任何机构或者个人提供相关信息和资料；11、每季度向产品管理人出具本产品的托管业务报告；12、法律、法规、国务院银行业监督管理机构规定的其他职责。  \\n二）销售机构基本信息  \\n1、机构名称：兴业银行股份有限公司客服热线：95561官方网站：www.cib.com.cn  \\n2、机构名称：东莞银行股份有限公司客服热线：956033官方网站：www.dongguanbank.cn  \\n3、机构名称：宁波银行股份有限公司客服热线：95574官方网站：www.nbcb.com.cn  \\n4、机构名称：江苏银行股份有限公司客服热线：95319官方网站：www.jsbchina.cn  \\n5、机构名称：华侨银行有限公司客服热线：40089-40089官方网站：www.ocbc.com.cn  \\n6、机构名称：中信百信银行股份有限公司客服热线：956186官方网站：www.aibank.com  \\n7、机构名称：苏州银行股份有限公司客服热线：96067官方网站：www.suzhoubank.com  \\n8、机构名称：浙商银行股份有限公司客服热线：95527官方网站：www.czbank.com  \\n9、机构名称：长沙银行股份有限公司客服热线：0731-96511官方网站：www.cscb.cn  \\n10、机构名称：九江银行股份有限公司客服热线：95316官方网站：www.jjccb.com  \\n11、机构名称：江苏常熟农村商业银行股份有限公司客服热线：956020官方网站：www.csrcbank.com  \\n12、机构名称：南洋商业银行（中国）有限公司客服热线：95327官方网站：www.ncbchina.cn  \\n13、机构名称：深圳农村商业银行股份有限公司客服热线：**********官方网站：www.**********.com  \\n14、机构名称：宁银理财有限责任公司客服热线：400-099-5574官方网站：www.wmbnb.com  \\n销售机构主要职责：',\n", " '1、严格遵循国家法律、法规及其他有关规定和理财产品销售文件的规定，办理本产品销售业务，勤勉、尽责地履行销售机构的职责；2、应保证在履行理财产品销售文件过程中占有、控制的理财产品资产或理财产品份额持有人的资产与销售机构自身资产相互独立，分别建账，分别管理；3、对投资者身份信息的真实性进行验证；4、对非机构投资者严谨客观实施风险承受能力评估并持续跟进，根据投资者的风险承受能力销售不同风险等级的产品，把合适的理财产品销售给合适的投资者；5、向投资者提供持续信息服务，包括但不限于以下方面:及时向投资者告知申赎理财产品的确认日期、确认份额和金额等信息；定期向投资者提供其所持有的理财产品基本信息；及时向投资者告知对其决策有重大影响的信息；6、充分了解面向特定对象销售的理财产品的投资者信息，收集、核验投资者金融资产证明、收入证明或纳税凭证等材料；7、根据反洗钱、反恐怖融资及非居民金融账户涉税信息尽职调查等相关法律法规要求识别客户身份；8、按照法律法规规定、理财产品销售文件约定办理理财产品的认（申）购、赎回，归集、划转理财产品销售结算资金，确保理财产品销售结算资金安全、及时划付。通过投资者指定账户办理理财产品认（申）购和赎回的款项收付，并将赎回、分红及认（申）购不成功的相应款项划入投资者指定账户，制作、留存款项收付的有效凭证；9、完整记录和保存销售业务活动信息，确保记录信息全面、准确和不可篡改；10、根据有关法律法规、监管规定和理财产品销售文件的要求，及时、充分披露本产品的相关信息；11、建立健全档案管理制度，妥善保管投资者理财产品销售相关资料，保管年限不得低于法规规定的年限；12、法律、法规、国务院银行业监督管理机构规定、理财产品销售文件约定的其他职责。',\n", " '6、如甲方在本协议项下投资的理财产品为《理财产品说明书》声明的面向合格投资者发行的理财产品，甲方承诺其符合监管要求的合格投资者条件。7、甲方保证熟悉理财产品类型特征及不同销售渠道的相关规定，了解理财产品直销与代销的相关区别。对于甲方通过乙方或代理销售机构的网上银行、手机银行等销售渠道方式购买的理财产品（不受限于本产品），甲方确认其在乙方或代理销售机构渠道系统点击确认的理财产品销售文件的合法有效性，与纸质签署具有同等效力，并确认相关记录构成对甲方操作行为（包括但不限于购买、赎回、撤单等业务）的证据，并且在双方发生争议时可以作为合法有效的证据使用。',\n", " '11、甲方承诺在产品存续期内，除非按照《理财产品说明书》约定行权终止理财产品，否则不得要求乙方在产品到期日或开放日前退还已扣划款项或以任何形式清算其持有的理财产品份额，且不得将甲方指定账户销户。代理销售机构即为甲方指定账户开户银行的，代理销售机构有权拒绝甲方的销户请求并及时告知乙方。',\n", " '4、乙方有权按照《理财产品说明书》的约定确定收益分配方案、向甲方分配收益。  \\n5、乙方有权按照《理财产品说明书》的约定披露理财产品相关信息。相关信息披露内容构成理财产品销售文件不可分割的一部分，与理财产品销售文件具有同等效力，甲方应在充分知晓、理解有关信息披露内容后签署本协议，并在签署本协议后通过约定的信息披露渠道持续关注理财产品信息披露内容。6、基于乙方履行理财产品销售文件所载明的各项义务、进行服务管理和风险管理、报送监管信息、履行反洗钱、反恐怖融资、非居民金融账户涉税信息尽职调查、投资者资质审核和销售适当性等法律法规和监管要求的义务的需要，甲方授权乙方在业务办理或履行过程中直接或通过代理销售机构间接获取并报送、处理甲方个人信息，处理方式为收集、存储、使用、加工、传输、提供等，个人信息包括姓名、性别、国籍或地区、职业、联系方式、地址、身份证件信息等身份信息，理财账户、银行账户等账户信息，风险承受能力等级、理财产品交易时间等金融交易信息，持有理财产品信息等财产信息，及法律法规、监管要求或行业自律准则规定的其他信息；在乙方履行相关法律法规、监管规定的义务及有权机关要求的情况下，甲方授权乙方向银行业理财登记托管中心、',\n", " '监管机构及其他有权机关报送或提供上述信息；在乙方办理本协议项下相关事项所必需的情形下，甲方授权乙方向乙方集团成员、服务机构及其他乙方认为必要的业务合作机构（包括但不限于代理销售机构等）提供乙方获取的上述甲方信息，授权使用时限与提供前述服务及履行相关法律法规和监管要求义务的必要时限一致。接收信息的上述第三方将为处理本协议项下事务之目的接触并按照乙方的业务需要处理甲方信息，处理方式包括但不限于存储、使用、加工、传输、提供等。乙方承诺将向有关第三方明确其保护甲方信息的职责并要求第三方承担相应保密义务。乙方将妥善保管投资者理财产品销售相关资料，保管年限不低于《中华人民共和国反洗钱法》、《理财公司理财产品销售管理暂行办法》等法律法规及监管规定的年限；超出必要保管期限后，乙方、销售机构及其他第三方合作机构（如有）将及时删除投资者的档案（包括以电子化形式以及纸质文本形式保管的投资者个人信息）。如甲方对第三方机构处理甲方的个人信息活动有疑问或要求，甲方可以通过《理财产品说明书》、第三方机构的官方网站（如有）、第三方机构的官方公众号（如有）等渠道获取相应的联系方式以行使甲方的个人信息权利，或者直接联系乙方。',\n", " '2、甲方通过乙方或代理销售机构的网上银行、手机银行等线上渠道方式购买本理财产品的，本协议经甲方点击同意本协议且乙方确认甲方成功认（申）购并收到甲方缴付的全部投资本金款项之日起生效。双方认可线上点击同意具有与书面签署同等的法律效力。',\n", " '宁银理财将于理财产品募集期间、存续期间和终止时，通过本公司官网（www.wmbnb.com）、相关理财代理销售机构官方渠道或其他适当的方式进行信息披露（具体产品的信息披露方式、频率等规则见《理财产品说明书》）。宁银理财或代理销售机构通过理财产品销售文件约定方式披露信息后，视为本公司已向您完全履行信息披露义务，您可依据理财产品销售文件的约定，及时登录本公司/代理销售机构网站或致电本公司/代理销售机构客户服务热线（宁银理财：400-099-5574；代理销售机构联系方式详见《理财产品说明书》）查询。如因您未及时查询，或因通讯、系统故障或其他不可抗力等因素的影响，致使您无法及时了解理财产品信息，所产生的责任和风险将由您自行承担。',\n", " '您预留在公司或代理销售机构的有效联系方式若发生变更，应及时通知公司或代理销售机构，如因您未及时告知，导致公司或代理销售机构无法及时联系您，由此产生的责任和风险将由您自行承担。',\n", " '对于在本公司直销渠道购买的产品，产品评级以本公司的评级结果为准。对于在代理销售机构购买的产品，代理销售机构应当独立、审慎地对本理财产品进行销售评级，代理销售机构对产品的评级与本公司产品评级结果不一致的，代理销售机构应当采用对应较高风险等级的评级结果并予以披露，理财产品评级应当以代理销售机构最终披露的评级结果为准。',\n", " '本产品为公募固定收益类开放式理财，收益类型为非保本浮动收益,投资期限为无固定期限,本公司对本产品的产品风险评级为PR2,适合被本公司评为稳健型，平衡型，成长型，进取型的投资者购买。该产品通过代理销售机构购买的，产品风险评级以代理销售机构最终披露的评级结果为准，投资者的风险承受能力等级以代理销售机构的评估结果为准。',\n", " '投资者可通过宁银理财或代理销售机构的营业网点、客服电话、电子渠道等途径了解本公司理财产品购买方式、收费标准及方式等，通过《理财产品说明书》了解产品要素。',\n", " '宁银理财将于理财产品募集期间、存续期间和终止时，通过本公司网站（www.wmbnb.com）、相关产品代理销售机构官方渠道或其他适当的方式进行信息披露（具体产品的信息披露方式、频率等规则见《理财产品说明书》）。宁银理财或代理销售机构通过理财产品销售文件约定方式披露信息后，视为宁银理财已向投资者完全履行信息披露义务，投资者应依据理财产品销售文件的约定，及时通过宁银理财网站、客服热线或相关产品代理销售机构官方渠道进行查询。',\n", " '基于宁银理财履行理财产品销售文件所载明的各项义务、进行服务管理和风险管理、报送监管信息、履行反洗钱、反恐怖融资、非居民金融账户涉税信息尽职调查、投资者资质审核和销售适当性等法律法规和监管要求的义务的需要，您授权宁银理财在业务办理或履行过程中直接或通过代理销售机构间接获取并报送、处理投资者个人信息，处理方式为收集、存储、使用、加工、传输、提供等，个人信息包括姓名、性别、国籍或地区、职业、联系方式、地址、身份证件信息等身份信息，理财账户、银行账户等账户信息，风险承受能力等级、理财产品交易时间等金融交易信息，持有理财产品信息等财产信息，及法律法规、监管要求或行业自律准则规定的其他信息；在宁银理财履行相关法律法规、监管规定的义务及有权机关要求的情况下，授权宁银理财向银行业理财登记托管中心、监管机构及其他有权机关报送或提供上述信息；在宁银理财办理本协议项下相关事项所必需的情形下，授权宁银理财向宁银理财集团成员、服务机构及其他宁银理财认为必要的业务合作机构（包括但不限于代理销售机构等）提供宁银理财获取的上述投资者信息，授权使用时限与提供前述服务及履行相关法律法规和监管要求义务的必要时限一致。接收信息的上述第三方将为处理本协议项下事务之目的接触并按照宁银理财的业务需要处理投资者信息，处理方式包括但不限于存储、使用、加工、传输、提供等。宁银理财承诺将向有关第三方明确其保护投资者信息的职责并要求第三方承担相应保密义务。宁银理财将妥善保管投资者理财产品销售相关资料，保管年限不低于《中华人民共和国反洗钱法》、《理财公司理财产品销售管理暂行办法》等法律法规及监管规定的年限；超出必要保管期限后，宁银理财、销售机构及其他第三方合作机构（如有）将及时删除投资者的档案（包括以电子化形式以及纸质文本形式保管的投资者个人信息）。如您对第三方机构处理您的个人信息活动有疑问或要求，您可以通过《理财产品说明书》、第三方机构的官方网站（如有）、第三方机构的官方公众号（如有）等渠道获取相应的联系方式以行使您的个人信息权利，或者直接联系我们。',\n", " '宁银理财有限责任公司（以下简称“乙方”）与其发行理财产品的购买方（以下简称“甲方”）经平等友好协商，本着自愿、诚实信用的原则，就乙方向甲方销售理财产品及提供理财服务相关事宜，达成如下协议（以下简称“本协议”）：',\n", " '甲方与乙方约定，本协议及本理财产品的《理财产品说明书》、《风险揭示书》、《投资协议书》、《投资者权益须知》等理财产品销售文件中所涉以下名词均具有以下含义：（一）理财计划/理财产品/产品：指宁银理财宁欣固定收益类半年定期开放式理财8号（周年庆专属）产品。（二）投资者指定账户/甲方指定账户：指甲方开立的用于本产品资金划转的银行账户，资金划转行为包括但不限于认（申）购时的本金扣划，赎回/到期时的本金（如有）及收益（如有）划付等。（三）募集期：是指乙方确定的可接受投资者对于理财产品认购申请的时间区间。（四）工作日：详见《理财产品说明书》。（五）产品成立日：指理财产品的投资运作起始日。（六）开放日：乙方发行的开放式理财产品成立后，可供投资者进行申购、赎回等交易的日期。（七）申购开放日：乙方发行的开放式理财产品成立后，可供投资者进行申购交易的日期。（八）认购：甲方在募集期向乙方申请购买理财产品份额的行为。（九）申购：甲方在开放期向乙方申请购买理财产品份额的行为。（十）赎回：甲方在开放期向乙方申请赎回理财产品份额的行为。（十一）到期日：指理财产品的投资运作终止日。（十二）资金清算日：理财产品的本金（若有）和收益（若有）划向甲方指定账户的日期。（十三）单位净值：指理财产品份额的单位净值，即每一份理财计划份额以人民币或其他币种计价的价格。']"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["refer_docs"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["```json\n", "{\n", "  \"销售机构\": [\n", "    \"宁波银行股份有限公司\",\n", "    \"兴业银行股份有限公司\",\n", "    \"东莞银行股份有限公司\",\n", "    \"江苏银行股份有限公司\",\n", "    \"华侨银行有限公司\",\n", "    \"中信百信银行股份有限公司\",\n", "    \"苏州银行股份有限公司\",\n", "    \"浙商银行股份有限公司\",\n", "    \"长沙银行股份有限公司\",\n", "    \"九江银行股份有限公司\",\n", "    \"江苏常熟农村商业银行股份有限公司\",\n", "    \"南洋商业银行（中国）有限公司\",\n", "    \"深圳农村商业银行股份有限公司\",\n", "    \"宁银理财有限责任公司\"\n", "  ]\n", "}\n", "```\n"]}], "source": ["chatbot = ChatBot(\n", "    model='qwen3-32b',\n", "    system_prompt=\"\"\"你是一个理财产品说明书解析专家，请根据用户提供的理财说明书的部分内容，从中提取出销售（代销）机构信息。\n", "    注意：请勿捏造数据，请根据实际情况输出。\n", "    请注意，输出格式必须为json格式，不要输出其他内容。\n", "\n", "    # 示例输出(仅供参考，请根据实际情况输出)\n", "    ```json\n", "    {\n", "      \"销售机构\": [\n", "        \"XX银行股份有限公司\",\n", "        \"XX银行股份有限公司\n", "      ]\n", "    }\n", "    ```\n", "    \"\"\"\n", "    )\n", "response = chatbot.chat(refer_doc_text)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 2}